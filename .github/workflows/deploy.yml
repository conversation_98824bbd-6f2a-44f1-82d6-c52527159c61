name: Deploy to IIS

on:
  push:
    branches:
      - main # Ana branch'a push işlemi olduğunda tetiklenecek.

  workflow_dispatch:

jobs:
  deploy:
    runs-on: windows-latest

    steps:
      - name: Check out the repository
        uses: actions/checkout@v2

      - name: Set up .NET 8 SDK
        uses: actions/setup-dotnet@v3
        with:
          dotnet-version: "8.0.x"

      - name: Publish the .NET project
        run: dotnet publish --configuration Release --output ./publish
        env:
          CI: false

      - name: Deploy to IIS using Web Deploy
        run: |
          "C:\Program Files (x86)\IIS\Microsoft Web Deploy V3\msdeploy.exe" -source:contentPath=./publish -dest:auto,computerName="https://$env:IIS_SERVER:8172/msdeploy.axd?site=$env:IIS_SITE_NAME",userName="$env:IIS_USERNAME",password="$env:IIS_PASSWORD",authType="Basic" -allowUntrusted
        env:
          IIS_USERNAME: ${{ secrets.IIS_USERNAME }}
          IIS_PASSWORD: ${{ secrets.IIS_PASSWORD }}
          IIS_SERVER: ${{ secrets.IIS_SERVER }}
          IIS_SITE_NAME: ${{ secrets.IIS_SITE_NAME }}
