{"scripts": {"watch": "cd src/Presentation/Api && dotnet watch run", "run": "cd src/Presentation/Api && dotnet run", "build": "cd src/Presentation/Api && dotnet build", "build-client": "cd src/Presentation/Client && npm run build", "clean": "find . -name 'bin' -o -name 'obj' -o -name 'node_modules' -type d -exec rm -rf {} +", "db-update": "cd src/Infrastructure/Database && dotnet ef database update --startup-project ../../Presentation/Api/Api.csproj --context ApplicationDbContext", "db-migrations-add": "cd src/Infrastructure/Database && dotnet ef migrations add --startup-project ../../Presentation/Api/Api.csproj --context ApplicationDbContext", "publish-mac": "cd src/Presentation/Api && dotnet publish Api.csproj -c Release /p:DebugType=None -o ~/Desktop/Publish-Tokgoz"}}