### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}/api/v1/Dashboard
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{$dotenv domain}}/api/v1/Account/login
Content-Type: application/json

{
  "Email": "{{email}}",
  "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

### GetTotalProduct
GET {{baseUrl}}/TotalProduct
Authorization: Bearer {{token}}

### GetTotalRequest
GET {{baseUrl}}/TotalRequest
Authorization: Bearer {{token}}

### GetDailyTotalRequest
GET {{baseUrl}}/DailyTotalRequest
Authorization: Bearer {{token}}

### GetTotalContract
GET {{baseUrl}}/TotalContract
Authorization: Bearer {{token}}

### GetTotalCompanyRequest
GET {{baseUrl}}/TotalCompanyRequest
Authorization: Bear<PERSON> {{token}}

### GetTotalStationRequest
GET {{baseUrl}}/TotalStationRequest
Authorization: Bearer {{token}}