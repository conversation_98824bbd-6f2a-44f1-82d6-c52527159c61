### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}/api/v1/Reports
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{$dotenv domain}}/api/v1/Account/login
Content-Type: application/json

{
  "Email": "{{email}}",
  "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

# Get Vehicle Report
GET {{baseUrl}}/VehicleReport
Accept: application/json
Authorization: Bearer {{token}}

###

# Get Request Report
GET {{baseUrl}}/RequestReport
Accept: application/json
Authorization: Bearer {{token}}

###

# Get Delay Request Report
GET {{baseUrl}}/DelayRequest
Accept: application/json
Authorization: Bearer {{token}}

###

# Get Daily Vehicle Report
GET {{baseUrl}}/DailyVehicleReport
Accept: application/json
Authorization: Bearer {{token}}

###

# Get Total Time Vehicle Report
GET {{baseUrl}}/TotalTimeVehicleReport
Accept: application/json
Authorization: Bearer {{token}}

###

# Get Daily Manager Report
GET {{baseUrl}}/DailyManagerReport
Accept: application/json
Authorization: Bearer {{token}}

###

# Get Daily Customer Report
GET {{baseUrl}}/DailyCustomerReport
Accept: application/json
Authorization: Bearer {{token}}

###

# Get Return Report
GET {{baseUrl}}/ReturnReport
Accept: application/json
Authorization: Bearer {{token}}