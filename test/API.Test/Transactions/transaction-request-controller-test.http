### <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
@baseUrl = {{$dotenv domain}}/api/v1/TransactionRequest
@email = {{$dotenv email}}
@password = {{$dotenv password}}

###

# @name login
POST {{$dotenv domain}}/api/v1/Account/login
Content-Type: application/json

{
  "Email": "{{email}}",
  "Password": "{{password}}"
}

###

@token = {{login.response.body.AccessToken}}

### GetTransactionStatus
GET {{baseUrl}}/TransactionStatus
Authorization: Bearer {{token}}

### GetConcreteLocation
GET {{baseUrl}}/ConcreteLocation
Authorization: Bearer {{token}}

### GetConcreteOption
GET {{baseUrl}}/ConcreteOption
Authorization: Bearer {{token}}

### GetConsistencyClass
GET {{baseUrl}}/ConsistencyClass
Authorization: Bearer {{token}}

### Filter
GET {{baseUrl}}/List
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "filter": {
    "PageNumber": 1,
    "PageSize": 10,
    "SortProperty": "Id",
    "SortType": "asc",
  }
}

### Get
GET {{baseUrl}}/Find/{Id}
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "Id": "guid",
  "IncludeProperties": ["property1", "property2"]
}

### Post
POST {{baseUrl}}/
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "Id": "guid",
  "TransactionDate": "2022-01-01T00:00:00",
  "TransactionType": "Credit",
  "Amount": 100.00,
  "Currency": "TRY",
  "Description": "Test transaction"
}

### Put
PUT {{baseUrl}}/
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "Id": "guid",
  "TransactionDate": "2022-01-01T00:00:00",
  "TransactionType": "Credit",
  "Amount": 100.00,
  "Currency": "TRY",
  "Description": "Test transaction"
}

### Patch
PATCH {{baseUrl}}/
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "Id": "guid",
  "TransactionDate": "2022-01-01T00:00:00",
  "TransactionType": "Credit",
  "Amount": 100.00,
  "Currency": "TRY",
  "Description": "Test transaction"
}

### Delete
DELETE {{baseUrl}}/
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "Id": "guid"
}

### DeleteList
DELETE {{baseUrl}}/List
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "Id": "guid1"
  },
  {
    "Id": "guid2"
  }
]

### PatchList
PATCH {{baseUrl}}/PatchList
Content-Type: application/json
Authorization: Bearer {{token}}

[
  {
    "Id": "guid1",
    "TransactionDate": "2022-01-01T00:00:00",
    "TransactionType": "Credit",
    "Amount": 100.00,
    "Currency": "TRY",
    "Description": "Test transaction"
  },
  {
    "Id": "guid2",
    "TransactionDate": "2022-01-01T00:00:00",
    "TransactionType": "Credit",
    "Amount": 100.00,
    "Currency": "TRY",
    "Description": "Test transaction"
  }
]