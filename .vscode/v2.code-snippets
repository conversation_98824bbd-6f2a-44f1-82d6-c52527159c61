{
    "Controller": {
        "prefix": "mzk-controller",
        "body": [
            "using Microsoft.AspNetCore.Authorization;",
            "using Microsoft.AspNetCore.Mvc;",
            "using Api.Shared;",
            "using Application.Shared.Wrappers;",
            "using Application.${1:Module}.Services;",
            "using Application.${1:Module}.DTOs;",
            "using Application.${1:Module}.FilterModel;",
            "",
            "namespace Api.Controllers.${1:Module};",
            "",
            "[Route(\"api/v1/${2:Entity}\")]",
            "[ApiExplorerSettings(GroupName = \"apiv1\")]",
            "[ApiController]",
            "[Authorize]",
            "public class ${2:Entity}Controller(I${2:Entity}Service service)",
            "    : BaseApiController<I${2:Entity}Service, ${2:Entity}DTO>(service)",
            "{",
            "    // [HttpGet(\"Filter\")]",
            "    // public virtual async Task<PagedResponse<${2:Entity}DTO>> Filter([FromQuery] ${2:Entity}FilterModel filter)",
            "    // {",
            "    //     return await _service.FilterAsync(filter);",
            "    // }",
            "}",
        ],
        "description": "Controller Snippet"
    },
    "Service Interface": {
        "prefix": "mzk-service-interface",
        "body": [
            "using Application.Shared.Services;",
            "using Application.Shared.Wrappers;",
            "using Application.${1:Module}.DTOs;",
            "using Application.${1:Module}.FilterModel;",
            "",
            "namespace Application.${1:Module}.Services;",
            "",
            "public interface I${2:Entity}Service : IBaseService<${2:Entity}DTO>",
            "{",
            "    Task<PagedResponse<${2:Entity}DTO>> FilterAsync(${2:Entity}FilterModel filter);",
            "}"
        ],
        "description": "Service Interface Snippet"
    },
    "Service": {
        "prefix": "mzk-service",
        "body": [
            "using AutoMapper;",
            "using Microsoft.EntityFrameworkCore;",
            "using Domain.Shared;",
            "using Application.Shared.Extensions;",
            "using Application.Shared.Services;",
            "using Application.Shared.Wrappers;",
            "using Domain.${1:Module};",
            "using Application.${1:Module}.DTOs;",
            "using Application.${1:Module}.FilterModel;",
            "",
            "namespace Application.${1:Module}.Services;",
            "",
            "public class ${2:Entity}Service(",
            "    IMapper mapper,",
            "    IGenericQueryRepositoryAsync<${2:Entity}> queryRepository,",
            "    IGenericCommandRepositoryAsync<${2:Entity}> commandRepository",
            ") : BaseService<${2:Entity}, ${2:Entity}DTO>(mapper, queryRepository, commandRepository), I${2:Entity}Service",
            "{",
            "",
            "    public async Task<PagedResponse<${2:Entity}DTO>> FilterAsync(${2:Entity}FilterModel filter)",
            "    {",
            "        var allData = _queryRepository.AsQueryable();",
            "        var filteredData = allData;",
            "        // if (!string.IsNullOrWhiteSpace(filter.Name))",
            "        // {",
            "        //     filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));",
            "        // }",
            "        filter.Count = allData.Count();",
            "        filter.FilteredCount = filteredData.Count();",
            "        var list = await filteredData",
            "            .Sorting(filter.SortProperty, filter.SortType)",
            "            .Paging(filter.PageIndex, filter.PageSize)",
            "            .Including(BaseIncludes, filter.IncludeProperties)",
            "            .Select(filter.SelectFields)",
            "            .ToListAsync()",
            "            .ConfigureAwait(false);",
            "        var data = _mapper.Map<List<${2:Entity}DTO>>(list);",
            "        return data.ToPagedResponse(filter);",
            "    }",
            "}"
        ],
        "description": "Service Snippet"
    },
    "MediatR query": {
        "prefix": "mzkquery",
        "body": [
            "using MediatR;",
            "using FluentValidation;",
            "using Application.Shared.Wrappers;",
            "using Domain.Shared;",
            "using Application.Shared.Extensions;",
            "",
            "namespace ${TM_DIRECTORY/(.*[\\\\\\/](repos|src|test|tests|desktop|projects)[\\\\\\/])|(^([a-z]:)?[\\\\\\/])|([\\\\\\/])|([^\\w.])/${5:+.}${6:+_}/gi};",
            "",
            "public record ${TM_FILENAME_BASE}(",
            "    Guid Id",
            ") : IRequest<Response<${2:Entity}DTO>>;",
            "",
            "public class ${TM_FILENAME_BASE}Handler(",
            "    IGenericQueryRepositoryAsync<${2:Entity}> repository) ",
            "    : IRequestHandler<${TM_FILENAME_BASE}, Response<${2:Entity}DTO>>",
            "{",
            "    private readonly IGenericQueryRepositoryAsync<${2:Entity}> _repository = repository;",
            "",
            "    public async Task<Response<${2:Entity}DTO>> Handle(${TM_FILENAME_BASE} request, CancellationToken cancellationToken)",
            "    {",
            "        $0",
            "        return new ${2:Entity}DTO { }.ToResponse();",
            "    }",
            "}",
            "",
            "public class ${TM_FILENAME_BASE}Validator : AbstractValidator<${TM_FILENAME_BASE}>",
            "{",
            "    public ${TM_FILENAME_BASE}Validator()",
            "    {",
            "        RuleFor(x => x.Id).NotEmpty();",
            "    }",
            "}"
        ],
        "description": "MediatR query"
    },
    "MediatR command": {
        "prefix": "mzkcommand",
        "body": [
            "using System;",
            "using System.Threading;",
            "using System.Threading.Tasks;",
            "using MediatR;",
            "using Microsoft.Extensions.Localization;",
            "using FluentValidation;",
            "using AutoMapper;",
            "",
            "namespace ${TM_DIRECTORY/(.*[\\\\\\/](repos|src|test|tests|desktop|projects)[\\\\\\/])|(^([a-z]:)?[\\\\\\/])|([\\\\\\/])|([^\\w.])/${5:+.}${6:+_}/gi};",
            "",
            "public record ${TM_FILENAME_BASE}(",
            "    Guid Id",
            ") : IRequest<ServiceResponse>;",
            "",
            "public class ${TM_FILENAME_BASE}Handler : IRequestHandler<${TM_FILENAME_BASE}, ServiceResponse>",
            "{",
            "    private readonly IStringLocalizer<${TM_FILENAME_BASE}Handler> _localizer;",
            "    private readonly IMapper _mapper;",
            "    private readonly AppSettings _settings;",
            "    private readonly IUnitOfWork _uow;",
            "    private readonly IRepository<${2:Entity}> _repository;",
            "",
            "    public ${TM_FILENAME_BASE}Handler(",
            "        IStringLocalizer<${TM_FILENAME_BASE}Handler> localizer,",
            "        AppSettings settings,",
            "        IMapper mapper,",
            "        IUnitOfWork uow,",
            "        IRepository<${2:Entity}> repository)",
            "    {",
            "        _localizer = localizer;",
            "        _mapper = mapper;",
            "        _settings = settings;",
            "        _uow = uow;",
            "        _repository = repository;",
            "    }",
            "",
            "    public async Task<ServiceResponse> Handle(${TM_FILENAME_BASE} request, CancellationToken cancellationToken)",
            "    {",
            "        $0",
            "        return true.ToResponse();",
            "    }",
            "}",
            "",
            "public class ${TM_FILENAME_BASE}Validator : AbstractValidator<${TM_FILENAME_BASE}>",
            "{",
            "    public ${TM_FILENAME_BASE}Validator()",
            "    {",
            "        RuleFor(x => x.Id).NotEmpty();",
            "    }",
            "}"
        ],
        "description": "MediatR command"
    },
    "MediatR publish": {
        "prefix": "mzkcommand",
        "body": [
            "using System;",
            "using System.Threading;",
            "using System.Threading.Tasks;",
            "using MediatR;",
            "using Microsoft.Extensions.Localization;",
            "using FluentValidation;",
            "using AutoMapper;",
            "",
            "namespace ${TM_DIRECTORY/(.*[\\\\\\/](repos|src|test|tests|desktop|projects)[\\\\\\/])|(^([a-z]:)?[\\\\\\/])|([\\\\\\/])|([^\\w.])/${5:+.}${6:+_}/gi};",
            "",
            "public record ${TM_FILENAME_BASE}(",
            "    Guid Id",
            ") : IRequest<ServiceResponse>;",
            "",
            "public class ${TM_FILENAME_BASE}Handler : IRequestHandler<${TM_FILENAME_BASE}, ServiceResponse>",
            "{",
            "    private readonly IStringLocalizer<${TM_FILENAME_BASE}Handler> _localizer;",
            "    private readonly IMapper _mapper;",
            "    private readonly AppSettings _settings;",
            "    private readonly IUnitOfWork _uow;",
            "    private readonly IRepository<${2:Entity}> _repository;",
            "",
            "    public ${TM_FILENAME_BASE}Handler(",
            "        IStringLocalizer<${TM_FILENAME_BASE}Handler> localizer,",
            "        AppSettings settings,",
            "        IMapper mapper,",
            "        IUnitOfWork uow,",
            "        IRepository<${2:Entity}> repository)",
            "    {",
            "        _localizer = localizer;",
            "        _mapper = mapper;",
            "        _settings = settings;",
            "        _uow = uow;",
            "        _repository = repository;",
            "    }",
            "",
            "    public async Task<ServiceResponse> Handle(${TM_FILENAME_BASE} request, CancellationToken cancellationToken)",
            "    {",
            "        $0",
            "        return true.ToResponse();",
            "    }",
            "}",
            "",
            "public class ${TM_FILENAME_BASE}Validator : AbstractValidator<${TM_FILENAME_BASE}>",
            "{",
            "    public ${TM_FILENAME_BASE}Validator()",
            "    {",
            "        RuleFor(x => x.Id).NotEmpty();",
            "    }",
            "}"
        ],
        "description": "MediatR command"
    },
    "Endpoints Full": {
        "prefix": "mzkendpointfull",
        "body": [
            "using MediatR;",
            "using Microsoft.AspNetCore.Builder;",
            "using Microsoft.AspNetCore.Http;",
            "using Microsoft.AspNetCore.Routing;",
            "using System.Collections.Generic;",
            "using System;",
            "using Microsoft.AspNetCore.Mvc;",
            "",
            "namespace Api.Endpoints;",
            "",
            "public static class ${2:Entity}Endpoints",
            "{",
            "    public static void Map${2:Entity}Endpoints(this IEndpointRouteBuilder app)",
            "    {",
            "        var group = app.MapGroup(\"panelapi/v2/${2:Entity}\")",
            "            .WithTags(\"${2:Entity}\")",
            "            .WithGroupName(\"panelapiv2\")",
            "            .RequireAuthorization(\"Bearer\");$0",
            "",
            "        group.MapGet(\"{Id}\", async (Guid Id, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(new Get${2:Entity}Query(Id))).ToResult();",
            "        });",
            "",
            "        group.MapGet(\"Filter\", async ([AsParameters] ${2:Entity}FilterQuery ${2/(.*)/${1:/downcase}/}FilterQuery, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(${2/(.*)/${1:/downcase}/}FilterQuery)).ToResult();",
            "        });",
            "",
            "        group.MapPost(\"\", async (Add${2:Entity}Command add${2:Entity}Command, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(add${2:Entity}Command)).ToResult();",
            "        });",
            "",
            "        group.MapPost(\"List\", async ([FromBody] List<${2:Entity}DTO> ${2/(.*)/${1:/downcase}/}ListDTO, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(new Add${2:Entity}ListCommand(${2/(.*)/${1:/downcase}/}ListDTO))).ToResult();",
            "        });",
            "",
            "        group.MapPut(\"{Id}\", async (Edit${2:Entity}Command edit${2:Entity}Command, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(edit${2:Entity}Command)).ToResult();",
            "        });",
            "",
            "        group.MapPut(\"List\", async ([FromBody] List<${2:Entity}DTO> ${2/(.*)/${1:/downcase}/}ListDTO, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(new Edit${2:Entity}ListCommand(${2/(.*)/${1:/downcase}/}ListDTO))).ToResult();",
            "        });",
            "",
            "        group.MapPatch(\"\", async (PatchDTO patchDTO, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(new Patch${2:Entity}Command(patchDTO))).ToResult();",
            "        });",
            "",
            "        group.MapPatch(\"List\", async (List<PatchDTO> patchListDTO, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(new Patch${2:Entity}ListCommand(patchListDTO))).ToResult();",
            "        });",
            "",
            "        group.MapDelete(\"{Id}\", async (Guid Id, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(new Delete${2:Entity}Command(Id))).ToResult();",
            "        });",
            "",
            "        group.MapDelete(\"List\", async ([FromBody] List<Guid> ${2/(.*)/${1:/downcase}/}ListDTO, IMediator mediator) =>",
            "        {",
            "            return (await mediator.Send(new Delete${2:Entity}ListCommand(${2/(.*)/${1:/downcase}/}ListDTO))).ToResult();",
            "        });",
            "    }",
            "}"
        ],
        "description": "Endpoints Full"
    },
    "Endpoints Empty": {
        "prefix": "mzkendpointempty",
        "body": [
            "using MediatR;",
            "using Microsoft.AspNetCore.Builder;",
            "using Microsoft.AspNetCore.Http;",
            "using Microsoft.AspNetCore.Routing;",
            "using System.Collections.Generic;",
            "using System;",
            "",
            "namespace Api.Endpoints.${1:Module};",
            "",
            "public class ${2:Entity}Endpoints : IEndpoint",
            "{",
            "    public void MapEndpoints(IEndpointRouteBuilder app)",
            "    {",
            "        var group = app.MapGroup(\"panelapi/v2/${2:Entity}\")",
            "            .WithTags(\"${2:Entity}\")",
            "            .WithGroupName(\"panelapiv2\")",
            "            .RequireAuthorization(\"Bearer\");$0",
            "",
            "    }",
            "}"
        ],
        "description": "Endpoints Empty"
    },
    "MapGet": {
        "prefix": "mzkmapget",
        "body": [
            "group.MapGet(\"$1\", async ([AsParameters] $2, IMediator mediator) =>",
            "{$0",
            "    return (await mediator.Send($3)).ToResult();$0",
            "});"
        ],
        "description": "MapGet"
    },
    "MapPost": {
        "prefix": "mzkmappost",
        "body": [
            "group.MapPost(\"$1\", async ($2, IMediator mediator) =>",
            "{$0",
            "    return (await mediator.Send($3)).ToResult();",
            "});"
        ],
        "description": "MapPost"
    },
    "MapPut": {
        "prefix": "mzkmapput",
        "body": [
            "group.MapPut(\"$1\", async($2, IMediator mediator) =>",
            "{$0",
            "    return (await mediator.Send($3)).ToResult();",
            "});"
        ],
        "description": "MapPut"
    },
    "MapPatch": {
        "prefix": "mzkmappatch",
        "body": [
            "group.MapPatch(\"$1\", async (PatchDTO patchDTO, IMediator mediator) =>",
            "{",
            "    return (await mediator.Send($2)).ToResult();",
            "});"
        ],
        "description": "MapPatch"
    },
    "MapDelete": {
        "prefix": "mzkmapdelete",
        "body": [
            "group.MapDelete(\"$1\", async($2, IMediator mediator) =>",
            "{",
            "    return (await mediator.Send($3)).ToResult();",
            "});"
        ],
        "description": "MapDelete"
    }
}