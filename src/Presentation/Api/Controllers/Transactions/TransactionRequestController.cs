using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Transactions.Services;
using Application.Transactions.DTOs;
using Application.Transactions.FilterModel;
using Domain.Shared;

namespace Api.Controllers.Transactions;

[Route("api/v1/TransactionRequest")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class TransactionRequestController(ITransactionRequestService service)
    : BaseController
{
    protected readonly ITransactionRequestService _service = service;

    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<TransactionRequestDTO>> Filter([FromQuery] TransactionRequestFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("TransactionStatus")]
    public virtual async Task<List<TransactionStatusDTO>> GetTransactionStatus()
    {
        return await _service.GetTransactionStatusAsync();
    }

    [HttpGet("ConcreteLocation")]
    public virtual async Task<List<ConcreteLocationDTO>> GetConcreteLocation()
    {
        return await _service.GetConcreteLocationAsync();
    }

    [HttpGet("ConcreteOption")]
    public virtual async Task<List<ConcreteOptionDTO>> GetConcreteOption()
    {
        return await _service.GetConcreteOptionAsync();
    }

    [HttpGet("ConsistencyClass")]
    public virtual async Task<List<ConsistencyClassDTO>> GetConsistencyClass()
    {
        return await _service.GetConsistencyClassAsync();
    }

    [HttpGet("List")]
    public virtual async Task<PagedResponse<TransactionRequestDTO>> Filter([FromQuery] BaseFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("{Id}")]
    public virtual async Task<Response<TransactionRequestDTO>> Get(Guid Id)
    {
        return await _service
            .FindAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpGet("Find/{Id}")]
    public virtual async Task<Response<TransactionRequestDTO>> Get(Guid Id, [FromQuery] string[] IncludeProperties)
    {
        return await _service
            .FindAsync(Id, IncludeProperties)
            .ConfigureAwait(false);
    }

    [HttpPost]
    public virtual async Task<Response<TransactionRequestDTO>> Post([FromBody] TransactionRequestDTO PostData)
    {
        return await _service.InsertAsync(PostData);
    }

    [HttpPut]
    public virtual async Task<Response<TransactionRequestDTO>> Put([FromBody] TransactionRequestDTO PostData)
    {
        return await _service.UpdateAsync(PostData);
    }

    [HttpPut("Approve/{Id}")]
    public virtual async Task<Response<string>> Approve(Guid Id, [FromQuery] string? Note)
    {
        return await _service.Approve(Id, Note);
    }

    [HttpPut("Reject/{Id}/{Note}")]
    public virtual async Task Reject(Guid Id, string Note)
    {
        await _service.Reject(Id, Note);
    }

    [HttpPut("Cancel/{Id}/{Note}")]
    public virtual async Task Cancel(Guid Id, string Note)
    {
        await _service.Cancel(Id, Note);
    }

    [HttpPatch]
    public virtual async Task Patch([FromBody] PatchDTO PatchVM)
    {
        await _service.PatchAsync(PatchVM)
            .ConfigureAwait(false);
    }

    [HttpPatch("PatchList")]
    public virtual async Task PatchList([FromBody] List<PatchDTO> PatchListVM)
    {
        await _service.PatchListAsync(PatchListVM)
            .ConfigureAwait(false);
    }

    [HttpDelete("{Id}")]
    public virtual async Task Delete([FromRoute] Guid Id)
    {
        await _service.DeleteAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpDelete]
    public virtual async Task Delete(TransactionRequestDTO entityVM)
    {
        await _service.DeleteAsync(entityVM)
            .ConfigureAwait(false);
    }

    [HttpDelete("DeleteList")]
    public virtual async Task DeleteList(List<Guid> Ids)
    {
        await _service.DeleteListAsync(Ids)
            .ConfigureAwait(false);
    }

    [HttpDelete("List")]
    public virtual async Task DeleteList(List<TransactionRequestDTO> DeleteDataList)
    {
        await _service.DeleteListAsync(DeleteDataList)
            .ConfigureAwait(false);
    }

    [HttpGet("TransactionRequestType")]
    public virtual async Task<List<TransactionRequestTypeDTO>> GetTransactionRequestType()
    {
        return await _service.GetTransactionRequestTypeAsync()
            .ConfigureAwait(false);
    }

    [HttpPost("TransferOrderByErp")]
    [AllowAnonymous]
    public virtual async Task<Response<string>> TransferOrderByErp()
    {
        return await _service.TransferOrderByErpAsync();
    }
}