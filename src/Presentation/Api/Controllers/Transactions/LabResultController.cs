using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Transactions.Services;
using Application.Transactions.DTOs;
using Application.Transactions.FilterModel;

namespace Api.Controllers.Transactions;

[Route("api/v1/LabResult")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class LabResultController(ILabResultService service)
    : BaseController
{
    protected readonly ILabResultService _service = service;

    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<LabResultDTO>> Filter([FromQuery] LabResultFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("{Id}", Order = 1)]
    public virtual async Task<Response<LabResultDTO>> Get(Guid Id)
    {
        return await _service
            .FindAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpPost(Order = 1)]
    public virtual async Task<Response<LabResultDTO>> Post([FromForm] LabResultDTO PostData)
    {
        return await _service.InsertAsync(PostData);
    }

    [HttpPost("AddList", Order = 1)]
    public virtual async Task<PagedResponse<LabResultDTO>> AddList([FromForm] List<LabResultDTO> PostDataList)
    {
        return await _service.InsertListAysnc(PostDataList);
    }

    [HttpDelete("{Id}", Order = 1)]
    public virtual async Task Delete([FromRoute] Guid Id)
    {
        await _service.DeleteAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpDelete("DeleteList", Order = 1)]
    public virtual async Task DeleteList(List<Guid> Ids)
    {
        await _service.DeleteListAsync(Ids)
            .ConfigureAwait(false);
    }
}