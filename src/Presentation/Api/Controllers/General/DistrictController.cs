using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.General.Services;
using Application.General.DTOs;
using Application.General.FilterModel;

namespace Api.Controllers.General;

[Route("api/v1/District")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class DistrictController(IDistrictService service)
    : BaseApiController<IDistrictService, DistrictDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<DistrictDTO>> Filter([FromQuery] DistrictFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}