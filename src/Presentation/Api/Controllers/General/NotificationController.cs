using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.General.Services;
using Application.General.DTOs;
using Application.General.FilterModel;

namespace Api.Controllers.General;

[Route("api/v1/Notification")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class NotificationController(INotificationService service) : BaseController
{
    protected readonly INotificationService _service = service;

    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<NotificationDTO>> Filter([FromQuery] NotificationFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("AllRead")]
    public async Task AllRead()
    {
        await _service.SetAllReadAsync();
    }

    [HttpGet("Find/{Id}", Order = 1)]
    public virtual async Task<Response<NotificationDTO>> Get(Guid Id, [FromQuery] string[] IncludeProperties)
    {
        return await _service
            .FindAsync(Id, IncludeProperties)
            .ConfigureAwait(false);
    }

    [HttpPost]
    public virtual async Task<Response<NotificationDTO>> Post([FromBody] NotificationDTO PostData)
    {
        return await _service.InsertAsync(PostData);
    }

    [HttpPost("BulkSend")]
    public virtual async Task<Response<BulkSendNotificationResultDTO>> BulkSend([FromBody] BulkSendNotificationDTO PostData)
    {
        return await _service.BulkSendAsync(PostData);
    }

    [HttpPut]
    public virtual async Task<Response<NotificationDTO>> Put([FromBody] NotificationDTO PostData)
    {
        return await _service.UpdateAsync(PostData);
    }

    [HttpDelete("{Id}")]
    public virtual async Task Delete([FromRoute] Guid Id)
    {
        await _service.DeleteAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpDelete]
    public virtual async Task Delete(NotificationDTO entityVM)
    {
        await _service.DeleteAsync(entityVM)
            .ConfigureAwait(false);
    }

    [HttpDelete("DeleteList")]
    public virtual async Task DeleteList(List<Guid> Ids)
    {
        await _service.DeleteListAsync(Ids)
            .ConfigureAwait(false);
    }

    [HttpDelete("List")]
    public virtual async Task DeleteList(List<NotificationDTO> DeleteDataList)
    {
        await _service.DeleteListAsync(DeleteDataList)
            .ConfigureAwait(false);
    }
}