using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.General.Services;
using Application.General.DTOs;
using Application.General.FilterModel;
using Domain.Shared;

namespace Api.Controllers.General;

[Route("api/v1/Gallery")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class GalleryController(IGalleryService service)
    : BaseController
{
    protected readonly IGalleryService _service = service;

    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<GalleryDTO>> Filter([FromQuery] GalleryFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("{Id}")]
    public virtual async Task<Response<GalleryDTO>> Get(Guid Id)
    {
        return await _service
            .FindAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpGet("Find/{Id}")]
    public virtual async Task<Response<GalleryDTO>> Get(Guid Id, [FromQuery] string[] IncludeProperties)
    {
        return await _service
            .FindAsync(Id, IncludeProperties)
            .ConfigureAwait(false);
    }

    [HttpPost]
    public virtual async Task<Response<GalleryDTO>> Post([FromForm] GalleryDTO PostData)
    {
        return await _service.InsertAsync(PostData);
    }

    [HttpPut]
    public virtual async Task<Response<GalleryDTO>> Put([FromForm] GalleryDTO PostData)
    {
        return await _service.UpdateAsync(PostData);
    }

    [HttpPatch]
    public virtual async Task Patch([FromBody] PatchDTO PatchVM)
    {
        await _service.PatchAsync(PatchVM)
            .ConfigureAwait(false);
    }

    [HttpPatch("PatchList")]
    public virtual async Task PatchList([FromBody] List<PatchDTO> PatchListVM)
    {
        await _service.PatchListAsync(PatchListVM)
            .ConfigureAwait(false);
    }

    [HttpDelete("{Id}")]
    public virtual async Task Delete([FromRoute] Guid Id)
    {
        await _service.DeleteAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpDelete]
    public virtual async Task Delete(GalleryDTO entityVM)
    {
        await _service.DeleteAsync(entityVM)
            .ConfigureAwait(false);
    }

    [HttpDelete("DeleteList")]
    public virtual async Task DeleteList(List<Guid> Ids)
    {
        await _service.DeleteListAsync(Ids)
            .ConfigureAwait(false);
    }

    [HttpDelete("List")]
    public virtual async Task DeleteList(List<GalleryDTO> DeleteDataList)
    {
        await _service.DeleteListAsync(DeleteDataList)
            .ConfigureAwait(false);
    }
}