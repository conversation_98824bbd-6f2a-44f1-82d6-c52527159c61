using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.General.Services;
using Application.General.DTOs;
using Application.General.FilterModel;

namespace Api.Controllers.General;

[Route("api/v1/NotAvailableDate")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class NotAvailableDateController(INotAvailableDateService service)
    : BaseApiController<INotAvailableDateService, NotAvailableDateDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<NotAvailableDateDTO>> Filter([FromQuery] NotAvailableDateFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}