using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.General.Services;
using Application.General.DTOs;
using Application.General.FilterModel;

namespace Api.Controllers.Comments;

[Route("api/v1/Comment")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class CommentController(ICommentService service)
    : BaseApiController<ICommentService, CommentDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public async Task<PagedResponse<CommentDTO>> Filter([FromQuery] CommentFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

}