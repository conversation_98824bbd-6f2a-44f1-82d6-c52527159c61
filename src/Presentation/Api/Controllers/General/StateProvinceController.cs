using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.General.Services;
using Application.General.DTOs;
using Application.General.FilterModel;

namespace Api.Controllers.General;

[Route("api/v1/StateProvince")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class StateProvinceController(IStateProvinceService service)
    : BaseApiController<IStateProvinceService, StateProvinceDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<StateProvinceDTO>> Filter([FromQuery] StateProvinceFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}