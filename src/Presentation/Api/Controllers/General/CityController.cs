using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.General.Services;
using Application.General.DTOs;
using Application.General.FilterModel;

namespace Api.Controllers.General;

[Route("api/v1/City")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class CityController(ICityService service)
    : BaseApiController<ICityService, CityDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<CityDTO>> Filter([FromQuery] CityFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}