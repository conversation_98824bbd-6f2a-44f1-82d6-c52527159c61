using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Companies.Services;
using Application.Companies.DTOs;
using Application.Companies.FilterModel;

namespace Api.Controllers.Companies;

[Route("api/v1/BuildingUser")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class BuildingUserController(IBuildingUserService service)
    : BaseApiController<IBuildingUserService, BuildingUserDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<BuildingUserDTO>> Filter([FromQuery] BuildingUserFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}