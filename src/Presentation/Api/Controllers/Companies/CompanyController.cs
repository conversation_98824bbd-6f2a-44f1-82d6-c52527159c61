using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Companies.Services;
using Application.Companies.DTOs;
using Application.Companies.FilterModel;

namespace Api.Controllers.Companies;

[Route("api/v1/Company")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class CompanyController(ICompanyService service)
    : BaseApiController<ICompanyService, CompanyDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<CompanyDTO>> Filter([FromQuery] CompanyFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}