using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Companies.Services;
using Application.Companies.DTOs;
using Application.Companies.FilterModel;

namespace Api.Controllers.Companies;

[Route("api/v1/Building")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class BuildingController(IBuildingService service)
    : BaseApiController<IBuildingService, BuildingDTO, Guid>(service)
{

    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<BuildingDTO>> Filter([FromQuery] BuildingFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}