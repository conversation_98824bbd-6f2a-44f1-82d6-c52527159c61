using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Account.Services;
using Application.Account.DTOs;
using Application.Account.FilterModel;

namespace Api.Controllers.Account;

[Route("api/v1/UserDepartment")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class UserDepartmentController(IUserDepartmentService service)
    : BaseApiController<IUserDepartmentService, UserDepartmentDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<UserDepartmentDTO>> Filter([FromQuery] UserDepartmentFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}