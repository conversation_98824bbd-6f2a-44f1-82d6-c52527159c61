using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Account.Services;
using Application.Account.DTOs;
using Application.Account.FilterModel;

namespace Api.Controllers.Account;

[Route("api/v1/User")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class UserController(IUserService service) : BaseController
{
    protected readonly IUserService _service = service;

    [HttpGet("Filter")]
    public async Task<PagedResponse<UserDTO>> Filter([FromQuery] UserFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("Role")]
    [AllowAnonymous]
    public async Task<List<RoleDTO>> GetRoleList()
    {
        return await _service.GetRoleListAsync();
    }

    [HttpGet("{Id}")]
    public async Task<Response<UserDTO>> Get(Guid Id)
    {
        return await _service
            .FindAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpGet("Find/{Id}")]
    public async Task<Response<UserDTO>> Get(Guid Id, [FromQuery] string[] IncludeProperties)
    {
        return await _service
            .FindAsync(Id, IncludeProperties)
            .ConfigureAwait(false);
    }

    [HttpPost]
    public async Task<Response<UserDTO>> Post([FromBody] RegisterDTO PostData)
    {
        return await _service.InsertAsync(PostData);
    }

    [HttpPost("AddList")]
    public async Task<PagedResponse<UserDTO>> AddList([FromBody] List<RegisterDTO> PostDataList)
    {
        return await _service.InsertListAysnc(PostDataList);
    }

    [HttpPut]
    public async Task<Response<UserDTO>> Put([FromBody] UserDTO PostData)
    {
        return await _service.UpdateAsync(PostData);
    }

    [HttpPut("{Id}/FcmDeviceId/{FcmDeviceId}")]
    public async Task Put(Guid Id, string FcmDeviceId)
    {
        await _service.UpdateFcmDeviceIdAsync(Id, FcmDeviceId);
    }

    [HttpPut("PutList")]
    public async Task PutList([FromBody] List<UserDTO> PostDataList)
    {
        await _service.UpdateListAsync(PostDataList);
    }

    [HttpPut("{UserId}/ChangePassword")]
    public async Task ChangePassword(Guid UserId, [FromBody] string Password)
    {
        await _service.ChangePasswordAsync(UserId, Password);
    }

    [HttpDelete("{Id}")]
    public async Task Delete(Guid Id)
    {
        await _service.DeleteAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpDelete("DeleteList")]
    public async Task DeleteList(List<Guid> Ids)
    {
        await _service.DeleteListAsync(Ids)
            .ConfigureAwait(false);
    }

    [HttpDelete("List")]
    public async Task DeleteList(List<UserDTO> DeleteDataList)
    {
        await _service.DeleteListAsync(DeleteDataList)
            .ConfigureAwait(false);
    }
}