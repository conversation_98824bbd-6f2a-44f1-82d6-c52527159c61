using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Contracts.Services;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;

namespace Api.Controllers.Contracts;

[Route("api/v1/ContractProductTransactionLog")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class ContractProductTransactionLogController(IContractProductTransactionLogService service)
    : BaseApiController<IContractProductTransactionLogService, ContractProductTransactionLogDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<ContractProductTransactionLogDTO>> Filter([FromQuery] ContractProductTransactionLogFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}