using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Contracts.Services;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;

namespace Api.Controllers.Contracts;

[Route("api/v1/ContractProductTransaction")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class ContractProductTransactionController(IContractProductTransactionService service)
    : BaseApiController<IContractProductTransactionService, ContractProductTransactionDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<ContractProductTransactionDTO>> Filter([FromQuery] ContractProductTransactionFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("ContractProductTransactionStatus")]
    public virtual async Task<List<ContractProductTransactionStatusDTO>> GetContractProductTransactionStatus()
    {
        return await _service.GetContractProductTransactionStatusAsync();
    }

    [HttpPost("InsertWithDocumentNo/{TransactionRequestId}/{DocumentNo}")]
    public virtual async Task<Response<ContractProductTransactionDTO>> InsertWithDocumentNo(Guid TransactionRequestId, string DocumentNo)
    {
        return await _service.InsertWithDocumentNoAsync(TransactionRequestId, DocumentNo);
    }
}