using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Contracts.Services;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;

namespace Api.Controllers.Contracts;

[Route("api/v1/ContractProduct")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class ContractProductController(IContractProductService service)
    : BaseApiController<IContractProductService, ContractProductDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<ContractProductDTO>> Filter([FromQuery] ContractProductFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}