using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Contracts.Services;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;

namespace Api.Controllers.Contracts;

[Route("api/v1/ContractFile")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class ContractFileController(IContractFileService service)
    : BaseController
{
    protected readonly IContractFileService _service = service;

    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<ContractFileDTO>> Filter([FromQuery] ContractFileFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("{Id}", Order = 1)]
    public virtual async Task<Response<ContractFileDTO>> Get(Guid Id)
    {
        return await _service
            .FindAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpPost(Order = 1)]
    public virtual async Task<Response<ContractFileDTO>> Post([FromForm] ContractFileDTO PostData)
    {
        return await _service.InsertAsync(PostData);
    }

    [HttpPost("AddList", Order = 1)]
    public virtual async Task<PagedResponse<ContractFileDTO>> AddList([FromForm] List<ContractFileDTO> PostDataList)
    {
        return await _service.InsertListAysnc(PostDataList);
    }

    [HttpDelete("{Id}", Order = 1)]
    public virtual async Task Delete([FromRoute] Guid Id)
    {
        await _service.DeleteAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpDelete("DeleteList", Order = 1)]
    public virtual async Task DeleteList(List<Guid> Ids)
    {
        await _service.DeleteListAsync(Ids)
            .ConfigureAwait(false);
    }
}