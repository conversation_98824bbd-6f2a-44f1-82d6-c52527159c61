using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Contracts.Services;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;

namespace Api.Controllers.Contracts;

[Route("api/v1/Contract")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class ContractController(IContractService service)
    : BaseApiController<IContractService, ContractDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public async Task<PagedResponse<ContractDTO>> Filter([FromQuery] ContractFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("PaymentPlan")]
    public async Task<List<PaymentPlanDTO>> GetPaymentPlan()
    {
        return await _service.GetPaymentPlanAsync()
            .ConfigureAwait(false);
    }

    [HttpPost("SendWithEmail/{ContractId}")]
    public async Task SendWithEmail(Guid ContractId)
    {
        await _service.SendWithEmail(ContractId)
            .ConfigureAwait(false);
    }

    [HttpPut("Approve/{ContractId}/{Note}")]
    public async Task Approve(Guid ContractId, string Note)
    {
        await _service.Approve(ContractId, Note)
            .ConfigureAwait(false);
    }

    [HttpPut("Reject/{ContractId}/{Note}")]
    public async Task Reject(Guid ContractId, string Note)
    {
        await _service.Reject(ContractId, Note)
            .ConfigureAwait(false);
    }

    [HttpGet("SendRemindMessage")]
    [AllowAnonymous]
    public async Task SendRemindMessage()
    {
        await _service.SendRemindMessageAsync()
            .ConfigureAwait(false);
    }


    [HttpPost("RequestByRetailUser")]
    public virtual async Task<Response<string>> RequestByRetailUser([FromBody] RequestByRetailUserDTO entityVM)
    {
        return await _service.RequestByRetailUserAsync(entityVM);
    }
}