using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Stations.Services;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;

namespace Api.Controllers.Stations;

[Route("api/v1/Department")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class DepartmentController(IDepartmentService service)
    : BaseApiController<IDepartmentService, DepartmentDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<DepartmentDTO>> Filter([FromQuery] DepartmentFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}