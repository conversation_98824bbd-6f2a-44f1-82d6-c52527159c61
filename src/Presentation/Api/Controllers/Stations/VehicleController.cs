using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Stations.Services;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;

namespace Api.Controllers.Stations;

[Route("api/v1/Vehicle")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class VehicleController(IVehicleService service)
    : BaseApiController<IVehicleService, VehicleDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public async Task<PagedResponse<VehicleDTO>> Filter([FromQuery] VehicleFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("VehicleStatus")]
    public async Task<List<VehicleStatusDTO>> GetVehicleStatusListAsync([FromQuery] VehicleFilterModel filter)
    {
        return await _service.GetVehicleStatusListAsync();
    }
}