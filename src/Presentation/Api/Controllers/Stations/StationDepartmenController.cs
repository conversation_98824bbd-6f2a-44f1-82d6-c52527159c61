using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Stations.Services;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;

namespace Api.Controllers.Stations;

[Route("api/v1/StationDepartment")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class StationDepartmentController(IStationDepartmentService service)
    : BaseApiController<IStationDepartmentService, StationDepartmentDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<StationDepartmentDTO>> Filter([FromQuery] StationDepartmentFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}