using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Stations.Services;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;

namespace Api.Controllers.Stations;

[Route("api/v1/Station")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class StationController(IStationService service)
    : BaseApiController<IStationService, StationDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<StationDTO>> Filter([FromQuery] StationFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("StationStatus")]
    public virtual async Task<List<StationStatusDTO>> GetStationStatusListAsync([FromQuery] StationFilterModel filter)
    {
        return await _service.GetStationStatusListAsync();
    }
}