using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Stations.Services;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;

namespace Api.Controllers.Stations;

[Route("api/v1/PompType")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class PompTypeController(IPompTypeService service)
    : BaseApiController<IPompTypeService, PompTypeDTO, int>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<PompTypeDTO>> Filter([FromQuery] PompTypeFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}