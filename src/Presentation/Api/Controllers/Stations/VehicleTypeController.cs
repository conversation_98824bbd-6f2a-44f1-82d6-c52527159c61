using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Shared.Wrappers;
using Application.Stations.Services;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;

namespace Api.Controllers.Stations;

[Route("api/v1/VehicleType")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class VehicleTypeController(IVehicleTypeService service)
    : BaseApiController<IVehicleTypeService, VehicleTypeDTO, int>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<VehicleTypeDTO>> Filter([FromQuery] VehicleTypeFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}