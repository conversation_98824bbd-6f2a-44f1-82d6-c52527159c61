using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Api.Shared;
using Application.Catalog.Services;
using Application.Catalog.DTOs;
using Application.Catalog.FilterModel;
using Application.Shared.Wrappers;

namespace Api.Controllers.Catalog;

[Route("api/v1/Product")]
[ApiExplorerSettings(GroupName = "apiv1")]
[ApiController]
[Authorize]
public class ProductController(IProductService service) : BaseApiController<IProductService, ProductDTO, Guid>(service)
{
    [HttpGet("Filter")]
    public virtual async Task<PagedResponse<ProductDTO>> Filter([FromQuery] ProductFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }
}