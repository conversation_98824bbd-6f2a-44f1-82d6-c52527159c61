using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Shared;
using Microsoft.AspNetCore.Mvc;

namespace Api.Shared;

public class BaseApiController<TService, TDTO, TKey>(TService service) : BaseController
where TService : IBaseService<TDTO, TKey>
where TDTO : BaseDTO
{
    protected readonly TService _service = service;

    [HttpGet("List", Order = 1)]
    public virtual async Task<PagedResponse<TDTO>> Filter([FromQuery] BaseFilterModel filter)
    {
        return await _service.FilterAsync(filter);
    }

    [HttpGet("{Id}", Order = 1)]
    public virtual async Task<Response<TDTO>> Get(Guid Id)
    {
        return await _service
            .FindAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpGet("Find/{Id}", Order = 1)]
    public virtual async Task<Response<TDTO>> Get(Guid Id, [FromQuery] string[] IncludeProperties)
    {
        return await _service
            .FindAsync(Id, IncludeProperties)
            .ConfigureAwait(false);
    }

    [HttpPost(Order = 1)]
    public virtual async Task<Response<TDTO>> Post([FromBody] TDTO PostData)
    {
        return await _service.InsertAsync(PostData);
    }

    [HttpPost("AddList", Order = 1)]
    public virtual async Task<PagedResponse<TDTO>> AddList([FromBody] List<TDTO> PostDataList)
    {
        return await _service.InsertListAysnc(PostDataList);
    }

    [HttpPut(Order = 1)]
    public virtual async Task<Response<TDTO>> Put([FromBody] TDTO PostData)
    {
        return await _service.UpdateAsync(PostData);
    }

    [HttpPut("PutList", Order = 1)]
    public virtual async Task PutList([FromBody] List<TDTO> PostDataList)
    {
        await _service.UpdateListAsync(PostDataList);
    }

    [HttpPatch(Order = 1)]
    public virtual async Task Patch([FromBody] PatchDTO PatchVM)
    {
        await _service.PatchAsync(PatchVM)
            .ConfigureAwait(false);
    }

    [HttpPatch("PatchList", Order = 1)]
    public virtual async Task PatchList([FromBody] List<PatchDTO> PatchListVM)
    {
        await _service.PatchListAsync(PatchListVM)
            .ConfigureAwait(false);
    }

    [HttpDelete("{Id}", Order = 1)]
    public virtual async Task Delete([FromRoute] TKey Id)
    {
        await _service.DeleteAsync(Id)
            .ConfigureAwait(false);
    }

    [HttpDelete("", Order = 1)]
    public virtual async Task Delete(TDTO entityVM)
    {
        await _service.DeleteAsync(entityVM)
            .ConfigureAwait(false);
    }

    [HttpDelete("DeleteList", Order = 1)]
    public virtual async Task DeleteList(List<TKey> Ids)
    {
        await _service.DeleteListAsync(Ids)
            .ConfigureAwait(false);
    }

    [HttpDelete("List", Order = 1)]
    public virtual async Task DeleteList(List<TDTO> DeleteDataList)
    {
        await _service.DeleteListAsync(DeleteDataList)
            .ConfigureAwait(false);
    }
}