using Microsoft.AspNetCore.Mvc;
using MediatR;

namespace Api.Shared;

[ApiController]
public abstract class BaseController : ControllerBase
{
    protected IMediator Mediator => _mediator ??= HttpContext.RequestServices.GetService<IMediator>();
    private IMediator _mediator;
    protected ILogger Logger => _logger ??= HttpContext.RequestServices.GetService<ILogger>();
    private ILogger _logger;
}