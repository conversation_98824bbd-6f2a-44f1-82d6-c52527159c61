using System.Net;
using Application.Shared.Wrappers;
using Domain.Shared;
using Microsoft.AspNetCore.Mvc;

namespace Api.Middleware;

public class CustomExceptionMiddleware(RequestDelegate next)
{
    private readonly RequestDelegate next = next;

    public async Task Invoke(HttpContext context)
    {
        try
        {
            await next(context);
        }
        catch (AppException ex)
        {
            await HandleExceptionAsync(context, ex);
        }
        catch (Exception exceptionObj)
        {
            await HandleExceptionAsync(context, exceptionObj);
        }
    }

    private Task HandleExceptionAsync(HttpContext context, AppException exception)
    {
        const int statusCode = 400;
        var error = new BaseResponse()
        {
            Message = exception.Message,
            InternalMessage = exception.StackTrace,
            Validations = exception.Validations,
            ExceptionCode = exception.Code
        };
        context.Response.StatusCode = statusCode;
        return context.Response.WriteAsJsonAsync(error);
    }

    private Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var error = new ErrorResponse
        {
            Message = GetExceptionMessage(exception),
            StatusCode = (int)HttpStatusCode.InternalServerError,
            Type = exception.GetType().FullName,
        };
        var result = new JsonResult(error)
        {
            StatusCode = error.StatusCode
        };
        context.Response.StatusCode = error.StatusCode;
        return context.Response.WriteAsJsonAsync(result);
    }

    private static string GetExceptionMessage(Exception exception)
    {
        var message = exception.Message;
        if (exception.InnerException != null)
        {
            message += "\nInnerException: " + GetExceptionMessage(exception.InnerException);
        }
        return message;
    }
}

public static class ConfigureCustomExceptionMiddleware
{
    public static void ConfigureCustomException(this IApplicationBuilder app)
    {
        app.UseMiddleware<CustomExceptionMiddleware>();
    }
}


// internal sealed class GlobalExceptionHandler : IExceptionHandler
// {
//     public async ValueTask<bool> TryHandleAsync(
//         HttpContext httpContext,
//         Exception exception,
//         CancellationToken cancellationToken)
//     {
//         if (exception is AppException)
//         {
//             await HandleExceptionAsync(httpContext, (AppException)exception);
//         }
//         else
//         {
//             await HandleExceptionAsync(httpContext, exception);
//         }
//         return true;
//     }

//     private Task HandleExceptionAsync(HttpContext context, AppException exception)
//     {
//         const int statusCode = 400;
//         var error = new BaseResponse()
//         {
//             Message = exception.Message,
//             InternalMessage = exception.StackTrace,
//             Validations = exception.Validations,
//             ExceptionCode = exception.Code
//         };
//         var result = new JsonResult(error)
//         {
//             StatusCode = statusCode
//         };
//         return context.Response.WriteAsJsonAsync(result);
//     }

//     private Task HandleExceptionAsync(HttpContext context, Exception exception)
//     {
//         var error = new ErrorResponse
//         {
//             Message = GetExceptionMessage(exception),
//             StatusCode = (int)HttpStatusCode.InternalServerError,
//             Type = exception.GetType().FullName,
//         };
//         var result = new JsonResult(error)
//         {
//             StatusCode = error.StatusCode
//         };
//         return context.Response.WriteAsJsonAsync(result);
//     }

//     private static string GetExceptionMessage(Exception exception)
//     {
//         var message = exception.Message;
//         if (exception.InnerException != null)
//         {
//             message += "\nInnerException: " + GetExceptionMessage(exception.InnerException);
//         }
//         return message;
//     }
// }