using Application.Shared.Wrappers;

namespace Api.Extensions;

public static class EndpointExtensions
{
    public static IResult ToResult<T>(this Response<T> data)
    {
        if (data.Value == null)
        {
            return Results.NotFound(data);
        }
        if (data.Validations?.Count > 0)
        {
            return Results.BadRequest(data);
        }
        return Results.Ok(data);
    }
}