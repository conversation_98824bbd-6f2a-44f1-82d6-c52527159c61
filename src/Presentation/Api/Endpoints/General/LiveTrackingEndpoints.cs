using Application.General.Queries;
using MediatR;

namespace Api.Endpoints.General;

public class LiveTrackingEndpoints : IEndpoint
{
    public void MapEndpoints(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("api/v1/LiveTracking")
            .WithTags("LiveTracking")
            .WithGroupName("apiv1")
            .RequireAuthorization();

        group.MapGet("", async ([AsParameters] GetLiveTrackingQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

    }
}