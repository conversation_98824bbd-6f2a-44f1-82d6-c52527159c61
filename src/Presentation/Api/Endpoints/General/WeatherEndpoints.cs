using Application.General.Queries;
using MediatR;

namespace Api.Endpoints.General;

public class WeatherEndpoints : IEndpoint
{
    public void MapEndpoints(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("api/v1/Weather")
            .WithTags("Weather")
            .WithGroupName("apiv1")
            .RequireAuthorization();

        group.MapGet("", async ([AsParameters] GetWeatherQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

    }
}