using Application.Accounting.Queries;
using MediatR;

namespace Api.Endpoints.Accounting;

public class AccountingEndpoints : IEndpoint
{
    public void MapEndpoints(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("api/v1/Accounting")
            .WithTags("Accounting")
            .WithGroupName("apiv1")
            .RequireAuthorization();

        group.MapGet("Filter", async ([AsParameters] GetAccountingListQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("Detail/{CariRecNo}", async (Guid CariRecNo, IMediator mediator) =>
        {
            return await mediator.Send(new GetAccountingDetailQuery(CariRecNo));
        });

        group.MapGet("DocumentFilter", async ([AsParameters] GetDocumentListQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("Document/{IrsaliyeNo}", async (string IrsaliyeNo, IMediator mediator) =>
        {
            return await mediator.Send(new GetDocumentDetailQuery(IrsaliyeNo));
        });

        group.MapGet("DocumentCheck/{IrsaliyeNo}", async (string IrsaliyeNo, IMediator mediator) =>
        {
            return await mediator.Send(new GetDocumentCheckQuery(IrsaliyeNo));
        });
    }
}