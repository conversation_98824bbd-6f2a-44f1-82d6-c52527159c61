using MediatR;
using Domain.Account;
using Microsoft.AspNetCore.Mvc;
using Application.Account.DTOs;
using Application.Account;
using Api.Extensions;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.Data;
using System.Text;
using Microsoft.AspNetCore.WebUtilities;
using System.Text.Encodings.Web;
using Domain.External;
using Domain.Shared;
using Application.Shared.Data;
using Microsoft.EntityFrameworkCore;

namespace Api.Endpoints.Account;

public class EntityEndpoints : IEndpoint
{
    public void MapEndpoints(IEndpointRouteBuilder app)
    {
        app.MapGroup("api/v1/Account")
            .MapIdentityApi<User>()
            .WithTags("Account")
            .WithGroupName("apiv1")
            .AllowAnonymous();

        var group = app.MapGroup("api/v1/Account")
            .WithTags("Account")
            .WithGroupName("apiv1");

        // group.MapPost("/token", async (GetTokenQuery query, IMediator mediator) =>
        // {
        //     return (await mediator.Send(query)).ToResult();
        // });

        group.MapPost("/register2", async ([FromBody] RegisterDTO registration, IMediator mediator) =>
        {
            await mediator.Send(new RegisterCommand(registration));
        });

        group.MapPost("FastLogin", async (FastLoginCommand fastLoginCommand, IMediator mediator) =>
        {
            await mediator.Send(fastLoginCommand);
        }).AllowAnonymous();

        group.MapPost("FastLoginToken", async (FastLoginTokenCommand fastLoginTokenCommand, IMediator mediator) =>
        {
            await mediator.Send(fastLoginTokenCommand);
        }).AllowAnonymous();

        group.MapPost("PhoneConfirm", async (PhoneConfirmCommand fastLoginTokenCommand, IMediator mediator) =>
        {
            await mediator.Send(fastLoginTokenCommand);
        }).AllowAnonymous();

        group.MapGet("UserInfo", async ([AsParameters] GetUserInfoQuery query, IMediator mediator) =>
        {
            try
            {
                var result = await mediator.Send(query);
                if (result.Value == null)
                {
                    return Results.Unauthorized();
                }
                return Results.Ok(result);
            }
            catch (Exception)
            {
                return Results.Unauthorized();
            }
        }).RequireAuthorization();

        group.MapPost("/forgotPasswordcustom", async (
            [FromBody] ForgotPasswordRequest resetRequest,
            HttpContext context,
            IEmailManager emailManager,
            [FromServices] IServiceProvider sp) =>
        {
            var userManager = sp.GetRequiredService<UserManager<User>>();
            var user = await userManager.FindByEmailAsync(resetRequest.Email);
            if (user is not null && await userManager.IsEmailConfirmedAsync(user))
            {
                var code = await userManager.GeneratePasswordResetTokenAsync(user);
                code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                var resetCode = HtmlEncoder.Default.Encode(code);
                var baseLink = $"{context.Request.Scheme}://{context.Request.Host}";
                var resetLink = $"{baseLink}/account/resetpassword?Email={resetRequest.Email}&ResetCode={resetCode}";
                await emailManager.SendEmailAsync(resetRequest.Email, "Şifre Sıfırlama", $"Lütfen şifrenizi sıfırlamak için <a href='{resetLink}'>buraya</a> tıklayın.", null, true);
            }
            return TypedResults.Ok();
        });
    }
}