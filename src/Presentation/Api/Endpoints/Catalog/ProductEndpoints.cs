using MediatR;
using Api.Extensions;
using Application.Catalog.DTOs;
using Application.Catalog.Commands;
using Microsoft.AspNetCore.Mvc;
using Domain.Shared;

namespace Api.Endpoints.Catalog;

public class ProductEndpoints : IEndpoint
{
    public void MapEndpoints(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("api/v1/Product2")
            .WithTags("Product")
            .WithGroupName("apiv1")
            .RequireAuthorization();

        // group.MapGet("{Id}", async (Guid Id, IMediator mediator) =>
        // {
        //     return (await mediator.Send(new GetProductQuery(Id))).ToResult();
        // });

        // group.MapGet("Filter", async ([AsParameters] ProductFilterQuery productFilterQuery, IMediator mediator) =>
        // {
        //     return (await mediator.Send(productFilterQuery)).ToResult();
        // });

        group.MapPost("", async (ProductDTO product, IMediator mediator) =>
        {
            return (await mediator.Send(new AddProductCommand(product))).ToResult();
        });

        group.MapPost("List", async ([FromBody] List<ProductDTO> productListDTO, IMediator mediator) =>
        {
            return (await mediator.Send(new AddProductListCommand(productListDTO))).ToResult();
        });

        group.MapPut("{Id}", async (ProductDTO product, IMediator mediator) =>
        {
            return (await mediator.Send(new EditProductCommand(product))).ToResult();
        });

        group.MapPut("List", async ([FromBody] List<ProductDTO> productListDTO, IMediator mediator) =>
        {
            return (await mediator.Send(new EditProductListCommand(productListDTO))).ToResult();
        });

        group.MapDelete("{Id}", async (Guid Id, IMediator mediator) =>
        {
            await mediator.Send(new DeleteByIdProductCommand(Id));
        });

        group.MapDelete("ListById", async ([FromBody] List<Guid> productListDTO, IMediator mediator) =>
        {
            await mediator.Send(new DeleteByIdProductListCommand(productListDTO));
        });

        group.MapDelete("", async ([FromBody] ProductDTO product, IMediator mediator) =>
        {
            await mediator.Send(new DeleteProductCommand(product));
        });

        group.MapDelete("List", async ([FromBody] List<ProductDTO> productListDTO, IMediator mediator) =>
        {
            await mediator.Send(new DeleteProductListCommand(productListDTO));
        });

        group.MapPatch("", async (PatchDTO patchDTO, IMediator mediator) =>
        {
            await mediator.Send(new PatchProductCommand(patchDTO));
        });

        group.MapPatch("List", async (List<PatchDTO> patchListDTO, IMediator mediator) =>
        {
            await mediator.Send(new PatchProductListCommand(patchListDTO));
        });
    }
}