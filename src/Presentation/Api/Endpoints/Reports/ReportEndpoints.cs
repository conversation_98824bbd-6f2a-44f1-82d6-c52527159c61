using Application.Reports.Query;
using MediatR;

namespace Api.Endpoints.Reports;

public class ReportsEndpoints : IEndpoint
{
    public void MapEndpoints(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("api/v1/Reports")
            .WithTags("Reports")
            .WithGroupName("apiv1")
            .RequireAuthorization();

        group.MapGet("VehicleReport", async ([AsParameters] GetVehicleReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("RequestReport", async ([AsParameters] GetRequestReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("DelayRequest", async ([AsParameters] GetDelayRequestReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("DailyVehicleReport", async ([AsParameters] GetDailyVehicleReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("TotalTimeVehicleReport", async ([AsParameters] GetTotalTimeVehicleReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("DailyManagerReport", async ([AsParameters] GetDailyManagerReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("DailyCustomerReport", async ([AsParameters] GetDailyCustomerReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("ReturnReport", async ([AsParameters] GetReturnReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("StationReport", async ([AsParameters] GetStationReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("PriceGuaranteeReport", async ([AsParameters] GetPriceGuaranteeReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("BuildingTransactionReport", async ([AsParameters] GetBuildingTransactionReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("DetailCompanyShippingReport", async ([AsParameters] GetDetailCompanyShippingReportQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

    }
}