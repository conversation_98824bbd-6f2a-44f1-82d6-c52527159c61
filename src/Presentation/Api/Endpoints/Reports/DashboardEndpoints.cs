using MediatR;
using Application.Dashboard.Query;

namespace Api.Endpoints.Reports;

public class EntityEndpoints : IEndpoint
{
    public void MapEndpoints(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("api/v1/Dashboard")
            .WithTags("Dashboard")
            .WithGroupName("apiv1")
            .RequireAuthorization();

        group.MapGet("TotalProduct", async ([AsParameters] GetTotalProductQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("TotalRequest", async ([AsParameters] GetTotalRequestQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("DailyTotalRequest", async ([AsParameters] GetDailyTotalRequestQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("TotalContract", async ([AsParameters] GetTotalContractQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("TotalCompanyRequest", async ([AsParameters] GetTotalCompanyRequestQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

        group.MapGet("TotalStationRequest", async ([AsParameters] GetTotalStationRequestQuery query, IMediator mediator) =>
        {
            return await mediator.Send(query);
        });

    }
}