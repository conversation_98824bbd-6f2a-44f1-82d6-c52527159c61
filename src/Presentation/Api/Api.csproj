<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <SpaRoot>..\Client</SpaRoot>
    <SpaProxyLaunchCommand>npm run start</SpaProxyLaunchCommand>
    <SpaProxyServerUrl>http://localhost:3000</SpaProxyServerUrl>
    <InvariantGlobalization>false</InvariantGlobalization>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy" Version="8.0.10" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.10" />
    <PackageReference Include="Scalar.AspNetCore" Version="1.2.9" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.8.1" />
    <PackageReference Include="MiniProfiler.AspNetCore.Mvc" Version="4.3.8" />
    <PackageReference Include="MiniProfiler.EntityFrameworkCore" Version="4.3.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.10">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <Content Include="Static\**\*" CopyToPublishDirectory="Always" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Presentation\Client\Client.esproj">
      <ReferenceOutputAssembly>true</ReferenceOutputAssembly>
    </ProjectReference>
    <ProjectReference Include="..\..\Core\Domain\Domain.csproj" />
    <ProjectReference Include="..\..\Core\Application\Application.csproj" />
    <ProjectReference Include="..\..\Infrastructure\Database\Database.csproj" />
    <ProjectReference Include="..\..\Infrastructure\External\External.csproj" />
  </ItemGroup>

</Project>
