using System.Text.Json.Serialization;
using Microsoft.OpenApi.Models;
using Database;
using External;
using Application;
using Api.Endpoints;
using Domain.Account;
using Database.Context;
using Api.Middleware;
using Domain.Shared;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Swashbuckle.AspNetCore.SwaggerUI;
using Microsoft.AspNetCore.Identity;
using Scalar.AspNetCore;
using Microsoft.Extensions.FileProviders;
using Application.Shared;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddDatabaseInsfrastructure(builder.Configuration);
builder.Services.AddExternalInsfrastructure(builder.Configuration);
builder.Services.AddApplication();
builder.Services.AddSingleton(builder.Configuration.GetSection("AppSettings").Get<AppSettings>() ?? new AppSettings());
builder.Services.AddHostedService<IntegrationEventProcessorJob>();
builder.Services.AddSingleton<InMemoryMessageQueue>();
builder.Services.AddSingleton<IEventBus, InMemoryEventBus>();
builder.Services.AddMemoryCache();
builder.Services.AddAuthentication().AddBearerToken(IdentityConstants.BearerScheme, options =>
{
    options.BearerTokenExpiration = TimeSpan.FromDays(150);
});
builder.Services.AddAuthorizationBuilder();
builder.Services.AddIdentityCore<User>().AddEntityFrameworkStores<ApplicationDbContext>().AddApiEndpoints();
builder.Services.AddMiniProfiler(options => options.ColorScheme = StackExchange.Profiling.ColorScheme.Auto).AddEntityFramework();
//builder.Services.AddHealthChecks();
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(x =>
{
    x.SwaggerDoc("apiv1", new OpenApiInfo
    {
        Title = "Application Api v1",
        Description = "Application Api v1."
    });
    x.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = @"JWT Authorization header using the Bearer scheme.
                    Enter 'Bearer' [space] and then your token in the text input below.
                    Example: 'Bearer 12345abcdef'",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });
    x.AddSecurityRequirement(new OpenApiSecurityRequirement()
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                },
                Scheme = "oauth2",
                Name = "Bearer",
                In = ParameterLocation.Header,
            },
            new List<string>()
        }
    });
});
builder.Services.ConfigureHttpJsonOptions(options =>
{
    options.SerializerOptions.PropertyNamingPolicy = null;
    options.SerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
});
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy => policy.AllowAnyHeader().AllowAnyMethod().AllowAnyOrigin());
});
ServiceDescriptor[] endpointServiceDescriptors = Assembly
    .GetExecutingAssembly()
    .DefinedTypes
    .Where(type => type is { IsAbstract: false, IsInterface: false } &&
                    type.ImplementedInterfaces.Any(x => x == typeof(IEndpoint)))
    .Select(type => ServiceDescriptor.Transient(typeof(IEndpoint), type))
    .ToArray();
builder.Services.TryAddEnumerable(endpointServiceDescriptors);
builder.Services.AddControllersWithViews()
    .AddJsonOptions(opts => opts.JsonSerializerOptions.PropertyNamingPolicy = null);

var app = builder.Build();
app.UseHsts();
app.UseHttpsRedirection();
app.UseDefaultFiles();
app.UseStaticFiles();
var staticFilePath = Path.Combine(Directory.GetCurrentDirectory(), "Static");
if (!Directory.Exists(staticFilePath))
{
    Directory.CreateDirectory(staticFilePath);
}
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(staticFilePath),
    RequestPath = "/static"
});
var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "Uploads");
if (!Directory.Exists(uploadsPath))
{
    Directory.CreateDirectory(uploadsPath);
}
app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(uploadsPath),
    RequestPath = "/uploads"
});
if (app.Environment.IsDevelopment())
{
    app.UseMiniProfiler();
    app.UseMigrationsEndPoint();
}
app.UseSwagger(options =>
{
    options.RouteTemplate = "openapi/{documentName}.json";
});
app.MapScalarApiReference();
app.UseSwaggerUI(c =>
{
    c.DocumentTitle = "API";
    c.DocExpansion(DocExpansion.None);
    c.DefaultModelRendering(ModelRendering.Example);
    c.EnableTryItOutByDefault();
    c.DefaultModelExpandDepth(1);
    c.ConfigObject.AdditionalItems.Add("syntaxHighlight", false);
    c.SwaggerEndpoint($"/openapi/apiv1.json", "Api v1");
});
app.UseCors("AllowAll");
app.ConfigureCustomException();
app.UseRouting();
IEnumerable<IEndpoint> endpoints = app.Services.GetRequiredService<IEnumerable<IEndpoint>>();
foreach (IEndpoint endpoint in endpoints)
{
    endpoint.MapEndpoints(app);
}
app.UseAuthentication();
app.UseAuthorization();
app.MapControllerRoute(
    name: "default",
    pattern: "{controller}/{action=Index}/{id?}");
app.MapFallbackToFile("/index.html");

await app.RunAsync();
