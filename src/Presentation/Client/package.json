{"name": "tok-goz", "version": "0.1.0", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "less": "lessc --js ./src/styles/antd.less ./src/styles/Antd.css"}, "dependencies": {"@ant-design/icons": "^5.4.0", "@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@react-google-maps/api": "^2.19.3", "@reduxjs/toolkit": "^2.2.7", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.105", "@types/react": "^18.3.4", "@types/react-dom": "^18.3.0", "antd": "^5.20.2", "axios": "^1.8.2", "dayjs": "^1.11.13", "moment": "^2.30.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-number-format": "^5.4.0", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router-dom": "^6.26.1", "react-scripts": "5.0.1", "recharts": "^2.12.7", "swiper": "^11.1.9", "typescript": "^4.9.5", "use-debounce": "^10.0.3", "web-vitals": "^2.1.4"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.10"}}