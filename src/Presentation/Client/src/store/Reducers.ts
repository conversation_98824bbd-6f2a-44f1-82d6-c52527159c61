import { combineReducers } from "@reduxjs/toolkit";
import accountSlice from "apps/Account/ClientSideStates";
import companySlice from "apps/Company/ClientSideStates";
import buildingSlice from "apps/Building/ClientSideStates";
import stationSlice from "apps/Station/ClientSideState";
import productSlice from "apps/Product/ClientSideState";
import contractSlice from "apps/Contract/ClientSideStates";
import userSlice from "apps/User/ClientSideStates";
import carSlice from "apps/Car/ClientSideStates";
import departmentSlice from "apps/Deparment/ClientSideStates";
import notificationSlice from "apps/Notification/ClientSideStates";
import reportSlice from "apps/Report/ClientSideStates";
import transactionSlice from "apps/Plan/ClientSideStates";
import gallerySlice from "apps/Gallery/ClientSideStates";
import dashboardSlice from "apps/Dashboard/ClientState";
import commentSlice from "apps/Comment/ClientSideStates";
import driverSlice from "apps/Drivers/ClientSideStates"


const rootReducer = combineReducers({
   
    account: accountSlice.reducer,
    company: companySlice.reducer,
    building:buildingSlice.reducer,
    station:stationSlice.reducer,
    prodcut:productSlice.reducer,
    contract:contractSlice.reducer,
    user:userSlice.reducer,
    car:carSlice.reducer,
    department:departmentSlice.reducer,
    notification:notificationSlice.reducer,
    report:reportSlice.reducer,
    transactions:transactionSlice.reducer,
    gallery:gallerySlice.reducer,
    dashboard:dashboardSlice.reducer,
    comment:commentSlice.reducer,
    driver:driverSlice.reducer,
  
  });
  
  export type RootState = ReturnType<typeof rootReducer>;
  
  export default rootReducer;