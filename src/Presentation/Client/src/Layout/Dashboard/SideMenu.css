.side-menu {
    /* background-color: transparent; */
    z-index: 1010;
    overflow: auto;
    /* height: 100vh; */
    position: fixed;
    top: 0;
    left: 0;
}
.side-menu.ant-layout-sider-collapsed .sidemenu-logo {
    margin: 4px;
}
.side-menu .ant-menu,
.ant-layout-sider-trigger {
    background: transparent;
    width: 100%;
}
.side-menu:not(.ant-layout-sider-collapsed) .ant-menu-item {
    display: flex;
    flex-direction: column;
    height: auto;
    margin: 0;
    padding: 12px 5px 13px 5px !important;
}
.side-menu:not(.ant-layout-sider-collapsed) .ant-menu-submenu {
    display: flex;
    flex-direction: column;
    height: auto;
    margin: 0;
    padding: 12px 5px 13px 5px !important;
}
.side-menu:not(.ant-layout-sider-collapsed) .ant-menu-item svg {
    display: inline-block;
    font-size: 20px;
}
.side-menu:not(.ant-layout-sider-collapsed)
    .ant-menu-item
    .ant-menu-title-content {
    font-size: 11px;
    line-height: 25px;
    text-align: center;
    margin: 0;
}
.side-menu:not(.ant-layout-sider-collapsed) .ant-menu-submenu {
    display: flex;
    flex-direction: column;
    height: auto;
    margin: 0;
    padding: 12px 5px 13px 5px !important;
}
.side-menu:not(.ant-layout-sider-collapsed) .ant-menu-submenu svg {
    font-size: 20px;
}
.side-menu:not(.ant-layout-sider-collapsed)
    .ant-menu-submenu
    .ant-menu-submenu-title {
    font-size: 11px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    margin: 0;
    padding: 0;
}
.side-menu:not(.ant-layout-sider-collapsed)
    .ant-menu-submenu
    .ant-menu-title-content {
    display: none;
}
.side-menu:not(.ant-layout-sider-collapsed)
    .ant-menu-submenu
    .ant-menu-submenu-arrow {
    display: none;
}

.main-menu {
    border: none;
}
.main-menu .ant-menu-item-selected {
    background-color: white !important;
}
.main-menu .ant-menu-item-selected .ant-menu-title-content {
    color: rgba(0, 0, 0, 0.85) !important;
}
.main-menu .ant-menu-item-selected .anticon {
    color: rgba(0, 0, 0, 0.85) !important;
}

.tool-menu {
    position: absolute;
    bottom: 0;
    width: 100% !important;
    border: none;
}

.tool-menu .ant-menu-item-selected {
    background-color: transparent !important;
}

.sub-menu-sider {
    position: fixed !important;
    left: 5% !important;
    height: 100% !important;
    overflow: auto !important;
    z-index: 1010;
    margin-left: 0px;
    border-right: 2px solid rgb(225, 225, 225);
}

.ant-layout-sider-collapsed + .sub-menu-sider {
    margin-left: 40px;
}
.sub-menu-sider:not(.ant-layout-sider-collapsed) {
    border-right: 2px solid #e1e1e1;
}
