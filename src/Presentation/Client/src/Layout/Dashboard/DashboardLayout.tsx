import { Layout } from "antd";
import { Outlet } from "react-router-dom";
import { Footer } from "antd/lib/layout/layout";
import { useState } from "react";
import { SiderMenu } from "./Components/SiderMenu";




const { Content } = Layout;
const DashboardLayout = () => {
  const [collapsedSideMenu, setCollapsedSideMenu] = useState(false);


  return (
    <Layout  className={`${collapsedSideMenu ? " collapsed" : ""} !h-[100vh] !overflow-scroll `}>
      <SiderMenu setCollapsedSideMenu={setCollapsedSideMenu} /> 

      <Layout className="" >
        <Content className="!pl-24 ">
          <Outlet />
        <Footer
          className="text-xs text-gray-200 "
          style={{ textAlign: "center" }}
        >
          <span className="text-green-300">MAZAKA</span> Concrete-Company Solutions{" "}
        </Footer>
        </Content>
      </Layout>
    </Layout>
  );
};

export default DashboardLayout
