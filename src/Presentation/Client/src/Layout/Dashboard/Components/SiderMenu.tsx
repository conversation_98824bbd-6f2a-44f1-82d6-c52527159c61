import { Layout, Menu, MenuProps } from "antd";
import { useEffect, useState } from "react";
import { ReactComponent as Logo } from "assets/Images/TokGoz/MazakaLogo.svg";
import "Layout/Dashboard/SideMenu.css";
import { ItemType } from "antd/es/menu/interface"; 
import { useNavigate } from "react-router-dom";
import { openNotificationWithIcon } from "helpers/Notifications";
import MainMenuItems from "./MainMenuItems";
import { MenuData } from "../MenuItems";

const { Sider } = Layout;

export const SiderMenu = (props:any) => {
  const [menuItems, setMenuItems] = useState<any[]>([]);
  const [subMenuItems, setSubMenuItems] = useState<ItemType[]>([]);
  const [collapsedMainMenu, setCollapsedMainMenu] = useState(false);
  const [collapsedSubMenu, setCollapsedSubMenu] = useState(true);
  const [selectedMainMenuItem, setSelectedMainMenuItem] = useState("");
  const [selectedToolMenuItem, setSelectedToolMenuItem] = useState("");
  const navigate = useNavigate();



  useEffect(() => {
    const getUserMenuItems = async () => {
      try {
        const sortedMenuItems = MenuData.sort(
          (item1, item2) => item1.Sira - item2.Sira
        );
        setMenuItems(sortedMenuItems);
      } catch (error: any) {
        openNotificationWithIcon(
          "error",
          error?.Message ? error.Message : "basarsiz"
        );
        console.log("Something's wrong during get user menu", error);
      } 
    };
    getUserMenuItems();
  }, []);

  const selectMenuItem: MenuProps["onClick"] = (e) => {
    localStorage.setItem("activeSubMenuItem", e.key);
    var menuItem = menuItems.find((item) => item.Id === e.key);
    if (menuItem) {
      navigate(menuItem.Controller);
      setCollapsedSubMenu(true);
    }
  };

 


  

  return (
    <>
      <Sider
        className="side-menu !h-[100vh] !fixed left-0 top-0  "
        theme="light"
        collapsed={collapsedMainMenu}
        width="90"
        collapsedWidth={40}
      >
        <Logo className="sidemenu-logo h-8 mx-7 my-4" />
  
          <MainMenuItems
            setSelectedMainMenuItem={setSelectedMainMenuItem}
            selectedMainMenuItem={selectedMainMenuItem}
            selectedToolMenuItem={selectedToolMenuItem}
        
            collapsedSubMenu={collapsedSubMenu}
            setCollapsedSubMenu={setCollapsedSubMenu}
            subMenuItems={subMenuItems}
            setSubMenuItems={setSubMenuItems}
            collapsedMainMenu={collapsedMainMenu}
          
          />
     
      </Sider>
      <Sider
        className="sub-menu-sider "
        theme="light"
        width="330"
        collapsed={
          subMenuItems.length <= 0 ||
          collapsedMainMenu ||
          (!collapsedMainMenu && collapsedSubMenu)
        }
        collapsedWidth={0}
      >
        <Menu
          triggerSubMenuAction="click"
          mode="inline"
          onClick={selectMenuItem}
          items={subMenuItems}
          className="!pt-4"
        
        />
      </Sider>
    </>
  );
};
