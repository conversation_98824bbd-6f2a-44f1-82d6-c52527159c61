import {
  ApartmentOutlined,
  BellOutlined,
  CarOutlined,
  CommentOutlined,
  DashboardOutlined,
  HomeOutlined,
  InsertRowLeftOutlined,
  LineChartOutlined,

  PictureOutlined,
  ProductOutlined,
  ScheduleOutlined,
  SolutionOutlined,
  UsergroupAddOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Menu, MenuProps } from "antd";
import { FC, useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";
import { ItemType } from "antd/es/menu/interface";
import { logout } from "helpers/Logout";
import { MenuData } from "../MenuItems";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { permissions } from "apps/Permission/PermissionIndex";

interface MainMenuItemsProps {
  selectedMainMenuItem: any;
  setSelectedMainMenuItem: any;
  selectedToolMenuItem: any;
  collapsedSubMenu: any;
  setCollapsedSubMenu: any;
  subMenuItems: any;
  setSubMenuItems: any;
  collapsedMainMenu: any;

}

const getMenuItemsTree = (
  menuItems: any,
  UstYetkiId: string | undefined = undefined
): ItemType[] => {
  return menuItems
    .filter((item: any) => item.UstYetkiId === UstYetkiId)
    .sort((a: any, b: any) => b.Pinned - a.Pinned)
    .map((item: any) => {
      var subMenus = getMenuItemsTree(menuItems, item.Id);

      var menu = {
        key: item.Id,
        icon: findSuitableIcon(item?.type),
        label: item.Name,
        children: subMenus.length > 0 ? subMenus : undefined,
        type: item.Action ? item.Action : null,
      };
      return menu as ItemType;
    });
};

const findSuitableIcon = (type: string) => {
  if (type === "dashboard") {
    return <DashboardOutlined />;
  } else if (type === "schedule") {
    return <ScheduleOutlined />;
  } else if (type === "contract") {
    return <SolutionOutlined />;
  } else if (type === "report") {
    return <LineChartOutlined />;
  } else if (type === "station") {
    return <ApartmentOutlined />;
  } else if (type === "vehicle") {
    return <CarOutlined />;
  } else if (type === "user") {
    return <UsergroupAddOutlined />;
  } else if (type === "company") {
    return <InsertRowLeftOutlined />;
  } else if (type === "product") {
    return <ProductOutlined />;
  } else if (type === "galery") {
    return <PictureOutlined />;
  }
  else if (type === "building") {
    return <HomeOutlined />;
  }
  else if (type === "notification") {
    return <BellOutlined />;
  }
  else if (type === "comment") {
    return <CommentOutlined />;
  }
};

const getMainMenuItems = (menuItems: any): ItemType[] => {
  var mainMenuItems = menuItems.filter(
    (item: any) => item.UstYetkiId === undefined
  );
  return getMenuItemsTree(mainMenuItems);
};

const MainMenuItems: FC<MainMenuItemsProps> = ({
  setSelectedMainMenuItem,
  selectedMainMenuItem,
  selectedToolMenuItem,
  collapsedSubMenu,
  setCollapsedSubMenu,
  subMenuItems,
  setSubMenuItems,
  collapsedMainMenu,

}) => {
  const { userInfo } = useSelector((state: RootState) => state.account);
  const [menusWithPermissions, setMenusWithPermissions] = useState<any[]>([]);

  const navigate = useNavigate();
  useEffect(() => {
    if (userInfo?.RoleId) {
      const currentRoleId = userInfo.RoleId;


      const rolePermissions = permissions.find(
        (perm: any) => perm[currentRoleId] !== undefined
      ) as { [key in any]?: string[] };

      if (rolePermissions) {
        const allowedPermissions = rolePermissions[currentRoleId] || [];
        if (allowedPermissions.includes("*")) {
          setMenusWithPermissions(MenuData);
        } else {
          const filteredMenus = MenuData.filter(

            (menu) => {
              return !menu.type || allowedPermissions.includes(menu.type || "")

            }
          );
          setMenusWithPermissions(filteredMenus);
        }
      }
      else {
        setMenusWithPermissions([]);
      }
    }
  }, [userInfo]);





  const [menuItems, setMenuItems] = useState<any[]>([]);

  useEffect(() => {

    const sortedMenuItems: any[] = menusWithPermissions.sort(
      (item1: any, item2: any) => item1.Sira - item2.Sira
    );
    setMenuItems(sortedMenuItems);
  }, [menusWithPermissions]);

  const selectMainMenuItem: MenuProps["onClick"] = (e) => {
    if (e.key === selectedMainMenuItem) {
      setCollapsedSubMenu(!collapsedSubMenu);
      setSelectedMainMenuItem("");
    } else {
      setSelectedMainMenuItem(e.key);
      setCollapsedSubMenu(false);
    }

    let treeSubMenuItems = getMenuItemsTree(menuItems, e.key);
    setSubMenuItems(treeSubMenuItems);
    if (treeSubMenuItems.length <= 0) {
      var menuItem: any = menuItems.find((item: any) => item.Id === e.key);
      if (menuItem) {
        navigate(menuItem.Controller);
        setCollapsedSubMenu(true);
      }
    }
    if (e.key === "userInfo") {
      setSubMenuItems([
        {
          key: "Profil",
          onClick: () => {
            setSubMenuItems([]);
          },
          label: <Link to="/user/profile">{"Profil"}</Link>,
        },

        {
          key: "logout",
          label: "Çıkış",
          onClick: () => {
            logout(navigate);
          },
        },
      ]);
    }
  };



  return (
    <>
      <Menu
        className="main-menu"
        theme="light"
        onClick={selectMainMenuItem}
        items={

          getMainMenuItems(menuItems)
        }
        selectedKeys={[selectedMainMenuItem]}
      />
      <Menu
        theme="light"
        className=""
        selectedKeys={[selectedToolMenuItem]}
        onClick={selectMainMenuItem}
        items={[
          {
            key: "userInfo",
            icon: (
              <div className="!flex justify-center !w-full">
                <UserOutlined />
              </div>
            ),
          },


        ]}
      ></Menu>
    </>
  );
};

export default MainMenuItems;
