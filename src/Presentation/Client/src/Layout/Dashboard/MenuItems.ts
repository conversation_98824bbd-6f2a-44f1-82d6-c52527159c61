export interface MenuItem {
  Name: string;
  Controller?: string;
  Sira: number;
  type?: string;
  Id: string;
  Active?: boolean;
  UstYetkiId?: string;
}



export const MenuData: MenuItem[] = [
  {
    Name: "Dashboard",
    Controller: "/dashboard",
    Sira: 1,
    type: "dashboard",
    Id: "1",
  },
  {
    Name: "Planlar",
    Sira: 2,
    type: "schedule",
    Id: "2",
    Active: true,
  },
  {
    Name: "Planlanacak Operasyonlar",
    Controller: "/plan/transaction/list",
    UstYetkiId: "2",
    Sira: 1,
    Id: "A1",
    Active: true,
  },
  {
    Name: "<PERSON><PERSON><PERSON>",
    Controller: "/plan/discovery/list",
    UstYetkiId: "2",
    Sira: 4,
    Id: "A1344",
    Active: true,
  },
  {
    Name: "Tü<PERSON>",
    Controller: "/plan/all/transaction/list",
    UstYetkiId: "2",
    Sira: 5,
    Id: "7098",
    Active: true,
  },
  {
    Name: "<PERSON>özleşmesiz ve <PERSON>",
    Controller: "/tranascion/without/approved/list",
    UstYetkiId: "2",
    Sira: 2,
    Id: "A2",
    Active: true,
  },
  {
    Name: "İptal ve İadeler",
    Controller: "/tranascion/returning/list",
    UstYetkiId: "2",

    Sira: 3,

    Id: "A3",
    Active: true,
  },

  {
    Name: "Sözleşmeler",
    Sira: 3,
    type: "contract",
    Id: "3",
    Active: true,
  },
  {
    Name: "Tüm Sözleşmeler",
    Controller: "/contract/list",
    UstYetkiId: "3",
    Sira: 3,
    Id: "B1",
    Active: true,
  },
  {
    Name: "Onay Bekleyenler",
    Controller: "/contract/wait/approve/list",
    UstYetkiId: "3",
    Sira: 3,
    Id: "B2",
    Active: true,
  },
  {
    Name: "Raporlar",
    Sira: 4,
    type: "report",
    Id: "4",
    Active: true,
  },
  {
    Name: "Gecikmiş Sevkiyat Raporu",
    Controller: "/report/delay/list",
    UstYetkiId: "4",
    Sira: 1,
    Id: "44",
    Active: true,
  },
  {
    Name: "Araçların Günlük Dağıtım Raporu",
    Controller: "/report/vehicle/list",
    UstYetkiId: "4",
    Sira: 2,
    Id: "45",
    Active: true,
  },
  {
    Name: "Firma Çıkış Raporu",
    Controller: "/report/Daily/transaction",
    UstYetkiId: "4",
    Sira: 2,
    Id: "46",
    Active: true,
  },
  {
    Name: "Detaylı Firma Sevkiyat Raporu",
    Controller: "/report/daily/customer",
    UstYetkiId: "4",
    Sira: 2,
    Id: "47",
    Active: true,
  },
  {
    Name: "Garanti Fiyatı Bitecek Sözleşmeler Raporu",
    Controller: "/report/guaranteed/list",
    UstYetkiId: "4",
    Sira: 2,
    Id: "48",
    Active: true,
  },

  {
    Name: "İade Raporu",
    Controller: "/report/return/list",
    UstYetkiId: "4",
    Sira: 2,
    Id: "50",
    Active: true,
  },
  {
    Name: "Şantiyeler",
    Controller: "/building/list",
    type:"building",
    Sira: 5,
    Id: "5",
    Active: true,
  },
  {
    Name: "İstasyonlar",
    Controller: "/station/list",
    Sira: 6,
    type: "station",
    Id: "6",
    Active: true,
  },
  {
    Name: "Araçlar",
    Controller: "/car/list",
    Sira: 7,
    type: "vehicle",
    Id: "7",
    Active: true,
  },

  {
    Name: "Kullanıcılar",
    Controller: "/user/list",
    Sira: 8,
    type: "user",
    Id: "8",
    Active: true,
  },
  {
    Name: "Firmalar",
    Controller: "/company/list",

    Sira: 9,
    type: "company",

    Id: "9",
    Active: true,
  },
  {
    Name: "Ürün",
    Controller: "/product/list",
    Sira: 10,
    type: "product",
    Id: "10",
    Active: true,
  },
  {
    Name: "Yorumlar",
    Controller: "/comment/list",
    Sira: 11,
    type: "comment",
    Id: "11",
    Active: true,
   
  },
 
  {
    Name: "Galeri",
    Controller: "/gallery/list",

    Sira: 12,
    type: "galery",
    Id: "12",
    Active: true,
  },
  {
    Name: "Bildirim",
    Controller: "/notification/list",
    Sira: 13,
    type: "notification",
    Id: "13",
    Active: true,
  },
  
 
];
