import { get, post } from "services/BaseClient/Client";

import headers from "services/BaseClient/Headers.json";
import { endpoints } from './BaseClient/Endpoints';
import { CreateUrlFilter } from "helpers/CreateURLFilter";

export const getCountryList = async () => {
  const url = `${endpoints.getCountries}?PageSize=-1`;
  const config = headers.content_type.application_json;
  return get(url, config);
};

export const getStateProvinceList = async (countryCode:string |undefined) => {
  if (countryCode) {
    const url = `${endpoints.getStateProvinces+ "/" + countryCode}?PageSize=-1`;
    const config = headers.content_type.application_json;
    return get(url, config);
  } else {
    return new Promise((resovle, reject) => {
      resovle([]);
    });
  }
};

export const getDistrictList = async (filter:any) => {
  if(filter?.StateProvinceId)
  {

    const query = CreateUrlFilter(filter)
      const url = `${endpoints.getDistricts}?${query}`;
      const config = headers.content_type.application_json;
      return get(url, config);


  }
  else{
    return new Promise((resovle, reject) => {
      resovle({Data:[]});
    });
  }
  
  
 
};
export const postDistrict= async (data:any) => {
    const url = `${endpoints.addDistrict}`;
    const config = headers.content_type.application_json;
    return post(url, data,config);
 
};
export const getCityList = async (filter:any) => {
  if (filter?.DistrictId) {
    const query = CreateUrlFilter(filter)
    const url = `${endpoints.getCities }?${query}`;
    const config = headers.content_type.application_json;
    return get(url, config);
  } else {
    return new Promise((resovle, reject) => {
      resovle({Data:[]});
    });
  }
};
export const postCity= async (data:any) => {
  const url = `${endpoints.addCity}`;
  const config = headers.content_type.application_json;
  return post(url, data,config);

};
