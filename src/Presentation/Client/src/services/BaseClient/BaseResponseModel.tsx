export type Response = {
    StatusCode: number;
    Success: boolean;
    Code: number;
    Message: string;
    InternalMessage: string;
};

export type DataResponse<T> = Response & {
   
    Validations: Array<any>;
   
  
        Data: T |any;
Value:T|any,
        PageIndex: number;
        PageSize:number;
        Count: number;
        FilteredCount: number;
        SortProperty: string;
        SortType: string;
        TotalPageSize: number;
  
};

export type ListResponse<T> = Response & {
    Data: T[];
    PageSize: number;
    PageIndex: number;
    Count: number;
    FilteredCount: number;
    SortProperty: string;
    SortType: string;
    TotalPageSize: number;
};
