import axios from "axios";


let servicesBaseUrl = "/";

if (!process.env.NODE_ENV || process.env.NODE_ENV === "development") {
    servicesBaseUrl =
      process.env.REACT_APP_DEVELOPMENT_API_BASE_URL || "https://localhost:4000/api/v1/";
    console.log(
      `%cDevelopment mode is active! Your API Base URL: ${servicesBaseUrl} | MAZAKA`,
      "color:green;font-size:14px"
    );
  } else {
    servicesBaseUrl = window.location.origin + "/api/v1/";
  }


    /**
     * Create axios instance
     */
    let instance = axios.create({
      baseURL: servicesBaseUrl,
    });
  
    /**
     * Configure all http requests from the App
     */
    instance.interceptors.request.use(
      (config:any) => {
        const apiToken = localStorage.getItem("access_token");
        if (!config.headers) config.headers = {};
        if (!!apiToken) config.headers["Authorization"] = `Bearer ${apiToken}`;
  
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );
    /**
     * Configure all http response from the Request Server
     */
    instance.interceptors.response.use(
      (response) => {
        
  
        return response;
      },
      (error) => {
        // if (error?.response?.status === 401 || error?.response?.status === 403) {
        //   localStorage.removeItem("access_token");
        //   localStorage.removeItem("loggedIn");
        //   window.location.replace("/admin/login");
        // }
  
       
  
        return Promise.reject(error?.response?.data || error?.response || error);
      }
    );
  
  
  export default instance;