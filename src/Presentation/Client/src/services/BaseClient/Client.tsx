import http from "services/BaseClient/Http"
import axios, { AxiosRequestHeaders } from "axios";
const CancelToken = axios.CancelToken;
const source = CancelToken.source();

export async function get<TModel>(
  url: string,
  headers: any
): Promise<TModel | any> {
  return http
    .get<TModel | any>(url, { cancelToken: source.token, headers: headers })
    .then((response) => {
      return response.data;
    });
}

export async function post<TModel>(
  url: string,
  data: any,
  headers: any
): Promise<TModel> {
  return http
    .post<TModel>(url, data, {
      cancelToken: source.token,
      headers: headers,
    })
    .then((response) => {
      return response.data;
    });
}

export async function put<TModel>(
  url: string,
  data: any,
  headers: any
): Promise<TModel> {
  return http
    .put<TModel>(url, data, { cancelToken: source.token, headers: headers })
    .then((response) => {
      return response.data;
    });
}
export async function getFile<TModel>(
  url: string,
  headers: any
): Promise<TModel | any> {
  return http.get(url, headers);
}
export async function postFile<TModel>(
  url: string,
  data: any,
  headers: any
): Promise<TModel | any> {
  return http.post(url, data, headers);
}

export async function deleteRequestList<TModel>(
  url: string,
  data: any,
  headers: any
): Promise<TModel> {
  return http
    .delete<TModel>(url, { data, cancelToken: source.token, headers })
    .then((response) => {
      return response.data;
    });
}

export async function patch<TModel>(
  url: string,
  data: any,
  headers: any
): Promise<TModel> {
  return http
    .patch<TModel>(url, data, {
      cancelToken: source.token,
      headers: headers,
    })
    .then((response) => {
      return response.data;
    });
}
// 'delete' variable name has been using by javascript, so I couldn't use this variable name.
export async function deleteRequest<TModel>(
  url: string,
 
  data?: any,
  headers?: any,
): Promise<TModel> {
  return http
    .delete<TModel>(url, {
      cancelToken: source.token,
      headers: headers,
      data: data,
    })
    .then((response) => {
      return response.data;
    });
}
