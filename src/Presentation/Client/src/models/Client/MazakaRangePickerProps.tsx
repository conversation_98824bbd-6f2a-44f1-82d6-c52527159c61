import { ColProps } from "antd";
import { SizeType } from "antd/lib/config-provider/SizeContext";
import { FormItemProps } from "antd/lib/form";
import { HTMLAttributes } from "react";
export interface MazakaRangePickerProps
  extends Omit<ColProps, keyof HTMLAttributes<HTMLDivElement>>,
    FormItemProps {
  size?: SizeType;
  LeftPlaceHolder?: string;
  RightPlaceHolder?: string;
  onChange?: any;
  value?: any;
  showTime?: boolean;
}
