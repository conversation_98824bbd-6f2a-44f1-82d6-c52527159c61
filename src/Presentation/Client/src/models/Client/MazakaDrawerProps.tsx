import { DrawerProps } from "antd";

export interface MazakaDrawerProps extends DrawerProps {
  children?: JSX.Element | JSX.Element[];
  span?: number;
  title?: any;
  onClick?: any;
  strong?: boolean;
  italic?: boolean;
  formTitle?: string;
  formText?: any;
  toggleVisible: Function;
  /**
   * @deprecated `text` is deprecated which will be removed in next major version. Please use
   *   `description` instead.
   */
  text?: any;
  description?: React.ReactNode;
  layoutType?: "wide" | "strecth";
}
