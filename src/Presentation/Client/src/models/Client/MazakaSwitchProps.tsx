import { ColProps, SwitchProps } from "antd";
import { FormItemProps } from "antd/lib/form";
import { ReactNode } from "react";

export interface MazakaSwitchProps
  extends FormItemProps,
    Omit<ColProps, "onChange" | "children" | "onReset" | "onClick" | "defaultValue">,
    Omit<SwitchProps, "defaultValue"> {
  checkedChildren?: ReactNode;
  unCheckedChildren?: ReactNode;
  wrapperClassName?: string;
  ref?: any;
}
