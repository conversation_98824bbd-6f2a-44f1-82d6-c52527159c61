import { LayoutProps } from "antd";
import { ReactNode } from "react";

export interface MazakaLayoutProps extends LayoutProps {
  layoutType?: "wide" | "strecth";
  code?: boolean;
  mark?: boolean;
  type?: "secondary" | "success" | "warning" | "danger";
  underline?: boolean;
  Tlevel?: 2 | 3 | 4 | 5;
  copyable?: boolean;
  text?: any;
  visible?: boolean;
  headDescription?: any;
  buttonTitle?: any;
  buttonIcon?: ReactNode;
  title?: any;

  buttonOnClick?: () => void;
}
