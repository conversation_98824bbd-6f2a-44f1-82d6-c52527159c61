import { ColProps, FormProps } from "antd";
import { HTMLAttributes, ReactNode } from "react";
import { MazakaButtonProcessType } from "models/Client/MazakaButtonProccessType"

/**
 * @param SubmitButtonPosition Array<VerticalPosition, HorizontalPosition>
 */
export type SubmitButtonYPosition = "top" | "bottom";
export type SubmitButtonXPosition = "left" | "right";
export interface MazakaFormProps
  extends Omit<ColProps, keyof HTMLAttributes<HTMLDivElement>>,
    FormProps {
  formId?: string;
  children?: JSX.Element | JSX.Element[];
  level?: 2 | 3 | 4 | 5;
  code?: boolean;
  mark?: boolean;
  type?: "secondary" | "success" | "warning" | "danger";
  underline?: boolean;
  formTitle?: string;
  formText?: string;
  copyable?: boolean;
  gutter?: [number, number];
  isWraperSubmit?: boolean;
  isCancelButton?: boolean;
  cancelButtonOnClick?: any;
  submitButtonCustomClass?: string;
  /**
   * @description This is content at beside the submitButton in same main content
   */
  submitContent?: ReactNode;
  /**
   * @description Form's submitbutton position
   */
  submitButtonXPosition?: SubmitButtonXPosition;
  submitButtonYPosition?: SubmitButtonYPosition;
  /**
   * @description Form's submitbutton visible status
   */
  submitButtonVisible?: boolean;
  /**
   * @description Form's submitbutton disable status
   */
  submitButtonDisable?: boolean;
  /**
   * @description Submit button text. Default value = 'p.Save' ("Kaydet","Submit")
   */
  submitText?: string;
  /**
   * @description If there is any icon at submit button, you can definete with this property
   */
  submitIcon?: JSX.Element | JSX.Element[];
  /**
   * @description you can use while saving or updating etc. any process
   */
  submitLoading?: boolean;
  /**
   * @description you can use while saving or updating etc. any process
   */
  submitProcessType?: MazakaButtonProcessType;
}
