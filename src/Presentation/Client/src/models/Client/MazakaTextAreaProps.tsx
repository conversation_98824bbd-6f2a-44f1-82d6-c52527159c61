import { ColProps } from "antd";
import { FormItemProps } from "antd/lib/form";
import { TextAreaProps } from "antd/lib/input";
import { HTMLAttributes } from "react";

type OmitTypes =
  | "name"
  | "status"
  | "children"
  | "onReset";
export interface MazakaTextAreaProps extends
  Omit<FormItemProps, OmitTypes>,
  Omit<ColProps, keyof HTMLAttributes<HTMLDivElement>>,
  Pick<ColProps, "span">,
  TextAreaProps {
  textAreaClassname?: any;
}