import { LayoutProps } from "antd";

export interface MazakaHeadLayoutProps extends LayoutProps {
  layoutType?: "wide" | "strecth";
  code?: boolean;
  mark?: boolean;
  type?: "secondary" | "success" | "warning" | "danger";
  underline?: boolean;
  Tlevel?: 2 | 3 | 4 | 5;
  copyable?: boolean;
  /**
   * @deprecated `text` is deprecated which will be removed in next major version. Please use
   *   `description` instead.
   */
  text?: any;
  description?: string;
  title?: any;
  visible?: boolean;
  toggleVisible: Function;
}