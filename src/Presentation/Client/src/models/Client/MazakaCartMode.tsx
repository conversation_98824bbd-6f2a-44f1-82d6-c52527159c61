import { CardProps, ColProps } from "antd";
import { Gutter } from "antd/lib/grid/row";
import { HTMLAttributes } from "react";

/**
 * @Left - This position is Card internal header and it sliding card content to bottom
 * @LeftOut - This position is Card external header
 * @Right - This position is Card internal header and it sliding card content to bottom
 * @RightOut - This position is Card external header
 */
export type MazakaCardTitlePositions =
  | "Left"
  | "LeftOut"
  | "Right"
  | "RightOut";

export interface MazakaCardProps
  extends Omit<ColProps, keyof HTMLAttributes<HTMLDivElement>>,
    CardProps {
  description?: React.ReactNode;
  gutter?: Gutter | [<PERSON><PERSON>, Gutter];
  icon?: any;
  titlePosition?: MazakaCardTitlePositions;
  titleButton?: React.ReactNode;
  comingSoon?: boolean;
  isFetchingDataError?: boolean;
  ref?: any;
}
