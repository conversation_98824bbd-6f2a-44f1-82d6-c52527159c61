import { ColProps } from "antd";
import { SizeType } from "antd/lib/config-provider/SizeContext";
import { FormItemProps } from "antd/lib/form";
import { HTMLAttributes } from "react";
export interface MazakaPickerModel
  extends Omit<ColProps, keyof HTMLAttributes<HTMLDivElement>>,
    FormItemProps {
  size?: SizeType;
  DatePickerPlaceHolder?: string;
  onChange?: any;
  disabled?: boolean;
  disablePastDates? :any;
  showTime?:any;
  mode?:string;
  /**
   * @example
   *
   * <Datepicker
   *  defaultValue={moment()}
   * />
   */
  defaultValue?: any;
}
