import { ColProps } from "antd/lib/col";
import { FormItemProps } from "antd/lib/form";
import { SelectProps } from "antd/lib/select";

export interface MazakaSelectProps
  extends Omit<FormItemProps<any>, "children" | "onReset" | "status">,
    Omit<
      ColProps,
      | "defaultValue"
      | "onChange"
      | "onSelect"
      | "onBlur"
      | "onFocus"
      | "placeholder"
      | "prefix" // omit this to avoid conflict
    >,
    SelectProps<any> {
  colClassName?: any;
}
