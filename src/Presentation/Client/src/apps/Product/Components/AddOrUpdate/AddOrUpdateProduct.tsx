import { Col, Form, Row,} from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect, } from "react";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";
import { addProduct, updateProductWithPut } from "../../Services";
import { useQueryClient } from "react-query";
import endpoints from "apps/Product/EndPoints"
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
const AddOrUpdateProductForm: FC<GeneralAddOrUpdateFormProps> = ({
  selectedRecord,
  onFinish
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
   const formValues = form.getFieldsValue()
   formValues["Description"] = formValues["Description"]||""
  
 
    try {
      selectedRecord?await updateProductWithPut({...selectedRecord,...formValues}):await addProduct(formValues);
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      form.resetFields()
      queryClient.resetQueries({
        queryKey: endpoints.getProductListFilter,
        exact: false,
      });
      onFinish()
    } catch (error: any) {
      showServiceErrorMessage(error,mazakaForm,"Product")
      
    }
  };
  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });
    }
  }, [selectedRecord]);
 
 

  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} {...formActions}>
          <Row gutter={[0, 10]}>
            <MazakaInput
              className="!m-0"
              label={"Adı"}
              placeholder="Adı"
              name={"Name"}
              xs={24}
              rules={[{ required: true, message: "" }]}
            />

            <MazakaTextArea
              name="Description"
              label={"Açıklama"}
            placeholder={"Açıklama"}
              xs={24}
              className="!m-0"
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateProductForm;
