import { Col, Form, Row } from "antd";
import GeneralPhoneNumber from "apps/Common/GeneralPhoneNumber";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetPompTypeFilter, hanldleSetProductFilter } from "../ClientSideState";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import GeneralProductInput from "apps/Common/GeneralProductInput";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { productFilter } = useSelector((state: RootState) => state.prodcut);
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    
    
    // Boş değerleri filtrele
    const cleanFormValues = Object.fromEntries(
      Object.entries(formValues).filter(([, value]) => value != null && value !== '')
    );
    
    const cleanCurrentFilter = Object.fromEntries(
      Object.entries(productFilter || {}).filter(([, value]) => value != null && value !== '')
    );
    
    const newFilter = { ...cleanCurrentFilter, ...cleanFormValues };
  
    await dispatch(
      hanldleSetProductFilter({ filter: newFilter })
    );
    
    mazakaForm.setSuccess(1000, () => {}, "İşlem Başarılı");
    onFinish();
  };

  useEffect(() => {
    let data = { ...productFilter };
      if (data?.Phone) {
        data["Phone"] = data["Phone"].replace(
          /^0090(\d{3})(\d{3})(\d{4})$/,
          "+90 ($1) $2 $3"
        );
      }
    form.setFieldsValue({
      ProductIds: data?.ProductIds|| undefined,    
    });
  }, [productFilter]);
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
       

        <Row gutter={[0, 20]}>

        <GeneralProductInput
              name="ProductIds"
              label={"Ürün Adı"}
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />
          
            <Col xs={24}>
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                Filtrele
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
