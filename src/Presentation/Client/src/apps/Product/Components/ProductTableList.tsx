import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Col, Drawer, Dropdown, Modal, Spin, Table } from "antd";
import { ItemType } from "antd/es/menu/interface";
import { openNotificationWithIcon } from "helpers/Notifications";
import { lazy, Suspense, useState } from "react";
import { useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { ProductListAndDetails } from "../Models";
import { deleteProduct } from "../Services";
import endpoints from "apps/Product/EndPoints";
import { RootState } from "store/Reducers";
import { hanldleSetProductFilter } from "../ClientSideState";
import { useGetProducts } from "../ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
const AddOrUpdateProductForm = lazy(() => import("./AddOrUpdate/AddOrUpdateProduct"));

const ProductTableList: any = () => {
  const dispatch = useDispatch();
  const { productFilter } = useSelector((state: RootState) => state.prodcut);
  const queryClient = useQueryClient();
  const [selectedRecord, setSelectedRecord] =
    useState<ProductListAndDetails | null>(null);
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const columns = [
    {
      title: "Adı",
      dataIndex: "Name",
      sorter: (a: any, b: any) => a?.Name.localeCompare(b?.Name)
    },
    {
      title: "Açıklama",
      dataIndex: "Description",
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await setSelectedRecord(record);
        setIsShowEditDrawer(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyari",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecektir.Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgec",
      onOk: async () => {
        try {
          await deleteProduct(record);
          openNotificationWithIcon("success", "İşlem başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getProductListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "Product", true);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = {
      ...productFilter,
      PageIndex: pageNum,
      PageSize: pageSize,
    };
    dispatch(hanldleSetProductFilter({ filter: newFilter }));
  };
  const products = useGetProducts(productFilter);

  return (
    <>
      <Col span={24}>
        <Table
          columns={columns}
          dataSource={products.data ? products.data.Data : []}
          loading={products.isLoading || products.isFetching}
          rowKey={"Id"}
          pagination={{
            position: ["bottomRight"],
            className: "!px-0",
            onChange: handleChangePagination,
            total: products.data?.FilteredCount || 0,
            current: products.data?.PageIndex,
            pageSize: products.data?.PageSize,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
        />
      </Col>

      <Drawer
        title="Ürünü Güncelle"
        open={isShowEditDrawer}
        onClose={() => {
          setIsShowEditDrawer(false);
        }}
      >
        <>
          {isShowEditDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateProductForm
                selectedRecord={selectedRecord}
                onFinish={() => {
                  setIsShowEditDrawer(false);
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </>
  );
};

export default ProductTableList;
