import { useQuery } from "react-query";
import endpoints from "apps/Product/EndPoints";
import { getPompListFilter, getProductListFilter } from "apps/Product/Services";

export const useGetProducts = (filter: any) => {
  const query = useQuery(
    [endpoints.getProductListFilter, filter],
    () => {
      return getProductListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetPompTypes= (filter: any) => {
    const query = useQuery(
      [endpoints.getPompTypeListFilter, filter],
      () => {
        return getPompListFilter(filter);
      },
      {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
      }
    );
  
    return query;
  };