import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  productFilter: {
    PageIndex: 1,
    PageSize: 20,
  },
  pompTypeFilter:{
    PageIndex: 1,
    PageSize: 20,
  }
};

const productSlice = createSlice({
  name: "productSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetProductFilter: (state, action) => {
      let data = action.payload;
      state.productFilter = data.filter;
    },
    handleResetAllFieldsProduct: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterProduct: (state) => {
       state.productFilter = {
        PageIndex: 1,
        PageSize: 20,
      }
      },
      hanldleSetPompTypeFilter: (state, action) => {
        let data = action.payload;
        state.pompTypeFilter = data.filter;
     
      },
      handleResetFilterPomType: (state) => {
        state.pompTypeFilter = {
         PageIndex: 1,
         PageSize: 20,
       }
       },
  },
});

export const { handleResetAllFieldsProduct, hanldleSetProductFilter,handleResetFilterProduct,handleResetFilterPomType,hanldleSetPompTypeFilter} = productSlice.actions;
export default productSlice;
