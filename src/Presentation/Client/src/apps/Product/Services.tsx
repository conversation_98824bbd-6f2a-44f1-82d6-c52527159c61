import {deleteRequest, get, patch, post, put} from "services/BaseClient/Client"
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";
import endpoints from "apps/Product/EndPoints"
import { AddProductFormModel, PompType, ProductListAndDetails } from "./Models";



export const getProductListFilter = async (filter: any): Promise<DataResponse<ProductListAndDetails>> => {
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getProductListFilter}?${query}&SortProperty=Order`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  };
 

  export const addProduct = async (data: AddProductFormModel): Promise<DataResponse<any>> => {
    const url = `${endpoints.addProduct}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<any>>(url, data, config);
  };
  
  export const updateProductWithPut = async (data: ProductListAndDetails): Promise<DataResponse<ProductListAndDetails>> => {
    const url = `${endpoints.updateProductWithPut}`;
    const config = headers.content_type.application_json;
    return put<DataResponse<any>>(url, data, config);
  };
  export const updateProductWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateProductWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
  };
  export const deleteProduct = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.deleteProduct}/${data.Id}`;
    const config = headers.content_type.application_json;
    return deleteRequest<DataResponse<any>>(url, data, config);
  };

  export const getPompListFilter = async (filter: any): Promise<DataResponse<PompType>> => {
    const query = CreateUrlFilter(filter)
      const url = `${endpoints.getPompTypeListFilter}?${query}`;
      const config = headers.content_type.application_json;
    
      return get<DataResponse<any>>(url, config);
    };

export const addPompType = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.addPompType}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<any>>(url, data, config);
    };

export const updatePompTypeWithPut = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.updatePompTypeWithPut}`;
    const config = headers.content_type.application_json;
    return put<DataResponse<any>>(url, data, config);
    };
    export const updatePompTypeWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updatePompTypeWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
    };
    export const deletePompType = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.deletePompType}/${data.Id}`;
    const config = headers.content_type.application_json;
    return deleteRequest<DataResponse<any>>(url, data, config);
    };
    