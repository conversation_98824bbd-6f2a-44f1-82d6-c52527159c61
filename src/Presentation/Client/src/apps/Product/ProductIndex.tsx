import { LinkOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";
import { addPompType, deletePompType, updatePompTypeWithPut } from "./Services";
import endpoints from "apps/Product/EndPoints";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetPompTypes } from "./ServerSideStates";
import { handleResetFilterProduct, hanldleSetPompTypeFilter } from "./ClientSideState";
import ProductTableList from "./Components/ProductTableList";
import DetailsFilter from "./Components/DetailsFilter";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";

const  AddOrUpdateProductForm = lazy(
  () => import("./Components/AddOrUpdate/AddOrUpdateProduct")
);
const  StatusIndex = lazy(
  () => import("apps/Status/StatusIndex")
);

const ProductIndex = () => {
  const { Text } = Typography;

  const [isShowAddProductDrawer, setIsShowAddProductDrawer] = useState(false);
  const [
    isShowPompTypeList,
    setIsShowPompTypeList,
  ] = useState(false);
  const {  pompTypeFilter } = useSelector((state: RootState) => state.prodcut);
  const { productFilter } = useSelector((state:RootState)=> state.prodcut);
  const pompTypes = useGetPompTypes(pompTypeFilter);
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
  return (
    <>
      <MazakaLayout
        title={"Ürün Listesi"}
        headDescription={"Ürün sayfası, sistemde kayıtlı tüm ürünlerinizin listesini görüntülemenizi sağlar."}
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <Button
              onClick={() => {
                setIsShowAddProductDrawer(true);
              }}
              className="!flex items-center"
              type="primary"
            >
              <div className="!flex items-center gap-1">
                <PlusOutlined />
                <Text className="!text-white">Ürün Ekle</Text>
              </div>
            </Button>
            <Button
              onClick={() => {
                setIsShowPompTypeList(true);
              }}
              className="!flex items-center"
            >
              <div className="!flex items-center gap-1">
                <LinkOutlined />
                <Text>Pompa Tipi Ekle</Text>
              </div>
            </Button>
            <MazakaDetailsFilterButton
                  setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
                />
                {Object.entries(productFilter).length > 2 && (
                  <>
                    <MazakaClearFilterButton
                      actionFunk={ handleResetFilterProduct}
                     
                    />
                  </>
                )}
          </Col>
          <Col span={24}><ProductTableList/></Col>
        </Row>
      </MazakaLayout>

      <MazakaDrawer
        title={"Pompa Tipleri"}
        placement="right"
        open={isShowPompTypeList}
        toggleVisible={() => {
          setIsShowPompTypeList(
            !isShowPompTypeList
          );
        }}
        layoutType="strecth"
      >
        <>
        {
          isShowPompTypeList&&
          <Suspense fallback={<Spin/>}>
            <StatusIndex
              postService={addPompType}
              updateService={updatePompTypeWithPut}
              deleteService = {deletePompType}
              getQueryEndPoint={endpoints.getPompTypeListFilter}
              getQueryObj={pompTypes}
              filter={pompTypeFilter}
              actionFilterFunc={hanldleSetPompTypeFilter}
              formType="pompType"
              
            />

          </Suspense>
        }
        </>
      </MazakaDrawer>

      <Drawer
        title="Yeni Ürün Ekle"
        open={isShowAddProductDrawer}
        onClose={() => {
          setIsShowAddProductDrawer(false);
        }}
      >
        <>
        {
          isShowAddProductDrawer&&
          <Suspense fallback={<Spin/>}>
            <AddOrUpdateProductForm onFinish={()=>{
               setIsShowAddProductDrawer(false);
            }}/>
            
          </Suspense>
        }
        </>
      </Drawer>
      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
        <>
          {isShowFilterDetailsDrawer && (
            <Suspense fallback={<Spin />}>
              <DetailsFilter
                onFinish={() => {
                  setIsShowFilterDetailsDrawer(false);
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </>
  );
};

export default ProductIndex;
