import { Route } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";
const ProductIndex = lazy(() => import('./ProductIndex'))

export const productRouteList = [
  <Route key={"productRouteList"}>
    <Route
      path={"/product/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <ProductIndex/>
        </Suspense>
      }
    />
  </Route>,
];
