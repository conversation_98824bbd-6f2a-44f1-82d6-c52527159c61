import { Route } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";
const UserIndex  = lazy(() => import('./UserIndex'))
const Profile  = lazy(() => import('./Components/Profile'))


export const userRouteList = [


  <Route key={"UserRouteList"}>
    <Route
      path={"user/list"}
      element={
       <Suspense fallback={<Spin/>} >

         <UserIndex />
       </Suspense>
      
      }
    />
    <Route
      path={"user/profile"}
      element={
        <Suspense fallback={<Spin/>} >

          <Profile />
        </Suspense>
        
      }
    />
  </Route>,

];
