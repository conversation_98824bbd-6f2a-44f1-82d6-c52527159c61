import { Tabs, TabsProps } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetUserFilter } from "../ClientSideStates";
import { useEffect, useState } from "react";

const UserTabs = () => {
  const onChange = (key: string) => {
    let currentFilter: any = { ...filter };
    if (key === "1") {
      currentFilter["IsCompanyUser"] = false;
      currentFilter["PageSize"] = 20;
      delete  currentFilter["RoleIds"]
      delete  currentFilter["TaxNumber"]

    } else if (key === "2") {
      currentFilter["IsCompanyUser"] = true;
      currentFilter["PageSize"] = 20;
      delete  currentFilter["RoleIds"]
      delete  currentFilter["TaxNumber"]

    }
    else if(key==="3")
    {
      currentFilter["PageSize"] = -1;
      delete  currentFilter["IsCompanyUser"]
      delete  currentFilter["RoleIds"]
      delete  currentFilter["TaxNumber"]
    }
    else if(key==="4")
    {
      currentFilter["TaxNumber"] = "1";
      currentFilter["PageSize"] = 20;
      delete  currentFilter["RoleIds"]

    }
    else if(key==="5")
      {
        currentFilter["RoleIds"] = ["d9530b95-6cc4-4e13-8225-6d65d5b1e617","734354f3-c622-4750-a11c-04ab6f0fa497"];//kalipci,yapidenetim
        delete  currentFilter["IsCompanyUser"]
        delete  currentFilter["TaxNumber"]

        currentFilter["PageSize"] = 20;
      }
      setActiveKey(key)
    dispatch( hanldleSetUserFilter({filter:{...currentFilter}}))
  };
  const dispatch = useDispatch();
  const { filter } = useSelector((state: RootState) => state.user);
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Sistemsel",
    },
    {
      key: "2",
      label: "Müşteriler",
    },
    {
      key: "3",
      label: "Yeni Müşteriler",
    },
     {
      key: "4",
      label: "Perakende Müşteriler",
    },
    {
      key: "5",
      label: "Diğer",
    },
  ];
  useEffect(()=>{


  },[filter])
  const [activeKey,setActiveKey] = useState<string>("1")
  useEffect(()=>{
    if(filter?.IsCompanyUser)
    {
      setActiveKey("2")
    }
    if(!filter?.IsCompanyUser)
    {
      setActiveKey("1")
    }
    if(filter?.PageSize===-1)
    {
      setActiveKey("3")
    }
    if(filter?.RoleIds){
      setActiveKey("5")
    }
    if(filter?.TaxNumber){
      setActiveKey("4")
    }

  },[filter])
  return (
    <>
      <Tabs
        size="small"
        className="!text-gray-300"
        activeKey={activeKey}
        defaultActiveKey="1"
        onChange={onChange}
        items={items}
      />
    </>
  );
};

export default UserTabs;
