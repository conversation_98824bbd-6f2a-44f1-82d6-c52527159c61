import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  filter: {
    PageIndex: 1,
    PageSize: 20,
    IsCompanyUser:false,
    IncludeProperties:["UserDepartment","UserRole.Role","Company","BuildingUser.Building"],
    SortProperty:"Active",
    SortType:"ASC"
  },
  showIsNewCustomer:false,
};

const userSlice = createSlice({
  name: "UserSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetUserFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    hanldleSetIsNewCustomer: (state, action) => {
      let data = action.payload;
      state.showIsNewCustomer = data.status;
    },
    handleResetAllFieldsUser: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterUser: (state) => {
       state.filter = {
        PageIndex: 1,
        PageSize: 20,
        IsCompanyUser:false,
        IncludeProperties:["UserDepartment","UserRole.Role","Company","BuildingUser.Building"],
        SortProperty:"Active",
        SortType:"ASC"
      }
      },
  },
});

export const { handleResetAllFieldsUser,handleResetFilterUser,hanldleSetUserFilter, hanldleSetIsNewCustomer } = userSlice.actions;
export default userSlice;
