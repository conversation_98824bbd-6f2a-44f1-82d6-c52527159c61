import { LinkOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useEffect, useState } from "react";
import UserTabs from "./Components/Tabs";
import UserTableList from "./Components/UserTableList";
import endpoints from "apps/User/EndPoints";
import { useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import {
  handleResetFilterUser,
  hanldleSetUserFilter,
} from "./ClientSideStates";
import { useNavigate } from "react-router-dom";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import Search from "./Components/Search";
import DetailsFilter from "./Components/DetailsFilter";
const AddOrUpdateUserForm = lazy(
  () => import("./Components/AddOrUpdate/AddOrUpdateUserForm")
);

const UserIndex = () => {
  const { Text } = Typography;
  const queryClient = useQueryClient();
  const [isShowAddUserDrawer, setIsShowAddUserDrawer] = useState(false);
  const { filter } = useSelector((state: RootState) => state.user);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
  const dispatch = useDispatch();
  useEffect(() => {
    let currentFilter: any = { ...filter };
    currentFilter["IsCompanyUser"] = false;
    currentFilter["PageSize"] = 20;
    dispatch(hanldleSetUserFilter({ filter: { ...currentFilter } }));
  }, []);
  const navigate = useNavigate();
  return (
    <>
      <MazakaLayout
        title={"Kullanıcı Listesi"}
        headDescription={
          "Kullanıcı sayfası, sistemde  tüm kayıtlı kullanıcıların listesini görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]}>
          <Col span={24}>
            <UserTabs />
          </Col>
          <Col span={24} className="!flex justify-end !gap-3">
          {!filter?.IsCompanyUser && (
              <>
                <Button
                  onClick={() => {
                    navigate("/department/list");
                  }}
                  className="!flex items-center"
                >
                  <div className="!flex items-center gap-1">
                    <LinkOutlined />
                    <Text>Departman Ekle</Text>
                  </div>
                </Button>
              </>
            )}
            <MazakaDetailsFilterButton
              setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
            />
            {Object.entries(filter).length > 6 && (
              <>
                <MazakaClearFilterButton actionFunk={handleResetFilterUser} />
              </>
            )}
           

            <Button
              onClick={() => {
                setIsShowAddUserDrawer(true);
              }}
              className="!flex items-center"
              type="primary"
            >
              <div className="!flex items-center gap-1">
                <PlusOutlined />
                {(() => {
                  if (filter?.IsCompanyUser) {
                    return (
                      <>
                        <Text className="!text-white">Müşteri Ekle</Text>
                      </>
                    );
                  } else if (!filter?.IsCompanyUser) {
                    return (
                      <>
                        <Text className="!text-white">
                          Sistemsel Kullanıcı Ekle
                        </Text>
                      </>
                    );
                  } else if (filter?.PageSize === -1) {
                    return (
                      <>
                        <Text className="!text-white">Yeni Müşteri Ekle</Text>
                      </>
                    );
                  }
                })()}
              </div>
            </Button>
          </Col>
          <Col span={24}>
            <Row>
              <Col span={24} style={{ background: "#f0f0f0" }}>
                <Row gutter={[0, 10]} className="p-2">
                  <Col xs={24} xl={12} className="">
                    <Row gutter={[10, 10]}>
                      <Col xs={24} md={12} lg={10}>
                        <Search />
                      </Col>
                    </Row>
                  </Col>
                  <Col xs={24} xl={12} className="text-right"></Col>
                </Row>
              </Col>
              <Col span={24}>
                <UserTableList />
              </Col>
            </Row>
          </Col>
        </Row>
      </MazakaLayout>

      <Drawer
        title="Yeni Kullanıcı Ekle"
        open={isShowAddUserDrawer}
        onClose={() => {
          setIsShowAddUserDrawer(false);
        }}
      >
        <>
          {isShowAddUserDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateUserForm
                selectedRecord={selectedRecord}
                setSelectedRecord={setSelectedRecord}
                onFinish={() => {
                  setIsShowAddUserDrawer(false);
                  queryClient.resetQueries({
                    queryKey: endpoints.getUserListFilter,
                    exact: false,
                  });
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
        <>
          {isShowFilterDetailsDrawer && (
            <Suspense fallback={<Spin />}>
              <DetailsFilter
                onFinish={() => {
                  setIsShowFilterDetailsDrawer(false);
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </>
  );
};

export default UserIndex;
