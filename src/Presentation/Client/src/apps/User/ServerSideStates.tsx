import { useQuery } from "react-query";
import endpoints from "apps/User/EndPoints";
import { getBuildingUserListFilter, getUserDepartmentListFilter, getUserListFilter, getUserRoleList } from "./Services";


export const useGetUsers = (filter: any) => {
  const query = useQuery(
    [endpoints.getUserListFilter, filter],
    () => {
      return getUserListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetUserRoles= () => {
  const query = useQuery(
    [endpoints.getUserRoleListFilter],
    () => {
      return getUserRoleList();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetBuildingUsers = (filter: any) => {
  const query = useQuery(
    [endpoints.getUserListFilter, filter],
    () => {
      return getBuildingUserListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetUserDepartments = (filter: any) => {
  const query = useQuery(
    [endpoints.getUserDepartmentListFilter, filter],
    () => {
      return getUserDepartmentListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};