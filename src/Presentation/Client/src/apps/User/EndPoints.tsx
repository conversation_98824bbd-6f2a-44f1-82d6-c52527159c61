
const baseUserUrl = "User"
const baseUserDepartmentUrl = "UserDepartment"
const baseBuildingUserUrl = "BuildingUser"
  const endpoints = {
 
   getUserListFilter:`${baseUserUrl}/Filter`,
   getUserRoleListFilter:`${baseUserUrl}/Role`,
   getBuildingUserListFilter:`${baseBuildingUserUrl}/Filter`,
   addUser:`${baseUserUrl}`,
   addBuildingUser:`${baseBuildingUserUrl}`,
   updateUserWithPut:`${baseUserUrl}`,
   updateUserWithPatch:`${baseUserUrl}`,
   deleteUser:`${baseUserUrl}`,
   getUserDepartmentListFilter:`${baseUserDepartmentUrl}/Filter`,
   addUserDepartment:`${baseUserDepartmentUrl}`,
   updateUserDepartmentWithPut:`${baseUserDepartmentUrl}`
  };
  
  export default endpoints;
  