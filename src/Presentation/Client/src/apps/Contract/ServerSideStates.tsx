import { useQuery } from "react-query";
import endpoints from "apps/Contract/EndPoints";
import { getContractDetails, getContractFileUploadFilter, getContractListFilter, getContractPaymentPlanList, getContractProductListFilter, getContractTemplateListFilter } from "apps/Contract/Services";

export const useGetContracts = (filter: any) => {
  const query = useQuery(
    [endpoints.getContractListFilter, filter],
    () => {
      return getContractListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetContract = (contractId:string) => {
  const query = useQuery(
    [endpoints.getContractListFilter, contractId],
    () => {
      return getContractDetails(contractId);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetContractPaymentPlans = () => {
  const query = useQuery(
    [endpoints.getContractPaymentPlanList],
    () => {
      return getContractPaymentPlanList() ;
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetContractFileUpload = (filter: any) => {
  const query = useQuery(
    [endpoints.getFileUploadListFilter, filter],
    () => {
      return getContractFileUploadFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetContractProducts = (filter: any) => {
  const query = useQuery(
    [endpoints.getContractProductListFilter, filter],
    () => {
      return getContractProductListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetContractTemplates= (filter: any) => {
    const query = useQuery(
      [endpoints.getContractTemplateListFilter, filter],
      () => {
        return getContractTemplateListFilter(filter);
      },
      {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
      }
    );
  
    return query;
  };