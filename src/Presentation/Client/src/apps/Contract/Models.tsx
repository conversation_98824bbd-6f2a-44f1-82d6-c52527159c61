import { CompanyListAndDetails } from "apps/Company/Models";
import { ProductListAndDetails } from "apps/Product/Models";

interface ContractProduct {
  ContractId: string;
  Contract: string;
  ProductId: string;
  Product: ProductListAndDetails;
  Amount: number;
}
interface BaseSharedContract {
  CompanyId: string;
  Company: CompanyListAndDetails;
  Active: boolean;
  Title: string;
  Description: string;
  CreatedDate: string;
  CreatedUserId: string;
  StartDate: string;
  EndDate: string;
  ContractProduct: ContractProduct[];
}
export interface AddContractFormModel extends BaseSharedContract {}

export interface ContractListAndDetails extends BaseSharedContract {
  Id: string;
}

interface BaseSharedContractTemplate {
  Title:string;
  Description:string;
  Active:boolean
  }
  export interface AddContractTemplateFormModel extends BaseSharedContractTemplate {}
  
  export interface ContractTemplateListAndDetails extends BaseSharedContractTemplate {
    Id: string;
  }
