import { Tabs, TabsProps } from "antd";
import { hanldleSetContractFilter } from "apps/Contract/ClientSideStates";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const TabModes = () => {
  const { waitApproveFilter: filter } = useSelector(
    (state: RootState) => state.contract
  );
  const dispatch = useDispatch();
  const items: TabsProps["items"] = [
    {
      key: "pending",
      label: "Onay Bekleyenler",
      children: <></>,
    },
    {
      key: "rejected",
      label: "Reddedilenler",
      children: <></>,
    },
  ];
  const [activeKey, setActiveKey] = useState("pending");

  const handleOnChange = (value: string) => {
    let currentFilter = { ...filter };
    if (value === "pending") {
      currentFilter["IsApproved"]=0
    } else {
      currentFilter["IsApproved"] = 2
    }
    setActiveKey(value);
    dispatch(
      hanldleSetContractFilter({
        filter: currentFilter,
        type: "waitApproveFilter",
      })
    );
  };
  return (
    <>
      <Tabs activeKey={activeKey} items={items} onChange={handleOnChange} />
    </>
  );
};

export default TabModes;
