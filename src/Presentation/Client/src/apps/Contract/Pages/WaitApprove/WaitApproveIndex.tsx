
import {  Col,  Row, } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import {  useSelector, } from "react-redux";
import { RootState } from "store/Reducers";
import TopFilterOptions from "../All/components/TopFilterOptions";
import ContractTableList from "./Components/ContractTableList";
import TabModes from "./Components/TabModes";



const WaitApproveIndex = () => {
  const {waitApproveFilter} = useSelector((state:RootState)=>state.contract)

  return (
    <>
      <MazakaLayout
        title={"Onay Bekleyen Sözleşme Listesi"}
        headDescription={"Onay Bekleyen Sözleşme  sayfası, onay bekleyen tüm sözleşmelerin listesini görüntülemenizi sağlar"}
      >
        <Row gutter={[20, 20]} className="!mt-4">
          <Col span={24} >
            <TabModes/>
          </Col>
          <Col span={24} >
          <Row>
            <Col
            xs={24}
            md={12}
            >
              <TopFilterOptions type="waitApproveFilter" filter={waitApproveFilter} />
            </Col>
        

          </Row>
          </Col>
          <Col span={24}>
            <ContractTableList />
          </Col>
        </Row>
      </MazakaLayout>

    

      
    </>
  );
};

export default WaitApproveIndex;
