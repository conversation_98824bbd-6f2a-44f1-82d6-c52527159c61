import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaSelect } from "apps/Common/MazakaSelect";

import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Contract/EndPoints";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { approvedContractWithPut, rejectContractWithPut,  } from "apps/Contract/Services";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";


interface RedirectAnotherStationProps {
  onFinish?: any;
  selectedRecord?: any;

}

const ChangeStatusContract: FC<RedirectAnotherStationProps> = ({
  onFinish,
 

}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const statusOptions = [
    {label:"Onayla",value:1},
    {label:"Reddet",value:2},
  ]
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
   
    const status = formValues["IsApproved"]

    try {
      status ===1?await approvedContractWithPut(contractData.Id,formValues["ApprovedNote"]||""):await rejectContractWithPut(contractData.Id,formValues["ApprovedNote"]||"")
    
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getContractListFilter,
        exact: false,
      });
      queryClient.resetQueries({
        queryKey: endpoints.getContract,
        exact: false,
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ChangeTransactionStatus");
    }
  };
  const { contractData } = useSelector((state: RootState) => state.contract);



  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
         
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
          <MazakaSelect
              name="IsApproved"
              label={"Durum"}
              placeholder="Durum"
              xs={24}
              className="!m-0"
              options ={statusOptions}
              rules={[{ required: true, message: "" }]}
            />
          <MazakaTextArea
              name="ApprovedNote"
              label={"Açıklama"}
              placeholder="Açıklama"
              xs={24}
              className="!m-0"
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ChangeStatusContract;
