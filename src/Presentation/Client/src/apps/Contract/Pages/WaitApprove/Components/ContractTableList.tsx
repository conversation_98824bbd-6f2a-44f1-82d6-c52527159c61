import { EditOutlined, EllipsisOutlined, EyeOutlined } from "@ant-design/icons";
import { Col, Dropdown, Modal, Spin, Table, Tooltip, Typography } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetContracts } from "apps/Contract/ServerSideStates";
import {
  hanldleSetContractData,
  hanldleSetContractFilter,
} from "apps/Contract/ClientSideStates";
import { lazy, Suspense, useState } from "react";
import { ItemType } from "antd/es/menu/interface";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";
import dayjs from "dayjs";
const AddOrUpdateIndex = lazy(
  () =>
    import("apps/Contract/Pages/All/components/AddOrUpdate/AddOrUpdateIndex")
);
const ChangeStatusContract = lazy(
  () =>
    import("./ChangeStatusContract")
);

const ContractTableList = () => {
  const { waitApproveFilter } = useSelector(
    (state: RootState) => state.contract
  );
  const [isShowEditDrawer, setIsShowEditDrawer] = useState(false);
  const [isShowUpdateStatusModal, setIsShowUpdateStatusModal] = useState(false);
  const dispatch = useDispatch();
  const contracts = useGetContracts(waitApproveFilter);

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = {
      ...waitApproveFilter,
      PageIndex: pageNum,
      PageSize: pageSize,
    };
    dispatch(hanldleSetContractFilter({ filter: newFilter,type:"waitApproveFilter" }));
  };

  const { Text } = Typography;
  const columns = [
    {
      title: "Başlık",
      dataIndex: "Title",
      sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title)
    },
    {
      title: "Firma",
      dataIndex: ["Company", "Name"],
      sorter: (a: any, b: any) => a?.Company?.Name.localeCompare(b?.Company?.Name)
    },
    {
      title: "Başlangıç Tarihi",
      dataIndex: "StartDate",
      render: (text: any) => new Date(text).toLocaleDateString("tr-TR"),
      sorter: (a: any, b: any) =>
              dayjs(a?.StartDate).valueOf() - dayjs(b?.StartDate).valueOf(),
    },
    {
      title: "Bitiş Tarihi",
      dataIndex: "EndDate",
      render: (text: any) => new Date(text).toLocaleDateString("tr-TR"),
        sorter: (a: any, b: any) =>
              dayjs(a?.EndDate).valueOf() - dayjs(b?.EndDate).valueOf(),
    },
    {
      title: "Rapor",
      dataIndex: "ApprovedNote",
      key: "ApprovedNote",
      width: "15%",
      render: (value: string) => {
        if (value) {
          return (
            <>
              {value.length <= 30 ? (
                <>
                  <Text>{value}</Text>
                </>
              ) : (
                <>
                  <Tooltip title={value}>
                    <Text>{value.slice(0, 30)}</Text>
                  </Tooltip>
                </>
              )}
            </>
          );
        }
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];

  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await dispatch(hanldleSetContractData({ data: record }));
        setIsShowEditDrawer(true);
      },
      icon: <EyeOutlined />,
      label: "Detayları Gör",
    },
    {
      key: "2",
      onClick: async () => {
        await dispatch(hanldleSetContractData({ data: record }));
        setIsShowUpdateStatusModal(true);
      },
      icon: <EditOutlined />,
      label: "Durumu Güncelle",
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={contracts.isLoading || contracts.isFetching}
        dataSource={contracts.data ? contracts.data.Data : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: contracts.data?.FilteredCount || 0,
          current: contracts.data?.PageIndex,
          pageSize: contracts.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <Modal
        title={"Sözleşmenin durumunu değiştir"}
        footer={false}
        open={isShowUpdateStatusModal}
        onCancel={() => {
          setIsShowUpdateStatusModal(false);
        }}
      >
        {
          isShowUpdateStatusModal&&
          <Suspense fallback={<Spin/>}>
            <ChangeStatusContract
              onFinish={() => {
                setIsShowUpdateStatusModal(false);
              }}
            />

          </Suspense>
        }
      </Modal>
      <MazakaDrawer
        title="Sözleşmeyi Güncelle"
        placement="right"
        open={isShowEditDrawer}
        toggleVisible={() => {
          setIsShowEditDrawer(!isShowEditDrawer);
        }}
        layoutType="strecth"
      >
        <>
          {isShowEditDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateIndex />
            </Suspense>
          )}
        </>
      </MazakaDrawer>
    </Col>
  );
};

export default ContractTableList;
