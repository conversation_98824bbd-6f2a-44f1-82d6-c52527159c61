import { Col, Tag } from "antd";
import { useGetContract } from "apps/Contract/ServerSideStates";
import { FC } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";

interface AdminContractApprovedStatusProps{
    xs?:number,
    lg?:number
}

const AdminContractApprovedStatus:FC<AdminContractApprovedStatusProps> = ({
    xs,
    lg
}) => {
  const { contractData } = useSelector((state: RootState) => state.contract);
  const contractDetails: any = useGetContract(contractData.Id);
  return (
    <>
      {contractDetails.data?.Value && (
        <>
          <Col xs={xs} lg={lg} className="">
            {contractDetails.data?.Value?.PaymentPlanId === 5 && (
              <>
                <Tag
                  color={
                    contractDetails.data?.Value.IsApproved 
                      ? "green"
                      :  contractDetails.data?.Value.IsApproved===false 
                      ? "error"
                      : "warning"
                  }
                >
                  {contractDetails.data?.Value.IsApproved
                 
                    ? contractDetails.data?.Value.ApprovedNote ||
                      "Seçilen Ödeme planı onaylandı"

                    : contractDetails.data?.Value.IsApproved===false
                    ? contractDetails.data?.Value.ApprovedNote
                    : "Onay Bekleniyor"}
                </Tag>
              </>
            )}
          </Col>
        </>
      )}
    </>
  );
};

export default AdminContractApprovedStatus;
