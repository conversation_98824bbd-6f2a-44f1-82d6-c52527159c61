import { Form, Row } from "antd";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import { FC, useEffect } from "react";
import { useDispatch,} from "react-redux";
import { hanldleSetContractFilter } from "../../../ClientSideStates";

interface TopFilterOptionsProps{
  type:"all"|"waitApproveFilter"
  filter:any,
}

const TopFilterOptions:FC<TopFilterOptionsProps> = ({type,filter}) => {
  const [form] = Form.useForm();

  const dispatch = useDispatch();

  const handleOnChangeCompany = (value: string[]) => {
    let currentFilter = { ...filter };
    if (value) {
      currentFilter["CompanyId"] = value;
    } else {
      delete currentFilter["CompanyId"];
    }
      dispatch(hanldleSetContractFilter({ filter: currentFilter,type }));
    
   
  };

  useEffect(() => {
    form.setFieldValue("CompanyId", filter?.CompanyId);
  }, [filter]);

  return (
    <>
      <Form form={form}>
        <Row gutter={[10, 10]}>
          <GeneralCompanies
            name="CompanyId"
            placeholder="Firma"
            className="!m-0"
            xs={24}
            md={12}
            allowClear={true}
            onChange={handleOnChangeCompany}
          />
        </Row>
      </Form>
    </>
  );
};

export default TopFilterOptions;
