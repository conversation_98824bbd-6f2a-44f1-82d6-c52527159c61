import { Col, Row, Typography } from "antd";
import GeneralProductInput from "apps/Common/GeneralProductInput";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import MazakaInputNumber from "apps/Common/MazakaInputNumber";
import { useGetContractProducts } from "apps/Contract/ServerSideStates";
import {
  addContractProductList,
  updateContractProductList,
  updateContractProductWithPut,
} from "apps/Contract/Services";

import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import { useSelector } from "react-redux";
import endpoints from "apps/Contract/EndPoints";
import { RootState } from "store/Reducers";
import { FormInstance } from "antd/lib";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import GeneralConsistencyInput from "apps/Common/GeneralConsistencyClassInput";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import { MazakaDatePicker } from "apps/Common/MazakaDatePicker";
import dayjs from "dayjs";
import { MazakaSwitch } from "apps/Common/MazakaSwitch";
import { openNotificationWithIcon } from "helpers/Notifications";

interface ContractProductFormProps {
  isEditMode: boolean;
  setIsEditMode: any;
  selectedRecord: any;
  form: FormInstance;
}

const ContractProductForm: FC<ContractProductFormProps> = ({
  selectedRecord,
  isEditMode,
  setIsEditMode,
  form,
}) => {
  const queryClient = useQueryClient();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { contractData } = useSelector((state: RootState) => state.contract);
  const { userInfo } = useSelector((state: RootState) => state.account);
  
  // Muhasebeci rolü kontrolü
  const isAccounterRole = userInfo?.RoleId === "832735cd-3bdb-42c6-9501-cf34f71890a9";

  const contractProducts = useGetContractProducts(
    contractData
      ? {
        PageSize: -1,
        ContractId: contractData["Id"],
        IncludeProperties: ["Product"],
      }
      : undefined
  );
  let currentProductList = contractProducts.data
    ? contractProducts.data.Data
    : [];
  const handleAddProductToContract = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    let productList = formValues["ProductObjList"] || [];
    formValues["Amount"] = formValues["Amount"] || 0
    let unSavedData: any = [];
    let savedData: any = [];

    productList.forEach(
      (item: { key: string; label: string; value: string }) => {
        let obj = {
          ContractId: contractData["Id"],
          ProductId: item.value,
          Amount: formValues["Amount"],
          OldAmount: formValues["OldAmount"] || 0,
          ContractProductConsistencyClass: formValues["ConsistencyClass"]?.map(
            (consistencyId: any) => {
              return {
                ConsistencyClassId: consistencyId,
                ContractProductId: item.value,
              };
            }
          ),
          ContractProductBuilding: formValues["BuildingIds"].map(
            (buildingId: string) => {
              return {
                ContractProductId: contractData.Id,
                BuildingId: buildingId,
              };
            }
          ),
          Price: formValues["Price"],
          PriceWithoutPlug: formValues["PriceWithoutPlug"] ?? null,
          PriceUnderPowerPlant: formValues["PriceUnderPowerPlant"] ?? null,
          PriceGuaranteeDate: dayjs(formValues["PriceGuaranteeDate"]).format(
            "YYYY-MM-DD"
          ),
        };
        let findObj = currentProductList.find(
          (currentItem: any) => currentItem.ProductId === item.value
        );

        if (findObj) {
          const allObj = { ...findObj, ...obj };
          allObj["ContractProductBuilding"].map((item: any) => {
            return {
              BuildingId: item.BuildingId,
              ContractProductId: allObj.Id,
            };
          });

          delete allObj["Product"];
          savedData.push(allObj);
        } else {
          unSavedData.push(obj);
        }
      }
    );

    try {
      if (unSavedData.length > 0) {
        await addContractProductList(unSavedData);
      }
      if (savedData.length > 0) {
        await updateContractProductList(savedData);
      }
      mazakaForm.setSuccess(2000, () => "İşlem Başarılı");
      refetch();
      form.resetFields();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ContractProduct");
    }
  };

  const refetch = () => {
    queryClient.resetQueries({
      queryKey: endpoints.getContractProductListFilter,
      exact: false,
    });
  };

  const handleUpdateContractProduct = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    const data = { ...selectedRecord };
    delete data["Product"];
    delete data["Contract"];

    formValues["ContractProductConsistencyClass"] = formValues[
      "ConsistencyClass"
    ]?.map((consistencyId: any) => {
      return {
        ConsistencyClassId: consistencyId,
        ContractProductId: selectedRecord.Id,
      };
    });

    formValues["ContractProductBuilding"] = formValues["BuildingIds"].map(
      (buildingId: string) => {
        return {
          ContractProductId: selectedRecord.Id,
          BuildingId: buildingId,
        };
      }
    );

    delete formValues["BuildingIds"];
    try {
      await updateContractProductWithPut({ ...data, ...formValues });
      mazakaForm.setSuccess(2000, () => "İşlem Başarılı");
      setIsEditMode(false);
      form.resetFields();
      refetch();
    } catch (error: any) {
      mazakaForm.setFailed(
        2000,
        error?.Message ? error.Message : "İşlem başarısız"
      );

      console.log(
        "Something went wrong during the deletion of installment",
        error
      );
    }
  };
  const handleOnFinish = async () => {
    // Bu role sahipse işlem yapmasını engelle
    if (isAccounterRole) {
      openNotificationWithIcon("error", "Bu işlemi gerçekleştirme yetkiniz bulunmamaktadır.");
      return;
    }

    if (isEditMode) {
      handleUpdateContractProduct();
    } else {
      handleAddProductToContract();
    }
  };

  useEffect(() => {
    const data = { ...selectedRecord };
    data["PriceGuaranteeDate"] = dayjs(data["PriceGuaranteeDate"]);
    data["BuildingIds"] =
      data?.ContractProductBuilding?.map((item: any) => item.BuildingId) || [];
    data["ConsistencyClass"] =
      data["ContractProductConsistencyClass"]?.map(
        (item: any) => item?.ConsistencyClassId
      ) || [];
    form.setFieldsValue({ ...data });
  }, [selectedRecord]);

  const { Text } = Typography;
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
          initialValues={{ Active: true, IncludeTax: true }}
        >
          <Row gutter={[10, 20]}>
            <GeneralProductInput
              name={isEditMode ? "ProductId" : "ProductObjList"}
              label={"Ürün"}
              placeholder={"Ürün"}
              className="!m-0"
              xs={24}
              lg={6}
              disabled={isAccounterRole}
              mode={isEditMode ? undefined : "multiple"}
              onChange={(value: string, objList: any) => {
                if (!isEditMode && !isAccounterRole) {
                  form.setFieldValue("ProductObjList", objList);
                }
              }}
              rules={[{ required: true, message: "" }]}
            />
            <GeneralConsistencyInput
              name={"ConsistencyClass"}
              label={"Kıvam Sınıfı"}
              placeholder={"Kıvam Sınıfı"}
              className="!m-0"
              xs={24}
              lg={6}
              disabled={isAccounterRole}
              mode="multiple"
              rules={[{ required: true, message: "" }]}
            />
            <GeneralBuildingInput
              name="BuildingIds"
              placeholder="Şantiyeler"
              label="Şantiyeler"
              className="!m-0"
              xs={24}
              lg={6}
              disabled={isAccounterRole}
              allowClear={true}
              mode="multiple"
              externalValueId={contractData?.CompanyId}
            />
            <MazakaInputNumber
              name={"Amount"}
              label={"Toplam Miktar"}
              placeholder={"Miktar"}
              className="!m-0"
              suffix="m³"
              xs={24}
              lg={6}
              disabled={isAccounterRole}
            />
            <MazakaInputNumber
              name={"Price"}
              label={"Sözleşme Fiyatı"}
              placeholder={"Sözleşme Fiyatı"}
              className="!m-0"
              suffix="TL"
              xs={24}
              lg={6}
              disabled={isAccounterRole}
              rules={[{ required: true, message: "" }]}
            />
            <MazakaInputNumber
              name={"PriceWithoutPlug"}
              label={"Fişsiz Fiyatı"}
              placeholder={"Fişsiz Fiyatı"}
              className="!m-0"
              suffix="TL"
              xs={24}
              lg={6}
              disabled={isAccounterRole}
            />

             <MazakaInputNumber
              name={"PriceUnderPowerPlant"}
              label={"Santral Teslim Fiyatı"}
              placeholder={"Santral Teslim Fiyatı"}
              className="!m-0"
              suffix="TL"
              xs={24}
              lg={6}
              disabled={isAccounterRole}
            />
            <MazakaSwitch
              xs={24}
              lg={6}
              label="KDV Dahil"
              name={"IncludeTax"}
              disabled={isAccounterRole}
            />

            <MazakaInputNumber
              name={"OldAmount"}
              label={"Kullanılan Miktar"}
              placeholder={"Kullanılan Miktar"}
              className="!m-0"
              suffix="m³"
              xs={24}
              lg={6}
              disabled={isAccounterRole}
            />
            <MazakaDatePicker
              name={"PriceGuaranteeDate"}
              label={"Fiyatın Garanti Tarihi"}
              xs={24}
              lg={6}
              disabled={isAccounterRole}
              rules={[{ required: true, message: "" }]}
            />

            {!isAccounterRole && (
              <Col xs={24} className="!flex items-end gap-2">
                <MazakaButton
                  htmlType="submit"
                  processType={formActions.submitProcessType}
                >
                  {isEditMode ? "Güncelle" : "Ekle"}
                </MazakaButton>
                {isEditMode && (
                  <MazakaButton
                    onClick={() => {
                      setIsEditMode(false);
                      form.resetFields();
                    }}
                    type="primary"
                    className="!flex items-center !bg-red-400 !text-white"
                  >
                    Vazgeç
                  </MazakaButton>
                )}
              </Col>
            )}
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ContractProductForm;
