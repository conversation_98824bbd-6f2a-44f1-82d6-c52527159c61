import MazakaCard from "apps/Common/MazakaCart";
import ContractProductForm from "./ContractProductForm";
import ContractProductList from "./ContractProductList";
import { useState } from "react";
import { Form } from "antd";

const ContractProductIndex = () => {


  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedRecord,setSelectedRecord] = useState<any |null>(null)
  const [form] = Form.useForm();
  return (
    <>
      <MazakaCard
        title="Ürünler"
        titlePosition="Left"
        gutter={[20, 20]}
      >
        <ContractProductForm  isEditMode={isEditMode} form={form}
        selectedRecord={selectedRecord}
          setIsEditMode={setIsEditMode}/>
          
        <ContractProductList
          setSelectectedRecord ={setSelectedRecord}
          isEditMode={isEditMode}
          setIsEditMode={setIsEditMode}
          form={form}
        />
      </MazakaCard>
    </>
  );
};

export default ContractProductIndex;
