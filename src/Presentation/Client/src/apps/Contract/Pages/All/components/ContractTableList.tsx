import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { Col, Dropdown, Modal, Spin, Table, Tag, Typography } from "antd";
import { ItemType } from "antd/es/menu/interface";
import endpoints from "apps/Contract/EndPoints";
import { openNotificationWithIcon } from "helpers/Notifications";
import { lazy, Suspense, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetContracts } from "apps/Contract/ServerSideStates";
import { useQueryClient } from "react-query";
import {
  hanldleSetContractData,
  hanldleSetContractFilter,
} from "apps/Contract/ClientSideStates";
import { MazakaBull } from "apps/Common/MazakaBull";
import { deleteContract, sendEmailToCustomer } from "apps/Contract/Services";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";

import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";
import { useGetUsers } from "apps/User/ServerSideStates";
const AddOrUpdateIndex = lazy(
  () => import("./AddOrUpdate/AddOrUpdateIndex")
);

const ContractTableList = () => {
  const queryClient = useQueryClient();
  const [isShowEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const { contractFilter } = useSelector((state: RootState) => state.contract);
  const { userInfo } = useSelector((state: RootState) => state.account);
  const dispatch = useDispatch();
  const contracts = useGetContracts(contractFilter);
  const { Text } = Typography;

  const tableItemDropdownMenu = (record: any): ItemType[] => {
    const menuItems = [
      {
        key: "1",
        onClick: async () => {
          await dispatch(hanldleSetContractData({ data: record }));
          setIsEditDrawer(true);
        },
        icon: <EditOutlined />,
        label: userInfo.RoleId === "832735cd-3bdb-42c6-9501-cf34f71890a9" ? "Detayları Gör" : "Güncelle",
      }
    ];

    // Muhasebeci rolü değilse diğer işlemleri ekle
    if (userInfo?.RoleId !== "832735cd-3bdb-42c6-9501-cf34f71890a9") {
      menuItems.push(
        {
          key: "2",
          onClick: async () => {
            confirm(record);
          },
          icon: <DeleteOutlined />,
          label: "Sil",
        },
        {
          key: "3",
          onClick: async () => {
            sendCustomerConfirm(record);
          },
          icon: <UserOutlined />,
          label: "Müşteriye E-Posta Gönder",
        }
      );
    }

    return menuItems;
  };

  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteContract(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getContractListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "Contract", true);
        }
      },
    });
  };

  const sendCustomerConfirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Sözleşmenizin içeriği e-posta ile müterinize gönderilecek.Onaylıyor musunuz? `,
      okText: "Gönder",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await sendEmailToCustomer(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getContractListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "Contract", true);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = {
      ...contractFilter,
      PageIndex: pageNum,
      PageSize: pageSize,
    };
    dispatch(hanldleSetContractFilter({ filter: newFilter,type:"all" }));
  };

  const columns = [
   
    {
      title: "Başlık",
      dataIndex: "Title",
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{value}</Text>
            </div>
            <>
              {users.isLoading || users.isFetching ? (
                <>
                  <Spin />
                </>
              ) : (
                <>
                  {(() => {
                    if (userData) {
                      const findUser = userData.find(
                        (item: any) => item.Id === record.InsertUserId
                      );
                      if (findUser) {
                        return (
                          <Text className="!text-primary">
                            {findUser?.Name || ""}
                          </Text>
                        );
                      }
                      return <></>;
                    }
                  })()}
                </>
              )}
            </>
          </>
        );
      },
      sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title)
    },
    {
      title: "Firma",
      dataIndex: ["Company", "Name"],
      width: "25%",
      sorter: (a: any, b: any) => a?.Company?.Name.localeCompare(b?.Company?.Name)
    },

    {
      title: "Başlangıç Tarihi",
      dataIndex: "StartDate",
      render: (text: any) => new Date(text).toLocaleDateString("tr-TR"),
      sorter: (a: any, b: any) =>
        dayjs(a?.StartDate).valueOf() - dayjs(b?.StartDate).valueOf(),
    },
    {
      title: "Bitiş Tarihi",
      dataIndex: "EndDate",
      render: (text: any) => new Date(text).toLocaleDateString("tr-TR"),
      sorter: (a: any, b: any) =>
        dayjs(a?.EndDate).valueOf() - dayjs(b?.EndDate).valueOf(),
    },
    {
      title: "Kalan Gün Sayısı",

      render: (_: string, record: any) => {
        if (record.StartDate && record.EndDate) {
          return `${dayjs(record.EndDate)
            .diff(record.StartDate, "days")
            .toString()}`;
        }
        return <></>;
      },
      sorter: (a: any, b: any) => {
        const aDays = a.StartDate && a.EndDate
          ? dayjs(a.EndDate).diff(dayjs(a.StartDate), "days")
          : -Infinity;
        const bDays = b.StartDate && b.EndDate
          ? dayjs(b.EndDate).diff(dayjs(b.StartDate), "days")
          : -Infinity;
        return aDays - bDays;
      },
    },
    {
      title: "Onaylama Durumu",
      dataIndex: "IsApproved",
      render: (status: any,record:any) => {
        return (
          <>
          <div>
          <Tag
            color={`${
              status ? "green" : status === null ? "warning" : "error"
            }`}
          >
            {status
              ? "Onaylandı"
              : status === null
              ? "Onay Bekliyor"
              : "Reddildi"}
          </Tag>

          </div>
          <div>
          <>
              {users.isLoading || users.isFetching ? (
                <>
                  <Spin />
                </>
              ) : (
                <>
                  {(() => {
                    if (userData) {
                      const findUser = userData.find(
                        (item: any) => item.Id === record.UpdatedUserId
                      );
                      if (findUser) {
                        return (
                          <>
                          <div>

                          <Text className="!text-primary">
                            {findUser?.Name || ""}
                          </Text>
                          </div>
                          <div>
                            <Text>{dayjs(record?.UpdateDate).format("DD.MM.YYYY")}</Text>
                          </div>
                          <div>
                            <Text>{dayjs(record?.UpdateDate).format("HH:mm")}</Text>
                          </div>
                          </>
                         
                        );
                      }
                      return <></>;
                    }
                  })()}
                </>
              )}
            </>
          </div>
          </>
        );
      },
   
    },
    {
      title: "Durum",
      dataIndex: "Active",
      render: (status: boolean) => {
        return <MazakaBull type={status ? "Success" : "Error"} />;
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];

  const rowClassName = (record: any) => {
    const startDate = dayjs(record.StartDate);
    const endDate = dayjs(record.EndDate);
    const daysDiff = endDate.diff(startDate, "day");

    return daysDiff <= 30 ? "error-row" : "";
  };
  const users = useGetUsers({ PageSize: -1 });
  const userData = users.data ? users.data.Data : [];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        rowClassName={rowClassName}
        loading={contracts.isLoading || contracts.isFetching}
        dataSource={contracts.data ? contracts.data.Data : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: contracts.data?.FilteredCount || 0,
          current: contracts.data?.PageIndex,
          pageSize: contracts.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <MazakaDrawer
        title="Sözleşmeyi Güncelle"
        placement="right"
        open={isShowEditDrawer}
        toggleVisible={() => {
          setIsEditDrawer(!isShowEditDrawer);
        }}
        layoutType="strecth"
      >
       <>
       {
        isShowEditDrawer&&
        <Suspense fallback={<Spin/>}  >

          <AddOrUpdateIndex />
        </Suspense>
       }
       </>
      </MazakaDrawer>
    </Col>
  );
};

export default ContractTableList;
