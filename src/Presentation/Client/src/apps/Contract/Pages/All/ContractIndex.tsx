import { PlusOutlined } from "@ant-design/icons";
import { Button, Col, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { useState, lazy, Suspense } from "react";
import ContractTableList from "./components/ContractTableList";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";
import { useDispatch, useSelector } from "react-redux";
import { hanldleSetContractData } from "../../ClientSideStates";
import TopFilterOptions from "./components/TopFilterOptions";
import { RootState } from "store/Reducers";
const AddOrUpdateIndex = lazy(
  () => import("./components/AddOrUpdate/AddOrUpdateIndex")
);

const AllContractIndex = () => {
  const { Text } = Typography;

  const dispatch = useDispatch();

  const [isShowAddContractDrawer, setIsShowAddContractDrawer] = useState(false);
  const { contractFilter } = useSelector((state: RootState) => state.contract);

  return (
    <>
      <MazakaLayout
        title={"Sözleşme Listesi"}
        headDescription={
          "Sözleşme sayfası, sistemde tüm kayıtlı sözleşmelerin listesini görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]} className="!mt-4">
          <Col span={24}>
            <Row>
              <Col xs={24} md={12}>
                <TopFilterOptions type="all" filter={contractFilter} />
              </Col>
              <Col
                span={24}
                xs={24}
                md={12}
                className="!flex justify-end gap-2"
              >
                <Button
                  onClick={async () => {
                    await dispatch(hanldleSetContractData({ data: null }));
                    setIsShowAddContractDrawer(true);
                  }}
                  className="!flex items-center"
                  type="primary"
                >
                  <div className="!flex items-center gap-1">
                    <PlusOutlined />
                    <Text className="!text-white">Sözleşme Ekle</Text>
                  </div>
                </Button>
              </Col>
            </Row>
          </Col>
          <Col span={24}>
            <ContractTableList />
          </Col>
        </Row>
      </MazakaLayout>

      <MazakaDrawer
        title="Yeni Sözleşme Ekle"
        placement="right"
        open={isShowAddContractDrawer}
        toggleVisible={() => {
          setIsShowAddContractDrawer(!isShowAddContractDrawer);
        }}
        layoutType="strecth"
      >
        <>
          {isShowAddContractDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateIndex />
            </Suspense>
          )}
        </>
      </MazakaDrawer>
    </>
  );
};

export default AllContractIndex;
