import {
  Col,
  message,
  Row,
  Typography,
  Upload,
  Modal,
  Skeleton,
  Form,
} from "antd";
import {  useEffect, useState } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Contract/EndPoints";
import {
  DeleteOutlined,
  EyeOutlined,
  FileOutlined,
  InboxOutlined,
} from "@ant-design/icons";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { openNotificationWithIcon } from "helpers/Notifications";
import { useGetContractFileUpload } from "apps/Contract/ServerSideStates";
import {
  addContractFileUpload,
  deleteContractFile,
} from "apps/Contract/Services";
import MazakaCard from "apps/Common/MazakaCart";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { MazakaForm } from "apps/Common/MazakaForm";

import useMazakaForm from "hooks/useMazakaForm";
import { MazakaButton } from "apps/Common/MazakaButton";

const AddOrUpdateFileUpload = () => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const [fileList, setFileList] = useState<any[]>([]);
  const { Text } = Typography;
  const { contractData } = useSelector((state: RootState) => state.contract);
  const contractFiles = useGetContractFileUpload({
    ContractId: contractData?.Id,
    PageSize: -1,
  });

  useEffect(() => {
    if (contractFiles.data) {
      const files = contractFiles.data ? contractFiles.data.Data : [];

      let currentFiles = files.map((item: any) => ({
        ...item,
        name: item.FileName,
        isSaved: true,
      }));

      setFileList((prev: any) => currentFiles);
    }
  }, [contractFiles.data]);

  const validateFile = (file: any) => {
    const isValidSize = file.size <= 2000000;
    if (!isValidSize) {
      message.error("Dosya Boyutu en fazla 2MB olmalı");
      return false;
    }
    return true;
  };

  const beforeUpload = (file: any) => {
    const isValid = validateFile(file);
    if (isValid) {
      setFileList((prevFileList: any) => [...prevFileList, file]);
    }
    return false;
  };

  const handleDeleteFile = (item: any) => {
    Modal.confirm({
      title: "Bu dosyayı silmek istediğinizden emin misiniz?",
      okText: "Evet",
      cancelText: "Hayır",
      onOk: async () => {
        try {
          if (item.Id) {
            await deleteContractFile(item);
            openNotificationWithIcon("success", "Dosya başarıyla silindi.");
            queryClient.resetQueries({
              queryKey: endpoints.getFileUploadListFilter,
              exact: false,
            });
          }
          setFileList((prevFileList: any) =>
            prevFileList.filter((file: any) => file.name !== item.name)
          );
        } catch (error: any) {
          showServiceErrorMessage(error, undefined, "DeleteCertificate", true);
        }
      },
    });
  };

  const { Dragger } = Upload;

  const handleOnFinish = async () => {

    let unSavedFiles = fileList.filter((item:any)=>!item.isSaved)
   
    if (unSavedFiles.length<=0) {
      message.error("Yeni Bir Dosya Seçiniz");
      return false;
    }

    mazakaForm.setLoading();

    try {
      let data = fileList.map((item: any) => ({
        File: item,
        ContractId: contractData.Id,
      }));


      await Promise.all(
        data.map(async (item) => {
          await addContractFileUpload(item);
        })
      );

      queryClient.resetQueries({
        queryKey: endpoints.getFileUploadListFilter,
        exact: false,
      });

      mazakaForm.setSuccess(2000, () => "İşlem Başarılı");
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "AddCertificate");
    }
  };

  return (
    <MazakaCard span={24} title="Dosya Ekle" titlePosition="Left" gutter={[20, 20]}>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
          initialValues={{ Active: true }}
          {...formActions}
        >

      <Row gutter={[10, 10]}>
        <Col xs={24}>
          <Dragger
            accept=".pdf"
            beforeUpload={beforeUpload}
            customRequest={() => {}}
            multiple={true}
            fileList={fileList}
            showUploadList={false}
            className="!w-full"
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">Dosya Yükle</p>
            <p className="ant-upload-hint">
              Sertifikanizi buradan yükleyebilirsiniz
            </p>
          </Dragger>
        </Col>
        
        <Col xs={24}>
          <Skeleton
            loading={contractFiles.isLoading || contractFiles.isFetching}
          >
            {fileList.map((item) => (
              <div key={item.name} className="!flex justify-between">
                <div className="!flex items-center gap-1">
                  <FileOutlined className="!text-indigo-500" />
                  <Text>{item.name}</Text>
                </div>
                <div className="!flex gap-2">
                  <DeleteOutlined
                    className="!text-red-600"
                    onClick={() => handleDeleteFile(item)}
                  />
                  {item?.Id && (
                    <>
                      <EyeOutlined
                        className="!text-indigo-600"
                        onClick={() => {
                          window.open(
                            `${
                              process.env.NODE_ENV === "development"
                                ? process.env
                                    .REACT_APP_DEVELOPMENT_API_BASE_MEDIA_DOWNLOAD
                                : window.location.origin
                            }/uploads/ContractFile/${item?.FileName}`
                          );
                        }}
                      />
                    </>
                  )}
                </div>
              </div>
            ))}
          </Skeleton>
        </Col>
         <Col xs={24} className="!flex justify-end" >
                      <MazakaButton htmlType="submit"  processType={formActions.submitProcessType}>
                       Dosya Ekle
                      </MazakaButton>
                    </Col>
      </Row>
        </MazakaForm>
      </Col>
    </MazakaCard>
  );
};

export default AddOrUpdateFileUpload;
