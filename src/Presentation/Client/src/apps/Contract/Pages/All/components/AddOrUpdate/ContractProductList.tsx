import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Col, Dropdown, Modal, Table, Tag, Typography } from "antd";
import { ItemType } from "antd/es/menu/interface";
import { useGetContractProducts } from "apps/Contract/ServerSideStates";
import { openNotificationWithIcon } from "helpers/Notifications";
import { FC } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Contract/EndPoints";
import { deleteContractProduct } from "apps/Contract/Services";
import { FormInstance } from "antd/lib";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import PriceFormatter from "apps/Common/PriceFormatter";

interface ContractProductListProps {
  setIsEditMode: any;
  isEditMode: boolean;
  setSelectectedRecord: any;
  form: FormInstance;
}
const ContractProductList: FC<ContractProductListProps> = (props) => {
  const queryClient = useQueryClient();
  const { contractData } = useSelector((state: RootState) => state.contract);
  const { Text } = Typography;
  const columns = [
    {
      title: "Ürün",
      dataIndex: ["Product", "Name"],
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{value}</Text>
            </div>
            {
              record?.ContractProductConsistencyClass?.length > 0 &&
              <>
                <div className="!flex !flex-wrap " >
                  {
                    record?.ContractProductConsistencyClass?.map((item: any, index: number) => {
                      return (
                        <span key={item?.ConsistencyClassId || index} >
                          <Tag color="green" >{item?.ConsistencyClass?.Name || ""}</Tag>
                        </span>
                      )
                    })
                  }
                  <Text>{record?.ConsistencyClass?.Name || ""}</Text>
                </div>

              </>
            }
            <div className="!flex !flex-wrap gap-1">
              {record?.ContractProductBuilding?.map((item: any) => {
                return (
                  <>
                    <Tag color="blue">{item?.Building?.Name}</Tag>
                  </>
                );
              })}
            </div>
          </>
        );
      },
      width: "20%",
    },

    {
      title: (
        <>
          <div>Birim Fiyatı(TL)</div>
          <div>Garanti Tarihi</div>
        </>
      ),
      dataIndex: "Price",
      render: (value: string, record: any) => {
        return (
          <div>


            {/* Diğer Fiyat Tipleri */}
            <div className="!mt-1 space-y-1">
              {record?.Price && (
                <div>
                  <Tag color="blue">
                    <span className="!text-[10px]">
                      Sözleşme: <PriceFormatter inputPrice={record.Price} />
                    </span>
                  </Tag>
                </div>
              )}

              {record?.PriceWithoutPlug && record.PriceWithoutPlug !== value && (
                <div>
                  <Tag color="green" >
                    <span className="!text-[10px]">
                      Fişsiz: <PriceFormatter inputPrice={record.PriceWithoutPlug} />
                    </span>
                  </Tag>
                </div>
              )}

              {record?.PriceUnderPowerPlant && record.PriceUnderPowerPlant !== value && (
                <div>
                  <Tag color="orange" >
                    <span className="!text-[10px]">
                      Santral Teslim: <PriceFormatter inputPrice={record.PriceUnderPowerPlant} />
                    </span>
                  </Tag>
                </div>
              )}
              {record?.PriceGuaranteeDate && (
                <div>
                  <Text>
                    {new Date(record?.PriceGuaranteeDate).toLocaleDateString("tr-TR")}
                  </Text>
                </div>
              )}
            </div>

            {record?.hasOwnProperty("TaxInclude") && (
              <div className="!mt-1">
                <Tag color={record?.TaxInclude ? "green" : "red"}>
                  <span className="!text-[10px]">
                    {record?.TaxInclude ? "KDV Dahil" : "KDV Dahil Değildir"}
                  </span>
                </Tag>
              </div>
            )}
          </div>
        );
      },
      width: "20%",
    },

    {
      title: "Toplam Miktar(m³)",
      dataIndex: "Amount",
      width: "10%",
    },
    {
      title: "Kullanılan Miktar(m³)",
      dataIndex: "OldAmount",
      width: "10%",
    },
    {
      title: "Tüketim",
      dataIndex: "OldAmount",
      width: "10%",
      render: (value: any, record: any) => {
        // OldPrice ve OldAmount'tan birim fiyat hesapla
        const unitPrice = record.OldPrice / record.OldAmount;

        // Hangi fiyat tipinin kullanıldığını belirle
        let priceType = "";
        let tagColor = "";

        if (unitPrice === record.Price) {
          priceType = "Sözleşme Fiyatından";
          tagColor = "blue";
        } else if (unitPrice === record.PriceWithoutPlug) {
          priceType = "Fişsiz Fiyatından";
          tagColor = "green";
        } else if (unitPrice === record.PriceUnderPowerPlant) {
          priceType = "Santral Teslim Fiyatından";
          tagColor = "orange";
        }

        return (
          <div>
            <div></div>
            <Tag color={tagColor} style={{ marginTop: 4 }}>
              {value} m³ {priceType}
            </Tag>
          </div>
        );
      }
    },
    {
      title: "Toplam",
      render: (_: string, record: any) => {
        let total = record?.OldAmount * record?.OldPrice;
        if (total) {
          return <PriceFormatter inputPrice={total} />;
        }
        return <></>;
      },
      width: "12%",
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await props.setSelectectedRecord(record);
        props.setIsEditMode(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteContractProduct(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getContractProductListFilter,
            exact: false,
          });
          if (props.isEditMode) {
            props.setIsEditMode(false);
            props.form.resetFields();
          }
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "ContractProduct", true);
        }
      },
    });
  };
  const contractProducts = useGetContractProducts(
    contractData
      ? {
        PageSize: -1,
        ContractId: contractData["Id"],
        IncludeProperties: [
          "Product",
          "ContractProductConsistencyClass.ConsistencyClass",
          "ContractProductBuilding.Building",
        ],
      }
      : undefined
  );
  return (
    <Col span={24}>
      <Table
        loading={contractProducts.isLoading || contractProducts.isFetching}
        columns={columns}
        dataSource={contractProducts.data ? contractProducts.data.Data : []}
        // dataSource={[]}
        pagination={false}
        scroll={{ x: 700 }}
      />
    </Col>
  );
};

export default ContractProductList;
