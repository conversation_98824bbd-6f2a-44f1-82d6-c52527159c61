import { Col, Form, Row } from "antd";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaRangePicker } from "apps/Common/MazakaRangePicker";
import { MazakaSwitch } from "apps/Common/MazakaSwitch";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { addContract, updateContractWithPut } from "apps/Contract/Services";
import useMazakaForm from "hooks/useMazakaForm";
import { useEffect } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Contract/EndPoints";
import MazakaCard from "apps/Common/MazakaCart";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetContractData } from "apps/Contract/ClientSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import AdminContractApprovedStatus from "./AdminContractApproveStatus";
import { MazakaButton } from "apps/Common/MazakaButton";
import MazakaTextEditor from "apps/Common/MazakaTextEditor";

const GeneralInfoes = () => {
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { userInfo } = useSelector((state: RootState) => state.account);
  const { contractData } = useSelector((state: RootState) => state.contract);
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    formValues["StartDate"] = dayjs(formValues["Date"][0]).format("YYYY-MM-DD");
    formValues["EndDate"] = dayjs(formValues["Date"][1]).format("YYYY-MM-DD");
    delete formValues["Date"];

    try {
      if (contractData) {
        const data = { ...contractData };
        delete data["Company"];

        await updateContractWithPut({
          ...data,
          ...formValues,
        });
      } else {
        formValues["InsertUserId"] = userInfo.Id;
        const response: any = await addContract(formValues);

        dispatch(hanldleSetContractData({ data: response.Value }));
      }

      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getContractListFilter,
        exact: false,
      });
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "Contract");
    }
  };
  useEffect(() => {
    if (contractData) {
      const startDate = contractData["StartDate"]
        ? dayjs(contractData["StartDate"])
        : null;
      const endDate = contractData["EndDate"]
        ? dayjs(contractData["EndDate"])
        : null;
      const dateRange = startDate && endDate ? [startDate, endDate] : [];

      form.setFieldsValue({
        Title: contractData["Title"],
        Description: contractData["Description"],
        Active: contractData["Active"],
        Date: dateRange,
        CompanyId: contractData["CompanyId"],
      });
    }
  }, [contractData, form]);

  return (
    <MazakaCard>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          initialValues={{ Active: true }}
          submitButtonVisible={false}
        >
          <Row gutter={[10, 10]}>
            <Col span={24}>
              <Row>
                {contractData && (
                  <>
                    <AdminContractApprovedStatus xs={24} lg={12} />
                  </>
                )}
                <MazakaSwitch
                  className="!m-0"
                  wrapperClassName="!flex justify-end"
                  xs={24}
                  lg={contractData ? 12 : 24}
                  label={"Durum"}
                  name={"Active"}
                  onChange={(status) => {
                    form.setFieldValue("Active", status);
                  }}
                />
              </Row>
            </Col>

            <MazakaInput
              className="!m-0"
              label={"Başlık"}
              placeholder="Başlık"
              name={"Title"}
              xs={24}
              lg={8}
              rules={[{ required: true, message: "" }]}
            />

            <GeneralCompanies
              name="CompanyId"
              label={"Firma"}
              placeholder="Firma"
              className="!m-0"
              xs={24}
              lg={8}
              rules={[{ required: true, message: "" }]}
            />

            <MazakaRangePicker
              name={"Date"}
              label="Tarih Aralığı"
              xs={24}
              lg={8}
              className="!m-0"
              rules={[{ required: true, message: "" }]}
            />
            {contractData && (
              <MazakaTextEditor
                name="Description"
                label={"Açıklama"}
                xs={24}
                className="!m-0"
              />
            )}
            <Col xs={24} className="!flex justify-end">
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                Sözleşme Detaylarını Ekle
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </MazakaCard>
  );
};

export default GeneralInfoes;
