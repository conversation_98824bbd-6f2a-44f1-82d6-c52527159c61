import { Row } from "antd";
import GeneralInfoes from "./GeneralInfoes";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { lazy } from "react";
const AddOrUpdateFileUpload= lazy(
  () => import("./AddOrUpdateFile")
);
const ContractProductIndex= lazy(
  () => import("./ContractProductIndex")
);
const PaymentMethod= lazy(
  () => import("./PaymentMethod")
);

const AddOrUpdateIndex = () => {
  const { contractData } = useSelector((state: RootState) => state.contract);
  return (
    <>
      <Row gutter={[20, 20]}>
        <GeneralInfoes />
        {contractData && (
          <>
           <AddOrUpdateFileUpload  />
            <ContractProductIndex />
            <PaymentMethod/>
           
          </>
        )}
      </Row>
    </>
  );
};

export default AddOrUpdateIndex;
