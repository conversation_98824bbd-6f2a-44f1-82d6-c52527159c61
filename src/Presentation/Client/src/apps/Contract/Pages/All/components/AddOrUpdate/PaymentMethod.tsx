import { InfoCircleOutlined } from "@ant-design/icons";
import { Col, Form, Row, Tag, Typography } from "antd";
import GeneralContractPaymentPlanInput from "apps/Common/GeneralContractPaymentPlanInput";
import { MazakaButton } from "apps/Common/MazakaButton";
import MazakaCard from "apps/Common/MazakaCart";
import { MazakaForm } from "apps/Common/MazakaForm";
import {
  useGetContract,
  useGetContractProducts,
} from "apps/Contract/ServerSideStates";
import { updateContractWithPut } from "apps/Contract/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import endpoints from "apps/Contract/EndPoints";
import { useQueryClient } from "react-query";
import MazakaInputNumber from "apps/Common/MazakaInputNumber";
import { MazakaSwitch } from "apps/Common/MazakaSwitch";
import ContractAmountInput from "apps/Common/ContractAmountInputProps";

const PaymentMethod = () => {
  const queryClient = useQueryClient();
  const { Text } = Typography;
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const [planFilterData, setPlanFilterData] = useState([5]);
  const [selectedType, setSelectedType] = useState<string>("amount");



  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    const contractDetails = { ...contractData };
    contractDetails["PaymentPlanId"] = formValues["PaymentPlanId"];
    contractDetails["TotalWorth"] = formValues["TotalWorth"];
    contractDetails["LeftAmount"] = formValues["LeftAmount"]
    contractDetails["TotalAmount"] = formValues["TotalAmount"];


    delete contractDetails["PaymentPlan"];
    delete contractDetails["Company"];

    try {
      await updateContractWithPut(contractDetails);
      mazakaForm.setSuccess(1000, () => { }, "İşlem Başarılı");

      queryClient.resetQueries({
        queryKey: endpoints.getContractListFilter,
        exact: false,
      });
      queryClient.resetQueries({
        queryKey: endpoints.getContract,
        exact: false,
      });
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "PaymentPlanId");
    }
  };

  const { contractData } = useSelector((state: RootState) => state.contract);
  const contractDetails: any = useGetContract(contractData.Id);

  const contractProducts = useGetContractProducts(
    contractData
      ? {
        PageSize: -1,
        ContractId: contractData["Id"],
      }
      : undefined
  );

  useEffect(() => {
    if (contractProducts.data) {
      let total = 0;
      let allProducts = contractProducts.data ? contractProducts.data.Data : [];
      for (let item of allProducts) {
        total += item.Price * item.Amount;
      }
      if (total >= 10000000) {
        setPlanFilterData([]);
      } else {
        setPlanFilterData([5]);
      }
    }
  }, [contractProducts.data]);

  useEffect(() => {
    if (contractDetails.data) {
      form.setFieldsValue({
        PaymentPlanId: contractDetails?.data?.Value?.PaymentPlanId,
        TotalWorth: contractDetails?.data?.Value?.TotalWorth,
        TotalAmount: contractDetails?.data?.Value?.TotalAmount,
        LeftAmount: contractDetails?.data?.Value?.LeftAmount
      });
    }
  }, [contractDetails.data]);

  const getCurrentType = () => {
    if (contractDetails?.data?.Value?.TotalWorth && !contractDetails?.data?.Value?.TotalAmount) {
      return "amount";
    } else if (contractDetails?.data?.Value?.TotalAmount && !contractDetails?.data?.Value?.TotalWorth) {
      return "quantity";
    }
    return selectedType; // kullanıcının seçtiği type
  };

  const currentType = getCurrentType();

  return (
    <>
      <MazakaCard title="" titlePosition="Left" gutter={[20, 20]}>
        <Col>
          <MazakaForm
            form={form}
            onFinish={handleOnFinish}
            submitButtonVisible={false}
            initialValues={{ Active: true, IncludeTax: true }}
          >
            <Row gutter={[0, 20]}>
              <Col xs={24} lg={16}>
                <Row gutter={[10, 0]}>
                  <ContractAmountInput
                    label="Sözleşme Bilgisi"
                    placeholder="Değer girin"
                    inputNameAmount="TotalWorth"
                    inputNameQuantity="TotalAmount"
                    className="!flex"
                    selectedRecord={contractDetails?.data?.Value} // selectedRecord olarak gönderiyoruz
                    value={{
                      type: contractDetails?.data?.Value?.TotalWorth ? "amount" :
                        contractDetails?.data?.Value?.TotalAmount ? "quantity" : "amount",
                      amount: contractDetails?.data?.Value?.TotalWorth || contractDetails?.data?.Value?.TotalAmount
                    }}
                    onChange={(type, value) => {
                      if (type === "amount") {
                        form.setFieldValue("TotalWorth", value);
                        form.setFieldValue("TotalAmount", null);
                        setSelectedType("amount")
                      } else {
                        form.setFieldValue("TotalAmount", value);
                        form.setFieldValue("TotalWorth", null);
                        setSelectedType("quantity")
                      }
                    }}
                  />

                  {/* MazakaSwitch - sadece amount seçiliyse göster */}
                  {currentType === "amount" && (
                    <MazakaSwitch
                      xs={24}
                      lg={8}
                      label="KDV Dahil"
                      name={"IncludeTax"}
                      onChange={(staus: boolean) => {
                        form.setFieldValue("IncludeTax", staus);
                      }}
                    />
                  )}


                  <Col xs={24} className="!flex flex-col">
                    {contractDetails?.data?.Value?.TotalWorth && (
                      <>
                        <Text className="!font-bold text-black">
                          Kullanılan:{contractDetails?.data?.Value?.OldWorth}
                        </Text>
                        <Text className="!font-bold text-green-500">
                          Kalan:{contractDetails?.data?.Value?.LeftWorth}
                        </Text>
                      </>
                    )}
                    {contractDetails?.data?.Value?.TotalAmount && (
                      <>
                        <Text className="!font-bold text-black">
                          Kullanılan:{contractDetails?.data?.Value?.OldAmount}
                        </Text>
                        <Text className="!font-bold text-green-500">
                          Kalan:{contractDetails?.data?.Value?.LeftAmount}
                        </Text>
                      </>
                    )}

                  </Col>
                </Row>
              </Col>

              <Col xs={24}>
                <Text className="!font-bold">Ödeme Metodu</Text>
              </Col>

              <Col xs={24} md={16}>
                <Row gutter={[0, 5]}>
                  <Col span={24} className="!p-4  !bg-warning  rounded-md">
                    <div className="!flex items-center gap-2">
                      <InfoCircleOutlined className=" !text-indigo-500" />
                      <Text className="">Ödeme Seçenekleri</Text>
                    </div>
                    <div>
                      <Text className="!text-xs !font-bold">
                        Ay + 30 Gün seçeneğinde,sözleşme yönetici onayına
                        gönderilecektir.
                      </Text>
                    </div>
                  </Col>
                  <GeneralContractPaymentPlanInput
                    label={""}
                    name="PaymentPlanId"
                    xs={24}
                    placeholder="Ödeme Seçenekleri"
                    className="!m-0"
                    externalFilteredData={planFilterData}
                    rules={[{ required: true, message: "" }]}
                  />
                </Row>
              </Col>

              <Col xs={24} md={8} className="!flex items-end">
                <MazakaButton
                  htmlType="submit"
                  className="!flex items-center"
                  processType={formActions.submitProcessType}
                >
                  Kaydet
                </MazakaButton>
              </Col>
            </Row>
          </MazakaForm>
        </Col>
      </MazakaCard>
    </>
  );
};

export default PaymentMethod;
