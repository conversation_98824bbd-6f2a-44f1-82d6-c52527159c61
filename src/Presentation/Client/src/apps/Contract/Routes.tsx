import { Route } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";
const AllContractIndex  = lazy(() => import('./Pages/All/ContractIndex'))
const WaitApprovedIndex  = lazy(() => import('apps/Contract/Pages/WaitApprove/WaitApproveIndex'))




export const contractRouteList = [
  

  <Route key={"contractRouteList"}>
    <Route
      path={"/contract/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <AllContractIndex/>
        </Suspense>
      }
    />
    <Route
      path={"/contract/wait/approve/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <WaitApprovedIndex/>
        </Suspense>
      }
    />
  </Route>,

];
