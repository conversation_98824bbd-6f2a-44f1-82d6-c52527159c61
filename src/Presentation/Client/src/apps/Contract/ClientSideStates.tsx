import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  contractFilter: {
    PageIndex: 1,
    PageSize: 20,
    IncludeProperties: ["Company"],
  },
  contractData: null,
  waitApproveFilter: {
    PageIndex: 1,
    PageSize: 20,
    IsApproved: 0,
    SortProperty: "InsertDate,Active",
    SortType: "desc",
    IncludeProperties: ["Company"],
  },
};

const contractSlice = createSlice({
  name: "contractSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetContractFilter: (state, action) => {
      const type = action.payload.type;
      let data = action.payload;
      switch (type) {
        case "all":
          state.contractFilter = data.filter;
          break;
        case "waitApproveFilter":
          state.waitApproveFilter = data.filter;
          break;
      }
    },
    hanldleSetContractData: (state, action) => {
      let data = action.payload;
      state.contractData = data.data;
    },
    handleResetAllFieldsContract: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetContractFilter: (state, action) => {
      const type = action.payload;
      switch (type) {
        case "all":
          state.contractFilter = {
            PageIndex: 1,
            PageSize: 20,
            IncludeProperties: ["Company"],
          };
          break;
        case "waitApproveFilter":
          state.waitApproveFilter = {
            PageIndex: 1,
            PageSize: 20,
            IsApproved: 0,
            SortProperty: "InsertDate",
            SortType: "desc",
            IncludeProperties: ["Company"],
          };
          break;
      }
    },
  },
});

export const {
  handleResetContractFilter,
  handleResetAllFieldsContract,

  hanldleSetContractFilter,

  hanldleSetContractData,
} = contractSlice.actions;
export default contractSlice;
