import {
  deleteRequest,
  get,
  patch,
  post,
  put,
} from "services/BaseClient/Client";

import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";

import endpoints from "apps/Contract/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";
import { AddContractFormModel, ContractListAndDetails } from "./Models";

export const getContractListFilter = async (
  filter: any
): Promise<DataResponse<ContractListAndDetails>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getContractListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<ContractListAndDetails>>(url, config);
};
export const getContractPaymentPlanList = async (): Promise<DataResponse<ContractListAndDetails>> => {
  const url = `${endpoints.getContractPaymentPlanList}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<ContractListAndDetails>>(url, config);
};
export const getContractDetails= async (contractId:string): Promise<DataResponse<ContractListAndDetails>> => {
  const url = `${endpoints.getContract}/${contractId}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<ContractListAndDetails>>(url, config);
};
export const getContractFileUploadFilter = async (
  filter: any
): Promise<DataResponse<ContractListAndDetails>> => {
  if (filter) {
    const query = CreateUrlFilter(filter);
    const url = `${endpoints.getFileUploadListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<ContractListAndDetails>>(url, config);
  } else {
    return new Promise((resovle: any, reject) => {
      resovle([]);
    });
  }
};

export const getContractProductListFilter = async (
  filter: any
): Promise<DataResponse<any>> => {
  if (filter) {
    const query = CreateUrlFilter(filter);
    const url = `${endpoints.getContractProductListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  } else {
    return new Promise((resovle: any, reject) => {
      resovle([]);
    });
  }
};

export const addContract = async (
  data: any
): Promise<DataResponse<AddContractFormModel>> => {
  const url = `${endpoints.addContract}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const addContractFileUpload = async (
  data: any
): Promise<DataResponse<AddContractFormModel>> => {
  const url = `${endpoints.addFileUpload}`;
  const config = headers.content_type.multipart_form_data;
  return post<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const addListContractFileUpload = async (
  contractId: string,
  data: any
): Promise<DataResponse<AddContractFormModel>> => {
  const url = `${endpoints.addContractFileList}`;
  const config = headers.content_type.multipart_form_data;
  return post<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const sendEmailToCustomer = async (
  data: any
): Promise<DataResponse<AddContractFormModel>> => {
  const url = `${endpoints.sendEmailToCustomer}/${data.Id}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const addContractProductList = async (
  data: any
): Promise<DataResponse<AddContractFormModel>> => {
  const url = `${endpoints.addContractProductList}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const updateContractWithPut = async (
  data: any
): Promise<DataResponse<ContractListAndDetails>> => {
  const url = `${endpoints.updateContractWithPut}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const updateContractProductList = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateContractProductWithPutList}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const updateContractProductWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateContractProductWithPut}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const updateContractWithPatch = async (
  data: any
): Promise<DataResponse<PatchRequest>> => {
  const url = `${endpoints.updateContractWithPatch}`;
  const config = headers.content_type.application_json;
  return patch<DataResponse<any>>(url, data, config);
};
export const deleteContract = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteContract}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const deleteContractFile = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteFileUpload}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<ContractListAndDetails>>(url, data, config);
};

export const getContractTemplateListFilter = async (
  filter: any
): Promise<DataResponse<ContractListAndDetails>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getContractTemplateListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<ContractListAndDetails>>(url, config);
};

export const addContractTemplate = async (
  data: any
): Promise<DataResponse<AddContractFormModel>> => {
  const url = `${endpoints.addContractTemplate}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const updateContractTemplateWithPut = async (
  data: any
): Promise<DataResponse<ContractListAndDetails>> => {
  const url = `${endpoints.updateContractTemplateWithPut}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const approvedContractWithPut = async (
  contractId:string,note:string
): Promise<DataResponse<ContractListAndDetails>> => {
  const url = `${endpoints.approvedContract}/${contractId}/${note}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<ContractListAndDetails>>(url, {}, config);
};
export const rejectContractWithPut = async (
  contractId:string,note:string
): Promise<DataResponse<ContractListAndDetails>> => {
  const url = `${endpoints.rejectContract}/${contractId}/${note}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<ContractListAndDetails>>(url, {}, config);
};

export const updateContractTemplateWithPatch = async (
  data: any
): Promise<DataResponse<PatchRequest>> => {
  const url = `${endpoints.updateContractTemplateWithPatch}`;
  const config = headers.content_type.application_json;
  return patch<DataResponse<any>>(url, data, config);
};
export const deleteContractTemplate = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteContractTemplate}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<ContractListAndDetails>>(url, data, config);
};
export const deleteContractProduct = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteContractProduct}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<ContractListAndDetails>>(
    url,
    data,
    config
  );
};
