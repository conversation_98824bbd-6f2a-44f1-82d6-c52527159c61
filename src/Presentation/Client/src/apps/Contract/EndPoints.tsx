const baseContractUrl = "Contract"
const baseContractTemplateUrl = "ContractTemplate"
const baseContractProductUrl = "ContractProduct"
const baseFileUploadUrl = "ContractFile"
  const endpoints = {
    getContractListFilter:`${baseContractUrl}/Filter`,
    getContract:`${baseContractUrl}/Find`,
    getContractPaymentPlanList:`${baseContractUrl}/PaymentPlan`,
    addContract:`${baseContractUrl}`,
    approvedContract:`${baseContractUrl}/Approve`,
    rejectContract:`${baseContractUrl}/Reject`,
    sendEmailToCustomer:`${baseContractUrl}/SendWithEmail`,
    updateContractWithPut:`${baseContractUrl}`,
    updateContractWithPatch:`${baseContractUrl}`,
    deleteContract:`${baseContractUrl}`,
    getContractTemplateListFilter:`${baseContractTemplateUrl}/Filter`,
    addContractTemplate:`${baseContractTemplateUrl}`,
    updateContractTemplateWithPatch:`${baseContractTemplateUrl}`,
    updateContractTemplateWithPut:`${baseContractTemplateUrl}`,
    deleteContractTemplate:`${baseContractTemplateUrl}`,
    getContractProductListFilter:`${baseContractProductUrl}/Filter`,
    addContractProductList:`${baseContractProductUrl}/AddList`,
    updateContractProductWithPutList:`${baseContractProductUrl}/PutList`,
    updateContractProductWithPut:`${baseContractProductUrl}`,
    deleteContractProduct:`${baseContractProductUrl}`,
    getFileUploadListFilter:`${baseFileUploadUrl}/Filter`,
    addFileUpload:`${baseFileUploadUrl}`,
    addContractFileList:`${baseFileUploadUrl}/AddList`,
    updateFileUploadWithPut:`${baseFileUploadUrl}`,
    updateFileUploadWithPatch:`${baseFileUploadUrl}`,
    deleteFileUpload:`${baseFileUploadUrl}`,
    requestByRetailUser: `${baseContractUrl}/RequestByRetailUser`,
  };
  
  export default endpoints;
  
