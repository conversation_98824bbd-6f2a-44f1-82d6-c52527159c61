import { Col, Row, Form } from "antd";
import GeneralProductInput from "apps/Common/GeneralProductInput";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import MazakaInputNumber from "apps/Common/MazakaInputNumber";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import GeneralStationInput from "apps/Common/GeneralStationInput";
import { FC, useEffect, useState } from "react";
import { useQueryClient } from "react-query";
import { useSelector } from "react-redux";
import endpoints from "apps/Contract/EndPoints";
import { RootState } from "store/Reducers";
import { FormInstance } from "antd/lib";
import useMazakaForm from "hooks/useMazakaForm";
import GeneralContractPaymentPlanInput from "apps/Common/GeneralContractPaymentPlanInput";
import { requestByRetailUser } from "apps/Contract/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { openNotificationWithIcon } from "helpers/Notifications";
import endpointsv2 from "apps/Plan/EndPoints";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { MazakaSwitch } from "apps/Common/MazakaSwitch";


interface RetailContractFormProps {
  isEditMode: boolean;
  setIsEditMode: any;
  selectedRecord: any;
  form?: FormInstance;
  onFinish: () => void; // Modal'ı kapatmak için callback
}

const RetailContractForm: FC<RetailContractFormProps> = ({
  selectedRecord,
  isEditMode,
  setIsEditMode,
  form,
  onFinish,
}) => {
  const queryClient = useQueryClient();
  const { contractData } = useSelector((state: RootState) => state.contract);
  
  // Form instance oluştur
  const [defaultForm] = Form.useForm();
  const formInstance = form || defaultForm;
  
  // useMazakaForm hook'unu form instance ile çağır
  const { mazakaForm, formActions } = useMazakaForm(formInstance);
  
  // Payment plan filter data state
  const [planFilterData, setPlanFilterData] = useState([5]);

  const refetch = () => {
    queryClient.resetQueries({
      queryKey: endpoints.getContractProductListFilter,
      exact: false,
    });
  };

  const handleOnFinish = async (formFields: any) => {
    mazakaForm.setLoading();
    
    const submitData = {
      TransactionRequestId: selectedRecord?.Id,
      Price: formFields.Price,
      PaymentPlanId: formFields.PaymentPlanId,
      TaxInclude:formFields.TaxInclude ? formFields.TaxInclude : false,
      Description:formFields.Description,
    };

    try {
      const response =  await requestByRetailUser(submitData);
      console.log(response);
      mazakaForm.setSuccess(2000, () => "İşlem Başarılı");
      if (response?.Value) {
          openNotificationWithIcon("info", response.Message ? response.Message : response.Value);
        }
      
      queryClient.resetQueries({
        queryKey: endpointsv2.getTransactionRequestListFilter,
        exact: false,
      });
      
      if (isEditMode) {
        setIsEditMode(false);
      }
      formInstance.resetFields();
      
      // Modal'ı kapat
      onFinish();
      
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "RetailContract");
    }
  };

  // Price değerine göre payment plan filter'ı ayarla
  useEffect(() => {
    const price = selectedRecord?.Price || 0;
    if (price >= 10000000) {
      setPlanFilterData([]);
    } else {
      setPlanFilterData([5]);
    }
  }, [selectedRecord?.Price]);

  // selectedRecord değiştiğinde form verilerini doldur
  useEffect(() => {
    if (selectedRecord && formInstance) {
      const formData = {
        ProductId: selectedRecord.ProductId,
        BuildingId: selectedRecord.BuildingId || selectedRecord.Building?.Id,
        StationId: selectedRecord.StationId,
        TotalConcrete: selectedRecord.DesiredTotalConcrete || selectedRecord.TotalConcrete,
        Price: selectedRecord.Price,
        PaymentPlanId: selectedRecord.PaymentPlanId,
      };

      // Sadece değeri olan alanları set et
      const filteredData = Object.entries(formData).reduce((acc, [key, value]) => {
        if (value !== undefined && value !== null) {
          acc[key] = value;
        }
        return acc;
      }, {} as any);

      formInstance.setFieldsValue(filteredData);
    }
  }, [selectedRecord, formInstance]);

  return (
    <Col span={24}>
      <MazakaForm
        form={formInstance}
        onFinish={handleOnFinish}
        submitButtonVisible={false}
        initialValues={{ Active: true, IncludeTax: true }}
      >
        <Row gutter={[10, 20]}>
          <GeneralProductInput
            name="ProductId"
            disabled={true}
            label="Ürün"
            placeholder="Ürün"
            className="!m-0"
            xs={24}
            lg={24}
            rules={[{ required: true, message: "" }]}
          />

          <GeneralBuildingInput
            name="BuildingId"
            placeholder="Şantiyeler"
            disabled={true}
            label="Şantiyeler"
            className="!m-0"
            xs={24}
            lg={24}
            allowClear
            externalValueId={contractData?.BuildingId}
          />

          <GeneralStationInput
            name="StationId"
            placeholder="Santral"
            disabled={true}
            label="Santral Adı"
            className="!m-0"
            allowClear
          />

          <MazakaInputNumber
            name="TotalConcrete"
            label="Talep Edilen Beton Miktarı"
            disabled={true}
            placeholder="Talep Edilen Beton Miktarı"
            className="!m-0"
            suffix="m³"
            xs={24}
            lg={24}
          />

          <MazakaInputNumber
            name="Price"
            label="Sözleşme Fiyatı"
            placeholder="Sözleşme Fiyatı"
            className="!m-0"
            suffix="TL"
            xs={24}
            lg={selectedRecord?.PriceTypeId == 1 ? 19 : 24}
            rules={[{ required: true, message: "" }]}
          />
          
          {(selectedRecord?.PriceTypeId == 1 &&(
            <MazakaSwitch
                className="!m-0"
                wrapperClassName="!flex justify-end"
                xs={24}
                lg={5}
                label={"KDV Dahil"}
                name={"TaxInclude"}
                onChange={(status: any) => {
                  formInstance.setFieldValue("TaxInclude", status);
                }}
              />
          ))}

          <GeneralContractPaymentPlanInput
              label={"Ödeme Planı"}
              name="PaymentPlanId"
              xs={24}
              placeholder="Ödeme Seçenekleri"
              className="!m-0"
              externalFilteredData={planFilterData}
              rules={[{ required: true, message: "" }]}
            />

            

            <MazakaTextArea
              xs={24}
              label="Açıklama"
              placeholder="Açıklama"
              name="Description"
              className="!m-0"
            />

          <Col xs={24} className="!flex items-end gap-2">
            <MazakaButton htmlType="submit" processType={formActions?.submitProcessType}>
              {isEditMode ? "Güncelle" : "Ekle"}
            </MazakaButton>
            {isEditMode && (
              <MazakaButton
                onClick={() => {
                  setIsEditMode(false);
                  formInstance.resetFields();
                }}
                type="primary"
                className="!flex items-center !bg-red-400 !text-white"
              >
                Vazgeç
              </MazakaButton>
            )}
          </Col>
        </Row>
      </MazakaForm>
    </Col>
  );
};

export default RetailContractForm;