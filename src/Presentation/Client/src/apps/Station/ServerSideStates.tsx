import { useQuery } from "react-query";
import endpoints from "apps/Station/EndPoints";
import {
  getStationListFilter,
  getStationStatusListFilter
} from "apps/Station/Services";

export const useGetStations = (filter: any) => {
  const query = useQuery(
    [endpoints.getStationListFilter, filter],
    () => {
      return getStationListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetStationStatus = (filter: any) => {
  const query = useQuery(
    [endpoints.getStationStatusListFilter, filter],
    () => {
      return getStationStatusListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};