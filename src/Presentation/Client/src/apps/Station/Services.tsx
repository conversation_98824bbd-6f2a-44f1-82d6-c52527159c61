import {deleteRequest, get, patch, post, put} from "services/BaseClient/Client"
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Station/EndPoints"
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";



export const getStationListFilter = async (filter: any): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getStationListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  };
  export const getStationStatusListFilter = async (filter: any): Promise<DataResponse<any>> => {
    const query = CreateUrlFilter(filter)
      const url = `${endpoints.getStationStatusListFilter}?${query}`;
      const config = headers.content_type.application_json;
      return get<DataResponse<any>>(url, config);
    };

  export const addStation = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.addStation}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<any>>(url, data, config);
  };
  
  export const updateStationWithPut = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.updateStationWithPut}`;
    const config = headers.content_type.application_json;
    return put<DataResponse<any>>(url, data, config);
  };
  export const updateStationWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateStationWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
  };
  export const deleteStation = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.deleteStation}/${data.Id}`;
    const config = headers.content_type.application_json;
    return deleteRequest<DataResponse<any>>(url, data, config);
  };

  export const getStationDepartmentListFilter = async (filter: any): Promise<DataResponse<any>> => {
    const query = CreateUrlFilter(filter)
      const url = `${endpoints.getStationListFilter}?${query}`;
      const config = headers.content_type.application_json;
      return get<DataResponse<any>>(url, config);
    };