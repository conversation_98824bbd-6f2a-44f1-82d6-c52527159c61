interface StationStatus {
  Id?: number;
  Name: string;
  DisplayName: string;
}


interface BaseSharedStation {
  Name: string;
  Description: string;
  Address: string;
  Latitude: number;
  Longitude: number;
  Capacity: number;
  StatusId: number;
  Status: StationStatus;
  CreatedDate: string;
  CreatedUserId: string;
  
  
  }
  export interface AddStationFormModel extends BaseSharedStation{

  }

  export interface StationAndDetails extends BaseSharedStation{
 Id: string;
  }