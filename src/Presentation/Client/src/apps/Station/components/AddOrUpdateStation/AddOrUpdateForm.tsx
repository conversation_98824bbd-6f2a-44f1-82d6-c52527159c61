import { <PERSON><PERSON>, Col, Form, Modal, Row, Typography } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect, useState } from "react";


import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { PlusOutlined } from "@ant-design/icons";

import CustomGoogleMap from "apps/Common/GoogleMap";
import GeneralStationInput from "apps/Common/GeneralStationStatusInput";
import { addStation, updateStationWithPut } from "apps/Station/Services";
import MazakaInputNumber from "apps/Common/MazakaInputNumber";
import GeneralDepartmentInput from "apps/Common/GeneralDepartmentInput";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import LocationSearchInput  from 'apps/Common/GeneralPlaceSearchInput';



const AddOrUpdateForm: FC<GeneralAddOrUpdateFormProps> = ({ selectedRecord,onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    if (!marker || !marker?.lat || !marker.lng) {
      mazakaForm.setFailed(2000, "Konum Secimi zorunlu");
      return false;
    }
    let formValues = form.getFieldsValue();
    formValues["Latitude"] = Number(marker.lat);
    formValues["Longitude"] = Number(marker.lng);

    formValues["StateProvinceId"] = "fb01b76c-1335-4cbf-9631-08dcd7ed3bb2";

 
    try {
      if(selectedRecord)
      {
        const departmantIds = formValues["DepartmentIds"]
        let stationDepartment = departmantIds.map((id:string)=>{
          return {
            DepartmentId:id,
            StationId:selectedRecord["Id"]
          }
        })
        formValues["StationDepartment"] = stationDepartment
        formValues["Id"] = selectedRecord["Id"]
        delete formValues["DepartmentIds"]
        await updateStationWithPut({...formValues})
      }
      else{

        await addStation(formValues);
      }
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      onFinish()
      
    } catch (error: any) {
     showServiceErrorMessage(error,mazakaForm,"Station")
    }
  };
  useEffect(() => {
    if (selectedRecord) {
      const data = {...selectedRecord}
      data["DepartmentIds"] = selectedRecord["StationDepartment"].map((item:any)=>item.DepartmentId)
      form.setFieldsValue({ ...data });
    
      setMarker({
        lat: selectedRecord["Latitude"], 
        lng: selectedRecord["Longitude"],
      })
    }
  }, [selectedRecord]);

  const { Text } = Typography;
  const [isShowMap, setIsShowMap] = useState(false);
  const [marker, setMarker] = useState<google.maps.LatLngLiteral|null >(null);

  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} {...formActions}>
          <Row gutter={[0, 10]}>
         
            <MazakaInput
              className="!m-0"
              label={"Adı"}
              placeholder="Adı"
              name={"Name"}
              xs={24}
              rules={[{ required: true, message: "" }]}
            />
            <GeneralStationInput
              name="StatusId"
              label={"Durumu"}
              placeholder="Durum"
              className="!m-0"
              xs={24}
              rules={[{ required: true, message: "" }]}
            />

            <MazakaInputNumber
              name="Capacity"
              label={"Kapasite"}
              placeholder="Kapasite"
              className="!m-0"
              xs={24}
              suffix={"m³"}
              rules={[{ required: true, message: "" }]}
            />

           

            {
              selectedRecord&&
              <>
            <GeneralDepartmentInput
              name="DepartmentIds"
              label={"Departmanlar"}
              placeholder="Departmanlar"
              className="!m-0"
              xs={24}
              mode="multiple"
              rules={[{ required: true, message: "" }]}
            />
              </>
            }
            <Col xs={24} >
            <Row gutter={[0,10]}>
              <Col xs={24} >
                <Text className="!text-black">Konumu Ara</Text>
              </Col>
              <Col xs={24} >
                  <LocationSearchInput xs={24}
                  setMarker={setMarker}
                  form={form}
                  
                  />

              </Col>
            </Row>
            </Col>

            <Col span={24}>
              <Row>
                <MazakaTextArea
                  name="Address"
                  label={"Açık Adres"}
                  xs={24}
                  className="!m-0"
                  disabled
                />
                <Col span={24}>
                  <Button type="link" className="!flex items-center !pl-0">
                    <div className="!flex items-center gap-1">
                      <PlusOutlined />
                      <Text
                        onClick={() => {
                          setIsShowMap(true);
                        }}
                        className="!text-primary !text-xs"
                      >
                        Haritadan Seç
                      </Text>
                    </div>
                  </Button>
                </Col>
              </Row>
            </Col>
            <MazakaTextArea
              name="Description"
              label={"Açiklama"}
              placeholder="Açiklama"
              xs={24}
              className="!m-0"
            />
          </Row>
          <Modal
            width={"70%"}
         
            title=""
            open={isShowMap}
            onOk={()=>{  setIsShowMap(false);}}
            onCancel={() => {
              setIsShowMap(false);
            }}
          >
             
            <CustomGoogleMap
              marker={marker}
              setMarker={setMarker}
              form={form}
              type="station"
          
              
            />
          </Modal>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateForm;
