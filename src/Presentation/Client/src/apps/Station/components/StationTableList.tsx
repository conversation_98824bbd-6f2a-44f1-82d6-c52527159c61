import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import {
  Col,
  Divider,
  Drawer,
  Dropdown,
  Modal,
  Spin,
  Table,
  Typography,
} from "antd";
import { ItemType } from "antd/es/menu/interface";
import { MazakaBull } from "apps/Common/MazakaBull";
import { SecretText } from "apps/Common/SecretString";
import { openNotificationWithIcon } from "helpers/Notifications";
import { lazy, Suspense, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useQueryClient } from "react-query";
import { hanldleSetStationFilter } from "../ClientSideState";
import { useGetStations } from "../ServerSideStates";
import { deleteStation } from "../Services";
import endpoints from "apps/Station/EndPoints";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
const AddOrUpdateForm = lazy(
  () => import("./AddOrUpdateStation/AddOrUpdateForm")
);

const StationTableList = () => {
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const { filter } = useSelector((state: RootState) => state.station);
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await setSelectedRecord(record);
        setIsEditDrawer(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteStation(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getStationListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "Station", true);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetStationFilter({ filter: newFilter }));
  };
  const { Text } = Typography;

  const stations = useGetStations(filter);

  const columns: any = [
    {
      title: "Adı",
      dataIndex: "Name",
      key: "Name",
      sorter: (a: any, b: any) => a?.Name?.localeCompare(b?.Name)
    },
    {
      title: "Kapasite(m³)",
      dataIndex: "Capacity",
      key: "Capacity",
      sorter: (a: any, b: any) => Number(a?.Capacity ?? 0) - Number(b?.Capacity ?? 0)
    },

    {
      title: "Departmanlar",
      dataIndex: "StationDepartment",
      render: (StationDepartment: any[]) => {
        return (
          <>
            {StationDepartment.map((item) => {
              return (
                <>
                  <div>
                    <div>
                      <Text>{item.Department.Name}</Text>
                    </div>
                    <div className="!text-primary">
                      <SecretText
                        textType="phone"
                        text={item.Department.PhoneNumber}
                      />
                    </div>
                    <Divider className="!m-0" />
                  </div>
                </>
              );
            })}
          </>
        );
      },
      key: "Departments",
    },

    {
      title: "Konum",
      render: (_: any, record: any) => {
        return (
          <>
            <div>
              <div>{record.Latitude}</div>
              <div>{record.Longitude}</div>
            </div>
          </>
        );
      },
    },

    {
      title: "Adres",
      dataIndex: "Address",
      key: "Address",
    },

    {
      title: "Açıklama",
      dataIndex: "Description",
      key: "Description",
      sorter: (a: any, b: any) => a?.Description?.localeCompare(b?.Description)
    },

    {
      title: "Durum",
      dataIndex: "StatusId",
      sorter: (a: any, b: any) => Number(a?.StatusId ?? 0) - Number(b?.StatusId ?? 0),
      render: (status: boolean) => {
        return <MazakaBull type={status ? "Success" : "Error"} />;
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={stations.isLoading || stations.isFetching}
        dataSource={stations.data ? stations.data.Data : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: stations.data?.FilteredCount || 0,
          current: stations.data?.PageIndex,
          pageSize: stations.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <Drawer
        title="İstasyonu Güncelle"
        open={isShowEditDrawer}
        onClose={() => {
          setIsEditDrawer(false);
        }}
      >
        <>
          {isShowEditDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateForm
                selectedRecord={selectedRecord}
                onFinish={() => {
                  setIsEditDrawer(false);
                  queryClient.resetQueries({
                    queryKey: endpoints.getStationListFilter,
                    exact: false,
                  });
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </Col>
  );
};

export default StationTableList;
