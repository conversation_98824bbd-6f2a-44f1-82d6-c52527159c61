import { LinkOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";
import StationTableList from "./components/StationTableList";
import { useNavigate } from "react-router-dom";
import endpoints from "apps/Station/EndPoints";
import { useQueryClient } from "react-query";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { handleResetFilterStation } from "./ClientSideState";
import DetailsFilter from "./components/DetailsFilter";
const AddOrUpdateForm = lazy(
  () => import("./components/AddOrUpdateStation/AddOrUpdateForm")
);

const StationIndex = () => {
  const queryClient = useQueryClient();
  const { Text } = Typography;
  const navigate = useNavigate();

  const [isShowAddStationDrawer, setIsShowAddStationDrawer] = useState(false);
  const { filter } = useSelector((state: RootState) => state.station);
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] = useState(false);
  
  return (
    <>
      <MazakaLayout
        title={"Santral Listesi"}
        headDescription={
          "Santral sayfası, beton üretilen istasyonların listesini görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <Button
              onClick={() => {
                setIsShowAddStationDrawer(true);
              }}
              className="!flex items-center"
              type="primary"
            >
              <div className="!flex items-center gap-1">
                <PlusOutlined />
                <Text className="!text-white">Santral Ekle</Text>
              </div>
            </Button>
            <Button
              onClick={() => {
                navigate("/department/list");
              }}
              className="!flex items-center"
            >
              <div className="!flex items-center gap-1">
                <LinkOutlined />
                <Text>Departman Ekle</Text>
              </div>
            </Button>
            <MazakaDetailsFilterButton
                  setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
                />
                {Object.entries(filter).length > 3 && (
                  <>
                    <MazakaClearFilterButton
                      actionFunk={ handleResetFilterStation}
                     
                    />
                  </>
                )}
          </Col>
          <Col span={24}>
            <StationTableList />
          </Col>
        </Row>
      </MazakaLayout>

      <Drawer
        title="Yeni Santral Ekle"
        open={isShowAddStationDrawer}
        onClose={() => {
          setIsShowAddStationDrawer(false);
        }}
      >
        <>
          {isShowAddStationDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateForm
                onFinish={() => {
                  setIsShowAddStationDrawer(false);
                  queryClient.resetQueries({
                    queryKey: endpoints.getStationListFilter,
                    exact: false,
                  });
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
      <Drawer
          title="Detaylı Filtre"
          open={isShowFilterDetailsDrawer}
          onClose={() => {
            setIsShowFilterDetailsDrawer(false);
          }}
        >
          <>
            {isShowFilterDetailsDrawer && (
              <Suspense fallback={<Spin />}>
                <DetailsFilter
                  onFinish={() => {
                    setIsShowFilterDetailsDrawer(false);
                  }}
                />
              </Suspense>
            )}
          </>
        </Drawer>
    </>
  );
};

export default StationIndex;
