import { Route } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";
const StationIndex = lazy(() => import('./StationIndex'))




export const stationRouteList = [
  <Route key={"stationRouteList"}>
    <Route
      path={"/station/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <StationIndex/>
        </Suspense>
      }
    />
  </Route>,
];
