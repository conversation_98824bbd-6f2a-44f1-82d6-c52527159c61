import { createSlice } from "@reduxjs/toolkit";
const InitialState: any = {
  filter: {
    PageIndex: 1,
    PageSize: 20,
    IncludeProperties:["StationDepartment.Department",]
  },
};

const stationSlice = createSlice({
  name: "stationSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetStationFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    handleResetAllFieldsStation: (state,action) => {
      let data = action.payload;
      state.filter = data.filter;
      Object.assign(state, InitialState);
    },
    handleResetFilterStation: (state) => {
       state.filter = {
        PageIndex: 1,
        PageSize: 20,
        IncludeProperties:["StationDepartment.Department"]
      }
      },
  },
});

export const { handleResetAllFieldsStation,handleResetFilterStation,hanldleSetStationFilter } = stationSlice.actions;
export default stationSlice;
