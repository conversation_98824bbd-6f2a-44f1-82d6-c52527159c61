import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  filter: {
    PageIndex: 1,
    PageSize: 20,
    
  },
};

const gallerySlice = createSlice({
  name: "GallerySlice",
  initialState: InitialState,
  reducers: {
    hanldleSetGalleryFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    handleResetAllFieldsGallery: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterGallery: (state) => {
       state.filter = {
        PageIndex: 1,
        PageSize: 20,
      }
      },
  },
});

export const { handleResetAllFieldsGallery,handleResetFilterGallery,hanldleSetGalleryFilter } = gallerySlice.actions;
export default gallerySlice;
