import { Route } from "react-router-dom";
import React, { lazy, Suspense } from "react";
import { Spin } from "antd";
const GalleryIndex = lazy(() => import("./GalleryIndex"));

export const galleryRouteList = [
  <Route key={"galleryRouteList"}>
    <Route
      path={"/gallery/list"}
      element={
        <Suspense fallback={<Spin />}>
          <GalleryIndex />
        </Suspense>
      }
    />
  </Route>,
];
