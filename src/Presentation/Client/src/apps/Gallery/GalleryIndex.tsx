import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";
import { useQueryClient } from "react-query";
import endpoints from "./EndPoints";
import GalleryTableList from "./Components/GalleryTableList";
const AddOrUpdateGallery = lazy(
  () => import("./Components/AddOrUpdate/AddOrUpdateGallery")
);

const GalleryIndex = () => {
  const { Text } = Typography;
  const queryClient = useQueryClient();
  const [isShowAddGalleryDrawer, setIsShowAddGalleryDrawer] = useState(false);
  return (
    <>
      <MazakaLayout
        title={"Galeri Listesi"}
        headDescription={"Galeri sayfası, sistemde kayıtlı tüm duyuruların listesini görü<PERSON><PERSON><PERSON><PERSON> sağlar"}
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <Button
              onClick={() => {
                setIsShowAddGalleryDrawer(true);
              }}
              className="!flex items-center"
              type="primary"
            >
              <div className="!flex items-center gap-1">
                <PlusOutlined />
                <Text className="!text-white">Galeri Ekle</Text>
              </div>
            </Button>
          </Col>
          <Col span={24}>
          <GalleryTableList/>
          </Col>
        </Row>
      </MazakaLayout>

      <Drawer
        title="Yeni Galeri Ekle"
        open={isShowAddGalleryDrawer}
        onClose={() => {
          setIsShowAddGalleryDrawer(false);
        }}
      >
        <>
        {
          isShowAddGalleryDrawer&&
          <Suspense fallback={<Spin/>} >

            <AddOrUpdateGallery
            onFinish={()=>{
              setIsShowAddGalleryDrawer(false)
              queryClient.resetQueries({
                queryKey: endpoints.getGalleryListFilter,
                exact: false,
              });
    
            }} />
          </Suspense>
        }
        </>
      </Drawer>
    </>
  );
};

export default GalleryIndex;
