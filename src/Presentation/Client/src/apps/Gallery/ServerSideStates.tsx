import { useQuery } from "react-query";
import endpoints from "apps/Gallery/EndPoints";
import {
  getGalleryListFilter
} from "apps/Gallery/Services";

export const useGetGalleries = (filter: any) => {
  const query = useQuery(
    [endpoints.getGalleryListFilter, filter],
    () => {
      return getGalleryListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};