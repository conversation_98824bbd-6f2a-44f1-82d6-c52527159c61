import {deleteRequest, get, patch, post, put} from "services/BaseClient/Client"
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Gallery/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";



export const getGalleryListFilter = async (filter: any): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getGalleryListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  };

  export const addGallery = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.addGallery}`;
    const config = headers.content_type.multipart_form_data;
    return post<DataResponse<any>>(url, data, config);
  };
  export const updateGalleryWithPut = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.updateGalleryWithPut}`;
    const config = headers.content_type.multipart_form_data;
    return put<DataResponse<any>>(url, data, config);
  };
  export const updateGalleryWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateGalleryWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
  };
  export const deleteGallery = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.deleteGallery}/${data.Id}`;
    const config = headers.content_type.application_json;
    return deleteRequest<DataResponse<any>>(url, data, config);
  };