import { Col, Form, message, Row,  Typography, Upload } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect, useState } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Gallery/EndPoints";
import { DeleteOutlined, FileOutlined, InboxOutlined } from "@ant-design/icons";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { addGallery, updateGalleryWithPut } from "apps/Gallery/Services";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";

const AddOrUpdateGallery:FC<GeneralAddOrUpdateFormProps> = ({selectedRecord,onFinish}) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    if (fileList.length <= 0) {
      mazakaForm.setFailed(2000, "Resım Seçimi Zorunlu");
      return false;
    }
    let formValues = form.getFieldsValue();
    formValues["File"] = fileList[0];
    if (formValues?.File?.isSaved) {
      delete formValues["File"];
    }

  

    try {
      if (selectedRecord) {
       
       
        formValues["Id"] = selectedRecord["Id"]
      
        await updateGalleryWithPut({
          ...formValues,
        });
      } else {
       await addGallery(formValues);
       
      }

      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getGalleryListFilter,
        exact: false,
      });
      form.resetFields()
      onFinish()
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "Gallery");
    }
  };
  useEffect(() => {
    if (selectedRecord) {
  

      form.setFieldsValue({
        Title: selectedRecord["Title"],
        Description: selectedRecord["Description"],
       
      });
      if (selectedRecord?.FileName) {
        setFileList([{ name: selectedRecord?.FileName, isSaved: true }]);
      }
    }
  }, [selectedRecord]);
  const { Dragger } = Upload;

 

  const validateFile = (file: any, fileType: string) => {
    let isValidSize = true;
    if (file?.size === 0 || file?.Size === 0) {
      isValidSize = true;
    } else if (file?.Size && file?.Size !== 0) {
      isValidSize = file.Size <= 2000000;
    } else if (file?.size && file.size !== 0) {
      isValidSize = file.size <= 2000000;
    } else {
      isValidSize = false;
    }

  
     
      if (!isValidSize) {
        message.error("Dosya Boyutu en fazla 2MB  olmalı");
        return false;
      }
      return true
    

    
  };
  const beforeUpload = async (file: any) => {
    const status = validateFile(file, "");
    if (status) {
      setFileList([file]);
    } else {
      setFileList([]);
    }
    return status;
  };

  const [fileList, setFileList] = useState<any[]>([]);
  const { Text } = Typography;

  return (

      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          initialValues={{ Active: true }}
          {...formActions}
        >
          <Row gutter={[10, 10]}>
         

            <MazakaInput
              className="!m-0"
              label={"Başlık"}
              placeholder="Baslık"
              name={"Title"}
              xs={24}
           
              rules={[{ required: true, message: "" }]}
            />

      

       

            <MazakaTextArea
              name="Description"
              label={"Açıklama"}
              xs={24}
              className="!m-0"
              rules={[{ required: true, message: "" }]}
            />

            <Col xs={24}>
              <Row gutter={[10, 10]}>
                <Col xs={24}>
                  <Dragger
                    accept={".png,.jpeg,.jpg,.webp,.gif,.svg"}
                    beforeUpload={beforeUpload}
                  
                    customRequest={() => {}}
                    multiple={false}
                    fileList={fileList}
                    showUploadList={false}
                  >
                    <p className="ant-upload-drag-icon">
                      <InboxOutlined />
                    </p>
                    <p className="ant-upload-text">Resim Yükle</p>
                    <p className="ant-upload-hint">
                      İstedğiniz resimleri yükleyebilirsiniz
                    </p>
                  </Dragger>
                </Col>
                <Col xs={24}>
                  {fileList.map((item) => {
                    return (
                      <>
                        <div className="!flex justify-between">
                          <div className="!flex items-center gap-1">
                            <FileOutlined className="!text-indigo-500" />
                            <Text>{item.name}</Text>
                          </div>
                          <div>
                            <DeleteOutlined
                              className="!text-red-600"
                              onClick={() => {
                                setFileList([]);
                              }}
                            />
                          </div>
                        </div>
                      </>
                    );
                  })}
                </Col>
              </Row>
            </Col>
          </Row>
        </MazakaForm>
      </Col>

  );
};

export default AddOrUpdateGallery;
