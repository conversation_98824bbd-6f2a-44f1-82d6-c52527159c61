import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import {
  Col,
  Drawer,
  Dropdown,
  Image,
  Modal,
  Spin,
  Table,
  Typography,
} from "antd";
import { ItemType } from "antd/es/menu/interface";
import { openNotificationWithIcon } from "helpers/Notifications";
import { lazy, Suspense, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import endpoints from "apps/Gallery/EndPoints";
import { useQueryClient } from "react-query";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { deleteGallery } from "../Services";
import { hanldleSetGalleryFilter } from "../ClientSideStates";
import { useGetGalleries } from "../ServerSideStates";
import { FallbackObj } from "helpers/ImageFallBack";
const AddOrUpdateGallery = lazy(
  () => import("./AddOrUpdate/AddOrUpdateGallery")
);

const GalleryTableList = () => {
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const { filter } = useSelector((state: RootState) => state.gallery);
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await setSelectedRecord(record);
        setIsEditDrawer(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteGallery(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getGalleryListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "Gallery", true);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetGalleryFilter({ filter: newFilter }));
  };

  const galleries = useGetGalleries(filter);
  const { Text } = Typography;
  const columns = [
    {
      title: "Başlık",
      dataIndex: "Title",
      key: "Title",
      width: "30%",
      render: (value: string, record: any) => {
        return (
          <>
            <div className="!flex items-center  gap-2">
              <div className="!w-[70px] !h-[70px] !flex items-center justify-center">
                <Image
                  preview={false}
                  src={`${
                    process.env.NODE_ENV === "development"
                      ? process.env
                          .REACT_APP_DEVELOPMENT_API_BASE_MEDIA_DOWNLOAD
                      : window.location.origin
                  }/uploads/Gallery/${record.FileName || ""}`}
                  alt={""}
                  style={{ objectFit: "contain" }}
                  {...FallbackObj()}
                />
              </div>
              <div>
                <Text>{value}</Text>
              </div>
            </div>
          </>
        );
      },
      sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title)
    },
    {
      title: "Açıklama",
      dataIndex: "Description",
      key: "Description",
      width: "30%",
    },

    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={galleries.isLoading || galleries.isFetching}
        dataSource={galleries.data ? galleries.data.Data : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: galleries.data?.FilteredCount || 0,
          current: galleries.data?.PageIndex,
          pageSize: galleries.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <Drawer
        title="Firmayı Güncelle"
        open={isShowEditDrawer}
        onClose={() => {
          setIsEditDrawer(false);
        }}
      >
        <>
          {isShowEditDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateGallery
                selectedRecord={selectedRecord}
                onFinish={() => {
                  setIsEditDrawer(false);
                  queryClient.resetQueries({
                    queryKey: endpoints.getGalleryListFilter,
                    exact: false,
                  });
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </Col>
  );
};

export default GalleryTableList;
