import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Col,  Dropdown, Modal, Spin, Table } from "antd";
import { ItemType } from "antd/es/menu/interface";
import { openNotificationWithIcon } from "helpers/Notifications";
import { lazy, Suspense, useState } from "react";

import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import {
  handleSetCompanyData,
  hanldleSetCompanyFilter,
} from "apps/Company/ClientSideStates";
import { useGetCompanies } from "apps/Company/ServerSideStates";
import { deleteComapny } from "apps/Company/Services";
import endpoints from "apps/Company/EndPoints";
import { useQueryClient } from "react-query";
import { SecretText } from "apps/Common/SecretString";
import { MazakaBull } from "apps/Common/MazakaBull";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";


const AddOrUpdateIndex = lazy(() => import("./AddOrUpdate/AddOrUpdateIndex"));

const CompanyTableList = () => {
  const [isShowEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const { filter } = useSelector((state: RootState) => state.company);
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await dispatch(handleSetCompanyData({ data: record }));
        setIsEditDrawer(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteComapny(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getCompanyListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "Company", true);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetCompanyFilter({ filter: newFilter }));
  };

  const companies = useGetCompanies(filter);
  

  const columns = [
    {
      title: "Firma Adı",
      dataIndex: "Name",
      key: "Name",
      sorter: (a: any, b: any) => a?.Name?.localeCompare(b?.Name)
    },
    {
      title: "E-Posta",
      dataIndex: "Email",
      key: "Email",
      sorter: (a: any, b: any) => a?.Email?.localeCompare(b?.Email),
      render: (value: string) => {
        return (
          <>
            <div className="!text-primary">
              <SecretText text={value} textType="email" />
            </div>
          </>
        );
      },
    },
    {
      title: "Telefon",
      dataIndex: "Phone",
      key: "Phone",
      sorter: (a: any, b: any) => a?.Phone?.localeCompare(b?.Phone),
      render: (value: string) => {
        return (
          <>
            <div className="!text-primary">
              <SecretText text={value} textType="phone" />
            </div>
          </>
        );
      },
    },
    {
      title: "Vergi Numarası",
      dataIndex: "TaxNumber",
      key: "taxNumber",
      sorter: (a: any, b: any) => a?.TaxNumber?.localeCompare(b?.TaxNumber),
    },
    {
      title: "Vergi Dairesi",
      dataIndex: "TaxOffice",
      key: "TaxOffice",
      sorter: (a: any, b: any) => a?.TaxOffice.localeCompare(b?.TaxOffice)
    },
    {
      title: "Adres",
      dataIndex: "Address",
      key: "Address",
      sorter: (a: any, b: any) => a?.Address.localeCompare(b?.Address)
    },
    {
      title: "Durum",
      dataIndex: "Active",
      render: (status: boolean) => {
        return <MazakaBull type={status ? "Success" : "Error"} />;
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];





  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={companies.isLoading || companies.isFetching}
        dataSource={companies.data ? companies.data.Data : []}
  
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: companies.data?.FilteredCount || 0,
          current: companies.data?.PageIndex,
          pageSize: companies.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />

      <MazakaDrawer
        title="Firmayı Güncelle"
        placement="right"
        open={isShowEditDrawer}
        toggleVisible={() => {
          setIsEditDrawer(!isShowEditDrawer);
        }}
        layoutType="strecth"
      >
        <>
          {isShowEditDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateIndex />
            </Suspense>
          )}
        </>
      </MazakaDrawer>
    </Col>
  );
};

export default CompanyTableList;
