import { Tabs, TabsProps } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetCompanyFilter } from "../ClientSideStates";

const StatusTab = () => {
  const { filter } = useSelector((state: RootState) => state.company);
  const dispatch = useDispatch();
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Aktif",
    },
    {
      key: "2",
      label: "Pasif",
    },
  ];
  const onChange = (key: string) => {
    let currentFilter = { ...filter };
    if (key === "1") {
      currentFilter["Active"] = true;
    } else {
      currentFilter["Active"] = false;
    }
    dispatch(hanldleSetCompanyFilter({ filter: currentFilter }));
  };
  return (
    <>
      <Tabs defaultActiveKey="1" items={items} onChange={onChange} />
    </>
  );
};

export default StatusTab;
