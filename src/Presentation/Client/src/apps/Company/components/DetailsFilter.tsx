import { Col, Form, Row } from "antd";
import GeneralPhoneNumber from "apps/Common/GeneralPhoneNumber";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetCompanyFilter } from "../ClientSideStates";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { filter } = useSelector((state: RootState) => state.company);
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    
    // Phone alanını işle
    const processPhone = (phone: string | undefined) => {
      if (!phone) return undefined;
      
      const cleanPhone = phone.replace(/\D/g, ''); // Sadece rakamlar
      return cleanPhone.startsWith('0') ? cleanPhone.slice(1) : cleanPhone;
    };
    
    formValues["Phone"] = processPhone(formValues["Phone"]);
    
    // Boş değerleri filtrele
    const cleanFormValues = Object.fromEntries(
      Object.entries(formValues).filter(([, value]) => value != null && value !== '')
    );
    
    const cleanCurrentFilter = Object.fromEntries(
      Object.entries(filter || {}).filter(([, value]) => value != null && value !== '')
    );
    
    const newFilter = { ...cleanCurrentFilter, ...cleanFormValues };
  
    await dispatch(
      hanldleSetCompanyFilter({ filter: newFilter })
    );
    
    mazakaForm.setSuccess(1000, () => {}, "İşlem Başarılı");
    onFinish();
  };

  useEffect(() => {
    let data = { ...filter };
    if (data?.Phone) {
      data["Phone"] = data["Phone"].replace(
        /^0090(\d{3})(\d{3})(\d{4})$/,
        "+90 ($1) $2 $3"
      );
    }
    form.setFieldsValue({
      TaxNumber: data?.TaxNumber || undefined,
      Phone: data?.Phone || undefined,
      Ids:data?.Ids || undefined,
      Email:data?.Email || undefined,

    });
  }, [filter]);
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
       

        <Row gutter={[0, 20]}>

        <GeneralCompanies
              name="Ids"
              label={"Firma"}
              placeholder="Firma"
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />
          
          <GeneralPhoneNumber
            name="Phone"
            label={"Telefon"}
            xs={24}
            className="!m-0"
          
          />
          <MazakaInput
              className="!m-0"
              label={"Vergi Numarası"}
              placeholder="Vergi Numarası"
              name={"TaxNumber"}
              xs={24}
            />
          <MazakaInput
              className="!m-0"
              label={"Email"}
              placeholder="Email"
              name={"Email"}
              xs={24}
             
            />
          
            <Col xs={24}>
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                Filtrele
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
