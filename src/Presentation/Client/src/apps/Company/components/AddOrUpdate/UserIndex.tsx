import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON>, Drawer, Spin, Typography } from "antd";
import MazakaCard from "apps/Common/MazakaCart";
import { hanldleSetUserFilter } from "apps/User/ClientSideStates";
import UserTableList from "apps/User/Components/UserTableList";
import { lazy, Suspense, useState } from "react";
import { useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import endpoints from "apps/User/EndPoints";
const AddOrUpdateUserForm = lazy(
  () => import("apps/User/Components/AddOrUpdate/AddOrUpdateUserForm")
);

const UserIndex = () => {
  const [isShowAddUserDrawer, setIsShowAddUserDrawer] = useState(false);
  const { Text } = Typography;
  const queryClient = useQueryClient();
  const dispatch = useDispatch();
  const { filter } = useSelector((state: RootState) => state.user);
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const { companyData } = useSelector((state: RootState) => state.company);
  return (
    <>
      <MazakaCard title="Kullanıcılar" titlePosition="Left" gutter={[10, 10]}>
        <Col span={24} className="!flex justify-end gap-2">
          <Button
            onClick={async () => {
              let currentFilter: any = { ...filter };
              currentFilter["IsCompanyUser"] = true;
              currentFilter["PageSize"] = 20;
              await dispatch(
                hanldleSetUserFilter({ filter: { ...currentFilter } })
              );
              setIsShowAddUserDrawer(true);
            }}
            className="!flex items-center"
            type="primary"
          >
            <div className="!flex items-center gap-1">
              <PlusOutlined />
              <Text className="!text-white">Kullanıcı Ekle</Text>
            </div>
          </Button>
        </Col>
        <UserTableList companyId={companyData?.Id} />
        <Drawer
          title="Yeni Kullanıcı Ekle"
          open={isShowAddUserDrawer}
          onClose={() => {
            setIsShowAddUserDrawer(false);
          }}
        >
          <>
            {isShowAddUserDrawer && (
              <Suspense fallback={<Spin />}>
                <AddOrUpdateUserForm
                  selectedRecord={selectedRecord}
                  companyData={companyData}
                  setSelectedRecord={setSelectedRecord}
                  onFinish={() => {
                    setIsShowAddUserDrawer(false);
                    queryClient.resetQueries({
                      queryKey: endpoints.getUserListFilter,
                      exact: false,
                    });
                  }}
                />
              </Suspense>
            )}
          </>
        </Drawer>
      </MazakaCard>
    </>
  );
};

export default UserIndex;
