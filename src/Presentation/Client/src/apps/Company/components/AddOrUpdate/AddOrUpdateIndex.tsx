import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import GeneralInfoes from "./GeneralInfoes";
import { Row, Spin } from "antd";
import { lazy, Suspense } from "react";

const BuildingIndex = lazy(() => import("./BuildingIndex"));
const UserIndex = lazy(() => import("./UserIndex"));

const AddOrUpdateIndex = () => {
  const { companyData } = useSelector((state: RootState) => state.company);

  return (
    <Row gutter={[10, 20]}>
      <GeneralInfoes />

      {companyData && (
        <>
          <Suspense fallback={<Spin />}>
            <BuildingIndex />
          </Suspense>
          <Suspense fallback={<Spin />}>
            <UserIndex />
          </Suspense>
        </>
      )}
    </Row>
  );
};

export default AddOrUpdateIndex;
