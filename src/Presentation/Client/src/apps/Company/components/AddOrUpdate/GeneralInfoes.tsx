import { Col, Form, Row, } from "antd";
import GeneralPhoneNumber from "apps/Common/GeneralPhoneNumber";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaSwitch } from "apps/Common/MazakaSwitch";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import {
  addComapny,
  updateComapnyWithPut,
} from "apps/Company/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import endpoints from "apps/Company/EndPoints";
import MazakaCard from "apps/Common/MazakaCart";
import { handleSetCompanyData } from "apps/Company/ClientSideStates";

const GeneralInfoes: FC<GeneralAddOrUpdateFormProps> = ({

  onFinish,
}) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const {companyData:selectedRecord} = useSelector((state:RootState)=>state.company)
  const dispatch = useDispatch()
  useEffect(() => {
    if (selectedRecord) {
      let data = { ...selectedRecord };
      if (data?.Phone) {
        data["Phone"] = data["Phone"].replace(
          /^0090(\d{3})(\d{3})(\d{4})$/,
          "+90 ($1) $2 $3"
        );
      }
      form.setFieldsValue({ ...data });
    }
  }, [selectedRecord]);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();

    for(let key in formValues)
    {
      if (typeof key !=="boolean" && !formValues[key])
      {
        delete formValues[key]
      }
    }

    formValues["Phone"] = formValues["Phone"].replace(/\D/g, '')
    if(formValues["Phone"])
    {
      formValues["Phone"]= formValues["Phone"][0]==="0"? formValues["Phone"].slice(1): formValues["Phone"]
    }
    try {
      if (selectedRecord) {
        formValues["Id"] = selectedRecord["Id"]
        const response:any = await updateComapnyWithPut({  ...formValues });
        if(response?.Value)
          {
            dispatch(handleSetCompanyData({data:response.Value}))
          }
      } else {
        const response:any = await addComapny(formValues);
        if(response?.Value)
        {
          dispatch(handleSetCompanyData({data:response.Value}))
        }
      }
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      form.resetFields();
      queryClient.resetQueries({
        queryKey:endpoints.getCompanyListFilter,
        exact: false,
      });
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm,"Company");
    }
  };
  return (
    <MazakaCard>
      <Col span={24}>
        <MazakaForm
          form={form}
          initialValues={{ Active: true }}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[10, 10]}>
          <MazakaSwitch
              className="!m-0"
              xs={24}
              name={"Active"}
              label="Durum"
              wrapperClassName="!flex justify-end"
              onChange={(status) => {
                form.setFieldValue("Active", status);
              }}
            />
            <MazakaInput
              xs={24}
             
              lg={8}
              label="Firma Adı"
              className="!m-0"
              name={"Name"}
              placeholder="Firma Adı"
              rules={[{ required: true, message: "" }]}
            />
            <MazakaInput
              className="!m-0"
              label={"E-Posta"}
              placeholder="E-Posta"
              name={"Email"}
              xs={24}
              lg={8}
              rules={[
                { type: "email", message: "Geçersiz mail formatı" },
                { required: true, message: "" },
              ]}
            />
            <GeneralPhoneNumber
              name="Phone"
              label={"Telefon"}
              xs={24}
              lg={8}
              className="!m-0"
              rules={[{ required: true, message: "" }]}
            />

            <MazakaInput
              className="!m-0"
              label={"Cari Kodu"}
              placeholder="Cari Kodu"
              name={"AccountingCode"}
              xs={24}
              lg={8}
            />

            <MazakaInput
              className="!m-0"
              label={"Vergi Numarası"}
              placeholder="Vergi Numarası"
              name={"TaxNumber"}
              xs={24}
              lg={8}
            />
            <MazakaInput
              className="!m-0"
              label={"Vergi Dairesi"}
              placeholder="Vergi Dairesi"
              name={"TaxOffice"}
              xs={24}
              lg={8}
            />

            <MazakaTextArea
              xs={24}
              label="Adres"
              placeholder="Adres"
              name="Address"
              className="!m-0"
            />
            <MazakaTextArea
              xs={24}
              label="Açıklama"
              placeholder="Açıklama"
              name="Description"
              className="!m-0"
            />

          
          </Row>
        </MazakaForm>
      </Col>
    </MazakaCard>
  );
};

export default GeneralInfoes;
