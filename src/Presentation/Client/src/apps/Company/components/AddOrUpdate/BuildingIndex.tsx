import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Spin, Typography } from "antd";
import BuildingTableList from "apps/Building/components/BuildingTableList";
import MazakaCard from "apps/Common/MazakaCart";
import { lazy, Suspense, useState } from "react";
import { useQueryClient } from "react-query";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import endpoints from "apps/Building/Endpoints";
const AddOrUpdateBuildingForm = lazy(
  () => import("apps/Building/components/AddOrUpdateBuilding/AddOrUpdateBuildingForm")
);


const BuildingIndex = () => {
  const { Text } = Typography;
  const [isShowAddBuildingDrawer, setIsShowAddBuildingDrawer] = useState(false);
  const { companyData } = useSelector((state: RootState) => state.company);
  const queryClient = useQueryClient();
  return (
    <>
      <MazakaCard title="Şantiyeler" titlePosition="Left" gutter={[10, 10]}>
        <Col span={24} className="!flex justify-end gap-2">
        
          <Button
            onClick={async () => {
              setIsShowAddBuildingDrawer(true);
            }}
            className="!flex items-center"
            type="primary"
          >
            <div className="!flex items-center gap-1">
              <PlusOutlined />
              <Text className="!text-white">Şantiye Ekle</Text>
            </div>
          </Button>
        </Col>
        <Col span={24}>
          <BuildingTableList company={companyData} />
        </Col>
      </MazakaCard>
      <Drawer
        title="Yeni Şantiye Ekle"
        open={isShowAddBuildingDrawer}
        onClose={() => {
          setIsShowAddBuildingDrawer(false);
        }}
      >
       <>
       {
        isShowAddBuildingDrawer&&
        <Suspense fallback={<Spin/>} >
          <AddOrUpdateBuildingForm
            onFinish={() => {
              setIsShowAddBuildingDrawer(false);
              queryClient.resetQueries({
                queryKey: endpoints.getBuildingListFilter,
                exact: false,
              });
            }}
            externalCompanyId={companyData.Id}
          />

        </Suspense>
       }
       
       </>
      </Drawer>
    </>
  );
};

export default BuildingIndex;
