import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useEffect, useState } from "react";
import { Input } from "antd";
import { useDebouncedCallback } from "use-debounce";
import { hanldleSetCompanyFilter } from "../ClientSideStates";



const Search = () => {
    const {filter} = useSelector((state:RootState)=>state.company)
    let dispatch = useDispatch();
    const debounce = useDebouncedCallback((inputValue) => {
      let value = inputValue?.trim();
      if (value) {
        let newFilter = { ...filter };
        newFilter["Name"] = value;
        dispatch(
        hanldleSetCompanyFilter({filter:newFilter})
        );
      } else {
        let newFilter = { ...filter };
        delete newFilter["Name"];
      
        dispatch(
          hanldleSetCompanyFilter({filter:newFilter})
          );
      
      }
    }, 1000);
    let [searchValue, setSearchValue] = useState<string | undefined>(undefined);
  
    useEffect(() => {
      if(filter)
      {
       

        setSearchValue(filter[`Name`]);
      }
    }, [filter]);
    
    return ( <>

    
    
    <div className="!flex  items-center">
      <Input
        allowClear
        className="!py-3 !border-0 max-w-sm !w-full"
        placeholder={"Ara..."}
        onChange={(e) => {
          setSearchValue(e.target.value);
          debounce(e.target.value);
        }}
        value={searchValue}
      />
 
      
    </div>
    
    </> );
}
 
export default Search;