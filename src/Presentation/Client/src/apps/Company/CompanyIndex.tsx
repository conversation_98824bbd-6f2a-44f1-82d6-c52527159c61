import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";
import CompanyTableList from "apps/Company/components/CompanyTableList";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";
import {
  handleResetFilterCompany,
  handleSetCompanyData,
} from "./ClientSideStates";
import { useDispatch, useSelector } from "react-redux";
import Search from "./components/Search";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { RootState } from "store/Reducers";
import DetailsFilter from "./components/DetailsFilter";
import StatusTab from "./components/StatusTab";
const AddOrUpdateIndex = lazy(
  () => import("./components/AddOrUpdate/AddOrUpdateIndex")
);

const CompanyIndex = () => {
  const { Text } = Typography;
  const [isShowAddCarDrawer, setIsShowAddCarDrawer] = useState(false);
  const dispatch = useDispatch();
  const { filter } = useSelector((state: RootState) => state.company);
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
    


  return (
    <>
      <MazakaLayout
        title={"Firma Listesi"}
        headDescription={
          "Firma sayfası, sistemde tüm kayıtlı  firmaların listesini görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]}>
          <Col xs={24} >
            <StatusTab/>
          </Col>
          <Col span={24}  className="!flex justify-end gap-2">
            <Button
              onClick={async () => {
                await dispatch(handleSetCompanyData({ data: null }));
                setIsShowAddCarDrawer(true);
              }}
              className="!flex items-center"
              type="primary"
            >
              <div className="!flex items-center gap-1">
                <PlusOutlined />
                <Text className="!text-white">Firma Ekle</Text>
              </div>
            </Button>
            <MazakaDetailsFilterButton
                  setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
                />
                {Object.entries(filter).length > 3 && (
                  <>
                    <MazakaClearFilterButton
                      actionFunk={ handleResetFilterCompany}
                     
                    />
                  </>
                )}
          </Col>
          <Col span={24}>
            <Row>
              <Col span={24} style={{ background: "#f0f0f0" }}>
                <Row gutter={[0, 10]} className="p-2">
                  <Col xs={24} xl={12} className="">
                    <Row gutter={[10, 10]}>
                      <Col xs={24} md={12} lg={10}>
                        <Search />
                      </Col>
                    </Row>
                  </Col>
                  <Col xs={24} xl={12} className="text-right"></Col>
                </Row>
              </Col>
              <Col span={24}>
                <CompanyTableList />
              </Col>
            </Row>
          </Col>
        </Row>
      </MazakaLayout>

      <MazakaDrawer
        title="Yeni Firma Ekle"
        placement="right"
        open={isShowAddCarDrawer}
        toggleVisible={() => {
          setIsShowAddCarDrawer(!isShowAddCarDrawer);
        }}
        layoutType="strecth"
      >
        <>
          {isShowAddCarDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateIndex />
            </Suspense>
          )}
        </>
      </MazakaDrawer>
      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
        <>
          {isShowFilterDetailsDrawer && (
            <Suspense fallback={<Spin />}>
              <DetailsFilter
                onFinish={() => {
                  setIsShowFilterDetailsDrawer(false);
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </>
  );
};

export default CompanyIndex;
