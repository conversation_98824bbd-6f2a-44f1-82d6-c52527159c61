import { Route } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";
const CompanyIndex= lazy(() => import('apps/Company/CompanyIndex'))


export const companyRouteList = [
  <Route key={"companyRouteList"}>
    <Route
      path={"/company/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <CompanyIndex/>
        </Suspense>
      }
    />
  </Route>,
];
