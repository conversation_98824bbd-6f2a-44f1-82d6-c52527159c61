import { useQuery } from "react-query";
import endpoints from "apps/Company/EndPoints";
import {
  getCompanyListFilter
} from "apps/Company/Services";

export const useGetCompanies = (filter: any) => {
 
  const query = useQuery(
    [endpoints.getCompanyListFilter, filter],
    () => {
      return getCompanyListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};