import {deleteRequest, get, patch, post, put} from "services/BaseClient/Client"

import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import { AddCompanyFormModel, CompanyListAndDetails } from "./Models";
import endpoints from "apps/Company/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";



export const getCompanyListFilter = async (filter: any): Promise<DataResponse<CompanyListAndDetails>> => {
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getCompanyListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<CompanyListAndDetails>>(url, config);
  };

  export const addComapny = async (data: any): Promise<DataResponse<AddCompanyFormModel>> => {
    const url = `${endpoints.addCompany}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<CompanyListAndDetails>>(url, data, config);
  };
  export const updateComapnyWithPut = async (data: any): Promise<DataResponse<AddCompanyFormModel>> => {
    const url = `${endpoints.updateCompanyWithPut}`;
    const config = headers.content_type.application_json;
    return put<DataResponse<CompanyListAndDetails>>(url, data, config);
  };
  export const updateComapnyWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateCompanyWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
  };
  export const deleteComapny = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.deleteCompany}/${data.Id}`;
    const config = headers.content_type.application_json;
    return deleteRequest<DataResponse<CompanyListAndDetails>>(url, data, config);
  };