import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  filter: {
    PageIndex: 1,
    PageSize: 20,
    Active:true,
  },
  companyData:null
};

const companySlice = createSlice({
  name: "companySlice",
  initialState: InitialState,
  reducers: {
    hanldleSetCompanyFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    handleResetAllFieldsCompany: (state) => {
      Object.assign(state, InitialState);
    },
    handleSetCompanyData: (state,action) => {
     state.companyData = action.payload.data
    },
    handleResetFilterCompany: (state) => {

       state.filter = {
        PageIndex: 1,
        PageSize: 20,
        Active:true,
      }
      },
  },
});

export const { handleResetAllFieldsCompany,handleSetCompanyData,handleResetFilterCompany,hanldleSetCompanyFilter } = companySlice.actions;
export default companySlice;
