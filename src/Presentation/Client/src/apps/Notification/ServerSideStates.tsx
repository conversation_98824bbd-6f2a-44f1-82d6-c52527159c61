import { useQuery } from "react-query";
import endpoints from "apps/Notification/EndPoints";
import { getNotificationListFilter } from "./Services";


export const useGetNotifications = (filter: any) => {
  const query = useQuery(
    [endpoints.getNotificationListFilter, filter],
    () => {
      return getNotificationListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};