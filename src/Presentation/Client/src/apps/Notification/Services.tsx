import {deleteRequest, get, patch, post, put} from "services/BaseClient/Client"

import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Notification/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";
import { AddNotificationFormModel, NotificationListAndDetails } from "./Models";



export const getNotificationListFilter = async (filter: any): Promise<DataResponse<NotificationListAndDetails>> => {
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getNotificationListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<NotificationListAndDetails>>(url, config);
  };

  export const sendNotification = async (data: any): Promise<DataResponse<AddNotificationFormModel>> => {
    const url = `${endpoints.sendNotification}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<NotificationListAndDetails>>(url, data, config);
  };
  export const sendBulkNotification = async (data: any): Promise<DataResponse<AddNotificationFormModel>> => {
    const url = `${endpoints.sendBulkNotification}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<NotificationListAndDetails>>(url, data, config);
  };

  export const updateNotificationWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateNotificationWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
  };
  export const updateNotificationWithPut = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateNotificationWithPut}`;
    const config = headers.content_type.application_json;
    return put<DataResponse<any>>(url, data, config);
  };

  export const deleteNotification = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.deleteNotification}/${data?.Id}`;
    const config = headers.content_type.application_json;
    return deleteRequest<DataResponse<any>>(url, data,config);
  };
