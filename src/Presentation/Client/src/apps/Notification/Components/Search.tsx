import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetNotificationFilter } from "../ClientSideStates";
import { useEffect, useState } from "react";
import { Input } from "antd";
import { useDebouncedCallback } from "use-debounce";
import Sorting from "./SortProperty";

const Search = () => {
    const {filter} = useSelector((state:RootState)=>state.notification)
    let dispatch = useDispatch();
    const debounce = useDebouncedCallback((inputValue) => {
      let value = inputValue?.trim();
      if (value) {
        let newFilter = { ...filter };
        newFilter["Title"] = value;
        dispatch(
        hanldleSetNotificationFilter({filter:newFilter})
        );
      } else {
        let newFilter = { ...filter };
        delete newFilter["Title"];
        dispatch(
            hanldleSetNotificationFilter({filter:newFilter})
            );
      }
    }, 1000);
    let [searchValue, setSearchValue] = useState<string | undefined>(undefined);
  
    useEffect(() => {
      if(filter)
      {

        setSearchValue(filter[`Title`]);
      }
    }, [filter]);
    return ( <>
    
    <div className="!flex  items-center">
      <Input
        allowClear
        className="!py-3 !border-0 max-w-sm !w-full"
        placeholder={"Ara..."}
        onChange={(e) => {
          setSearchValue(e.target.value);
          debounce(e.target.value);
        }}
        value={searchValue}
      />
      <Sorting/>
      
    </div>
    
    </> );
}
 
export default Search;