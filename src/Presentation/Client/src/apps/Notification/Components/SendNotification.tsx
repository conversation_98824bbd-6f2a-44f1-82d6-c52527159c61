import { Col, Form, Row, Tabs } from "antd";
import GeneralUserInput from "apps/Common/GeneralUserInput";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect, useState } from "react";
import { sendBulkNotification, } from "../Services";
import { NotificationListAndDetails } from "../Models";
import { TabsProps } from "antd/lib";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import { openNotificationWithIcon } from "helpers/Notifications";

interface SendNotificationProps {
  onFinish?: any;
  isReadOnly?: boolean;
  selectedRecord?: NotificationListAndDetails;
}

const SendNotification: FC<SendNotificationProps> = ({
  onFinish,
  isReadOnly,
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form,{submitText:"Gönder"});

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    for(let key in formValues)
    {
      if (!formValues[key] || (Array.isArray(formValues[key]) && formValues[key].length === 0)) {
        delete formValues[key];   
      
      }
    }
    try {
      const response:any = await sendBulkNotification(formValues);
      if(response?.Value?.TotalSend)
      {

        openNotificationWithIcon("success",`Toplam gönderilen bildrim sayıs:${response?.Value?.TotalSend}  `)
      }
      if(response?.Value?.TotalTry)
      {

        openNotificationWithIcon("info",`Toplam gönderilecek bildrim sayıs:${response?.Value?.TotalTry}  `)
      }
      
     
        
      mazakaForm.setSuccess(2000, () => {
        "İşlem Tamalandı";
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm,"notification");
    }
  };

  useEffect(() => {
    if (isReadOnly) {
      form.setFieldsValue({ ...selectedRecord });
    }
  }, [selectedRecord]);
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Roller",
    },
    {
      key: "2",
      label: "Firmalar",
    },
    {
      key: "3",
      label: "Kullanıcılar",
    },
  ];
  const companyRoleList = [
    { label: "Firma", value: "aaaa544b-b5f7-4bad-8c68-22c87005bfac" },
    { label: "Şantiye", value: "44a832a5-fbe2-43eb-8c27-0e5050abd9ea" },
    { label: "Kalıpçı", value: "D9530B95-6CC4-4E13-8225-6D65D5B1E617" },
    { label: "Yapı Denetim Firması", value: "734354f3-c622-4750-a11c-04ab6f0fa497" },
  ];

  const [activeTab,setActiveTab]=useState("1")
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          initialValues={{ Active: true }}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
            <Col span={24}>
              <Tabs activeKey={activeTab} items={items} onChange={(value)=>{setActiveTab(value)}} />
            </Col>
            <MazakaInput
              xs={24}
              label="Başlık"
              className="!m-0"
              name={"Title"}
              placeholder="Başlık"
              rules={[{ required: true, message: "" }]}
              disabled={isReadOnly}
            />
            {
              (()=>{
                if(activeTab ==="1")
                {
                  return (
                    <>
                    <MazakaSelect
                      name="RoleIds"
                      label={"Roller"}
                      placeholder="Roller"
                      className="!m-0"
                      xs={24}
                      options={companyRoleList}
                      rules={[{ required: true, message: "" }]}
                      mode="multiple"
                    />
                    
                    </>
                  )
                }
                else if(activeTab ==="2")
                  {
                    return (
                      <>
                     <GeneralCompanies
                       name="CompanyIds"
                       label={"Firma"}
                       placeholder="Firma"
                       className="!m-0"
                       xs={24}
                       onChange={(_: string, obj: any) => {
                         form.setFieldValue("CompanyName", obj.label);
                       }}
                       mode="multiple"
                     />
                     
                      </>
                    )
                  }

                  else if(activeTab ==="3")
                    {
                      return (
                        <>
                        <GeneralUserInput 
                         xs={24}
                         label="Bildrim Gönderebilecek Kullanıcılar"
                         className="!m-0"
                         name={"UserIds"}
                         placeholder="Kullanıcılar"
                         rules={[{ required: true, message: "" }]}
                         mode="multiple"
                         isFCMDeviceId={true}
                        
                        />
                        
                      
                                
                       
                        </>
                      )
                    }
              })()
            }



            <MazakaTextArea
              xs={24}
              label="Açıklama"
              placeholder="Açıklama"
              name="Note"
              className="!m-0"
              rules={[{ required: true, message: "" }]}
              disabled={isReadOnly}
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default SendNotification;
