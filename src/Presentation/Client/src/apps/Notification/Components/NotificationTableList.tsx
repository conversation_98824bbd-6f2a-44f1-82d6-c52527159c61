import {
  DeleteOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Col, Dropdown, Modal, Table, Typography } from "antd";
import { ItemType } from "antd/es/menu/interface";

import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

import { useQueryClient } from "react-query";
import { MazakaBull } from "apps/Common/MazakaBull";
import { hanldleSetNotificationFilter } from "../ClientSideStates";
import { useGetNotifications } from "../ServerSideStates";
import { deleteNotification } from "../Services";
import endpoints from "apps/Notification/EndPoints";
import { openNotificationWithIcon } from "helpers/Notifications";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";


const NotificationTableList = () => {
  const { filter } = useSelector((state: RootState) => state.notification);
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];

  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteNotification(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getNotificationListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "Notification", true);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetNotificationFilter({ filter: newFilter }));
  };

  const notifications = useGetNotifications(filter);
  const { Text } = Typography;

  const columns = [
    {
      title: "Başlık",
      dataIndex: "Title",
      key: "Title",
      width: "10%",
      sorter: (a: any, b: any) => a?.Title.localeCompare(b?.Title)
    },
    {
      title: "Tarih",
      dataIndex: "InsertDate",
      key: "InsertDate",
      render: (text: any) => new Date(text).toLocaleDateString("tr-TR"),
      width: "10%",
      sorter: (a: any, b: any) =>
              dayjs(a?.InsertDate).valueOf() - dayjs(b?.InsertDate).valueOf(),
    },
    {
      title: "Kullanıcı",
      dataIndex: ["User", "Name"],
      sorter: (a: any, b: any) => a?.User?.Name.localeCompare(b?.User?.Name),
      key: "User",
      width: "20%",
      render: (value: string, record: any) => {
        return <Text>{`${value||""} ${record?.User?.Surname||""}`}</Text>;
      },
    },

    {
      title: "Açıklama",
      dataIndex: "Note",
      key: "Note",
      width: "20%",
    },

    {
      title: "Gönderim Durumu",
      dataIndex: "IsSend",
      width: "10%",
      render: (status: boolean) => {
        return <MazakaBull type={status ? "Success" : "Error"} />;
      },
    },

    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];



  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={notifications.isLoading || notifications.isFetching}
        dataSource={notifications.data ? notifications.data.Data : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: notifications.data?.FilteredCount || 0,
          current: notifications.data?.PageIndex,
          pageSize: notifications.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </Col>
  );
};

export default NotificationTableList;
