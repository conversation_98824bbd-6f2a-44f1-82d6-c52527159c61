import { SortAscendingOutlined } from "@ant-design/icons";
import { Dropdown, MenuProps, Space } from "antd";

import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetNotificationFilter } from "../ClientSideStates";



const Sorting = () => {
  const dispatch = useDispatch();
  const { filter } = useSelector((state: RootState) => state.notification);
  const applySorterFilter = ({ key }: { key: string }) => {
    let newFilter: any = {};

    const parsedKey = key.split("_");
    if (!!parsedKey[1]) {
      newFilter.SortProperty = parsedKey[0];
      newFilter.SortType = parsedKey[1] === "asc" ? "asc" : "desc";
      let allilter = { ...filter, ...newFilter };

      dispatch(hanldleSetNotificationFilter({ filter:allilter }));
    }
  };

  const sortingDropdownMenu: MenuProps["items"] = [
   
    {
      key: "InsertDate_asc",
      label: "Eskiden Yeniye göre",
    },
    {
        key: "InsertDate_desc",
        label: "Yeniden Eskiye göre",
      },
   
  
   
  ];
  return (
    <>
      <Dropdown
        menu={{
          items: sortingDropdownMenu,
          onClick: applySorterFilter,
        }}
        className="!mr-4"
      >
        <span onClick={(e) => e.preventDefault()}>
          <Space>
            <SortAscendingOutlined />
          </Space>
        </span>
      </Dropdown>
    </>
  );
};

export default Sorting;
