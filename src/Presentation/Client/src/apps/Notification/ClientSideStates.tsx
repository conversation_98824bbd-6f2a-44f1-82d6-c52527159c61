import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  filter: {
    PageIndex: 1,
    PageSize: 20,
    SortProperty: "InsertDate",
    SortType: "desc",
    IncludeProperties: ["User"],
  },
};

const notificationSlice = createSlice({
  name: "notificationSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetNotificationFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    handleResetAllFieldsNotification: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterNotification: (state) => {
      state.filter = {
        PageIndex: 1,
        PageSize: 20,
        SortProperty: "InsertDate",
        SortType: "desc",
        IncludeProperties: ["User"],
      };
    },
  },
});

export const {
  handleResetAllFieldsNotification,
  handleResetFilterNotification,
  hanldleSetNotificationFilter,
} = notificationSlice.actions;
export default notificationSlice;
