import { BellOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, <PERSON>, <PERSON>er, <PERSON>, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";
import { useQueryClient } from "react-query";
import NotificationTableList from "./Components/NotificationTableList";
import endpoints from "./EndPoints";

import Search from "./Components/Search";
const SendNotification = lazy(() => import("./Components/SendNotification"));

const NotificationIndex = () => {
  const { Text } = Typography;
  const queryClient = useQueryClient();

  const [isShowSendNotificationDrawer, setIsShowNotificationDrawer] =
    useState(false);

  return (
    <>
      <MazakaLayout
        title={"Bildirim Listesi"}
        headDescription={
          "Bildirim sayfası, sistemde  tüm bildiririmlerin listesini görüntülem<PERSON>zi sağlar"
        }
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <Button
              onClick={() => {
                setIsShowNotificationDrawer(true);
              }}
              className="!flex items-center"
              type="primary"
            >
              <div className="!flex items-center gap-1">
                <BellOutlined />
                <Text className="!text-white">Bildirim Gönder</Text>
              </div>
            </Button>
          </Col>
          <Col span={24}>
            <Row>
              <Col span={24} style={{ background: "#f0f0f0" }}>
                <Row gutter={[0, 10]} className="p-2">
                  <Col xs={24} xl={12} className="">
                    <Row gutter={[10, 10]}>
                      <Col xs={24} md={12} lg={10}>
                        <Search />
                      </Col>
                    </Row>
                  </Col>
                  <Col xs={24} xl={12} className="text-right"></Col>
                </Row>
              </Col>
              <Col span={24}>
                <NotificationTableList />
              </Col>
            </Row>
          </Col>
        </Row>
      </MazakaLayout>

      <Drawer
        title="Bildrim Gönderimi"
        open={isShowSendNotificationDrawer}
        onClose={() => {
          setIsShowNotificationDrawer(false);
        }}
      >
        <>
          {isShowSendNotificationDrawer && (
            <Suspense fallback={<Spin />}>
              <SendNotification
                onFinish={() => {
                  setIsShowNotificationDrawer(false);
                  queryClient.resetQueries({
                    queryKey: endpoints.getNotificationListFilter,
                    exact: false,
                  });
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </>
  );
};

export default NotificationIndex;
