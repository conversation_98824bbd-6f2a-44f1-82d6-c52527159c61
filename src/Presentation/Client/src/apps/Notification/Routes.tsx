import { Route } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";
const NotificationIndex = lazy(() => import('./NotificationIndex'))




export const notificationRouteList = [
  <Route key={"notificationRouteList"}>
    <Route
      path={"/notification/list"}
      element={<Suspense fallback={<Spin/>}>
        <NotificationIndex/>
      </Suspense>}
    />
  </Route>,
];
