import { Col, Row } from "antd";
import LastNewCust<PERSON> from "./LastNewCustomer";
import MazakaCard from "apps/Common/MazakaCart";
import LastNotifications from "./LastNotification";
import TodayProductsChart from "./TodayProductsChart";
import ContractTypeRequestList from "./ContractTypeRequestList";

const LastCustomerNotifications = () => {
  return (
    <>
     
       
          <Col xs={24} lg={12} className="!h-full">
            <Row gutter={[10, 10]}>
          
                <ContractTypeRequestList type={[1]} />
            
    
                <MazakaCard xs={24} className=" !h-[420px] !overflow-auto">
                  <LastNewCustomer />
                </MazakaCard>
              
            </Row>
          </Col>
          <Col xs={24} lg={12}>
            <Row gutter={[10, 10]}>

            <TodayProductsChart />
            <LastNotifications />
            </Row>
          </Col>
   
      
    </>
  );
};

export default LastCustomerNotifications;
