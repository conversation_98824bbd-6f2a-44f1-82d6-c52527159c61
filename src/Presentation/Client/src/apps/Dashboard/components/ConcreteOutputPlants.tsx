import MazakaCard from "apps/Common/MazakaCart";
import { Col, Typography } from "antd";
import {
  Tooltip,
  ResponsiveContainer,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Cell,
  Legend,
} from "recharts";
import { useEffect, useState } from "react";
import { getStationOutputChart } from "../Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import CustomNoData from "apps/Common/CustomNoData";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetDashboardRefetching } from "../ClientState";


const ConcreteOutputPlants = () => {
  const { isRefetching } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch()
  const { Text } = Typography;
  const [isLoading, setIsLoading] = useState(false);
  const [dataList, setDataList] = useState<any[]>([]);
  const initialFilter = {
    ApprovedStartDate:dayjs().format("YYYY-MM-DD"),
    ApprovedEndDate:dayjs().format("YYYY-MM-DD"),
  StatusIds:[4,5]
  }
  const getTotalStationRequet = async () => {
    setIsLoading(true);
    try {
      const response: any = await getStationOutputChart(initialFilter);
      if (response?.Value) {
        setDataList(response.Value.map((item:any)=>{
          return{
            Toplam:item.TotalRequest,
            Station:`${item?.Station ||"TS"} ${item.TotalConcrete}m³` ||"Belirlenmemis",
            StationColor:item.StationColor||"#1DC5BD"
          }
        }));
      }
    } catch (error: any) {
    showServiceErrorMessage(error,{},"TotalStationRequest",true)
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getTotalStationRequet();
  }, []);
  useEffect(()=>{
    if(isRefetching)
    {
     getTotalStationRequet()
      dispatch(hanldleSetDashboardRefetching({ status: false }));
    }
   },[isRefetching])



  return (
    <>
      <MazakaCard
        className="!h-[420px] overflow-auto !border-none"
        xs={24}
        lg={12}
        loading={isLoading}
      >
        <div>
          <Text className="!font-bold">Planlanan Sevkiyat(Santral)</Text>
        </div>

        {
          dataList?.length>0?<>
          
        <Col span={24}>
          <div style={{ width: "100%", height: 300 }}>
            <ResponsiveContainer>
              <PieChart>
                <Pie
                  data={dataList}
                  innerRadius={70}
                  outerRadius={90}
                  dataKey="Toplam"
                  nameKey={"Station"}
                  label
                >
                  {dataList.map((entry: any, index: number) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={entry["StationColor"]||"#1DC5BD"}
                    />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Col>
          </>:
          <>
          <CustomNoData/>
          </>
        }

      </MazakaCard>
    </>
  );
};

export default ConcreteOutputPlants;
