import { Image, Typography } from "antd";
import MazakaCard from "apps/Common/MazakaCart";
import { FC, } from "react";

interface SummaryCardListItemProps {
  title: string;
  value: string | number;
  src: string;

  color: string;
}

const SummaryCardListItem: FC<SummaryCardListItemProps> = (props) => {
  const { Text } = Typography;
  



  return (
    <>
      <MazakaCard
        xs={24}
        lg={8}
        className={`!rounded-lg !border-none ${props.title==="Planlanan"?"!bg-[#fef4dd]":props.title==="Kalan"?"!bg-[#ffe2e5]":"!bg-[#c8f7f5]"}` } 
      
        
        id="test"
      >
        <div className="!w-full !flex justify-center">
          <div className="!flex justify-center items-center !w-[40px] !h-[40px] !">
            <Image
              preview={false}
              src={props.src}
              width={"100%"}
              height={"100%"}
              style={{ objectFit: "contain" }}
            />
          </div>
        </div>

        <div className="!w-full ">
          <div className="!flex justify-center !my-1">
            <Text className={` !font-bold  `}>{props.title}</Text>
          </div>
          <div className="!flex justify-center">
            <Text className={`!font-bold `}>
              {props.value} m³
            </Text>
          </div>
        </div>
      </MazakaCard>
    </>
  );
};

export default SummaryCardListItem;
