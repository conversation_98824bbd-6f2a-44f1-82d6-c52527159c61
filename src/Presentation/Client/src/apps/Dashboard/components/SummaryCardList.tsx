import { Row, Skeleton } from "antd";
import { useEffect, useState } from "react";
import SummaryCardListItem from "./SummaryCardItem";
import { getSummaryCardInfoes } from "../Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetDashboardRefetching } from "../ClientState";

const SummaryCardList = () => {
  const { isRefetching } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch()
  const [isLoading,setIsLoading] = useState(false)
  const [data, setData] = useState({
    TotalPlanned: 0,
    TotalRemaining: 0,
    TotalCompleted: 0,
    Total:0,
  });

  const initialFilter = {
    ApprovedStartDate:dayjs().format("YYYY-MM-DD"),
    ApprovedEndDate:dayjs().format("YYYY-MM-DD"),
  
  }

  const getSummaryInfoes = async()=>{
    setIsLoading(true)
    try {
       const response:any =  await getSummaryCardInfoes(initialFilter)
       if(response?.Value)
       {
        setData(response.Value)
       }
    } catch (err:any) {
      showServiceErrorMessage(err,{},"Özet bilgileri",true)
    }
    finally{
      setIsLoading(false)
    }
  }
  useEffect(() => {
    getSummaryInfoes()
   
  }, []);

  useEffect(()=>{
    if(isRefetching)
    {
    getSummaryInfoes()
      dispatch(hanldleSetDashboardRefetching({ status: false }));
    }
   },[isRefetching])
  return (
    <Row gutter={[10, 10]}>
      <Skeleton loading={isLoading}>

      <SummaryCardListItem
        title="Planlanan"
        value={data["TotalPlanned"]}
        src="https://cdn-icons-png.flaticon.com/512/10030/10030249.png"

      color="!text-[#ffb42e]"
      />
      <SummaryCardListItem
        title="Kalan"
        value={data["TotalRemaining"]}
        src="https://cdn-icons-png.flaticon.com/512/7033/7033501.png"
    
          color="!text-[#f64e60]"
      />

      <SummaryCardListItem
        title="Gönderilen"
        value={data["TotalCompleted"]}
        src="https://cdn-icons-png.flaticon.com/512/16663/16663334.png"

          color="!text-[#1dc5bd]"
          
      />
       
      </Skeleton>
     
    </Row>
  );
};

export default SummaryCardList;
