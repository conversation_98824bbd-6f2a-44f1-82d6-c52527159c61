import React, { useEffect } from "react";
import { List, Typography, Col, Row } from "antd";
import { useGetUsers } from "apps/User/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";
import { UserListAndDetails } from "apps/Account/Models";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetDashboardRefetching } from "../ClientState";

const LastNewCustomers = () => {
  const { isRefetching } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch()
  const users: any = useGetUsers({
    PageSize: -1,
    IncludeProperties: ["Company"],
    StartInsertDate: dayjs().format("YYYY-MM-DD"),
    EndInsertDate: dayjs().format("YYYY-MM-DD"),
  });
  useEffect(() => {
    users.refetch();
  }, []);
  useEffect(()=>{
    if(isRefetching)
    {
      users.refetch()
      dispatch(hanldleSetDashboardRefetching({ status: false }));
    }
   },[isRefetching])

  useEffect(() => {
    if (users.isError) {
      showServiceErrorMessage(users.error, {}, "Dashboard:LastUsers", true);
    }
  }, [users.isError]);

  const { Text } = Typography;
  return (
    <Col span={24}>
      <Row>
        <Col span={24}>
          <Text className="!font-bold">Yeni Müşteriler</Text>
        </Col>
        <Col span={24}>
          <List
            dataSource={users.data ? users.data.Data.filter((item:UserListAndDetails)=>{
              return !item.CompanyId&&item.CompanyName
            }).slice(0, 20) : []}
            loading={users.isLoading || users.isFetching}
            renderItem={(user: any) => (
              <List.Item>
                <Col
                  xs={24}
                  className="!flex gap-2 !items-center justify-between !w-full"
                >
                  <Col xs={24} className="">
                    <div>
                      <Text className="!font-bold">
                        {user?.CompanyName || ""}
                      </Text>
                    </div>
                    <div>
                      <Text className="">{user.Name}</Text>
                    </div>
                  </Col>
                </Col>
              </List.Item>
            )}
          />
        </Col>
      </Row>
    </Col>
  );
};

export default LastNewCustomers;
