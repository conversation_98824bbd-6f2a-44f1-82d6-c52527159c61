import { Col, Progress, Table, Tag, Typography } from "antd";
import { useGetDailyManagerReports } from "apps/Report/ServerSideStates";
import { useEffect, useState } from "react";
import SummaryCardList from "./SummaryCardList";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";
import { getSummaryCardInfoes } from "../Services";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetDashboardRefetching } from "../ClientState";

const DailyTransactionTableList = () => {
  const { isRefetching } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch()
  const initialFilter ={  PageSize: -1,
    ApprovedStartDate:dayjs().format("YYYY-MM-DD"),
    ApprovedEndDate:dayjs().format("YYYY-MM-DD"),
    

   };



  const dailyTranasactions:any = useGetDailyManagerReports(initialFilter);
  useEffect(()=>{
    // Every rendered this component needed to send reqeust 
    dailyTranasactions.refetch()
  },[])
  useEffect(()=>{
   if(isRefetching)
   {
     dailyTranasactions.refetch()
     dispatch(hanldleSetDashboardRefetching({ status: false }));
   }
  },[isRefetching])

  useEffect(()=>{
      if(dailyTranasactions.isError)
      {
        showServiceErrorMessage(dailyTranasactions.error,{},"DailyTransaction",true)
      }


  },[dailyTranasactions.isError])
const {Text} = Typography
  const columns = [
    {
      title: "Firma Bilgiler",
      dataIndex: "Station",
      key: "StationName",
      
      render:(_:any,record:any)=>{
        return(
          <div className="">
            <div className="!flex">
               <Text className="!text-xs !font-bold !text-[#3f4254]">{record.Building}</Text>
            </div>
            <div className="!flex !my-1">
               <Text className="!text-xs">{record.Company}</Text>
            </div>
            <div className="!flex">
               <Text className="!text-xs">{record.Station}</Text>
            </div>
          </div>
        )
      }
    },
   
    {
      title: "Toplam(m³)",
      dataIndex: "TotalConcreteRequested",
      key: "TotalConcreteRequested",
     
    },
    {
      title: "Tamamlanan",
      dataIndex: "TotalConcreteSent",
      key: "TotalConcreteSent",
      render: (value: any, record: any) => {
        const totalConcreteRequested = record.TotalConcreteRequested|0;
        const totalConcreteSent = totalConcreteRequested-(record?.TotalConcreteRemaining||0);
        const percent = totalConcreteRequested > 0 &&record?.TotalConcreteRemaining !==null
        ? Math.round((totalConcreteSent / totalConcreteRequested) * 100 * 100) / 100 
        : 0;
    
        return (
          <>
            <Progress
              type="circle"
              percent={Math.round(percent)} 
              size="small"
            />
          </>
        );
      },
    },
    {
      title: "Kalan(m³)",
      dataIndex: "TotalConcreteRemaining",
      key: "TotalConcreteRemaining",
      
     
    },
   
  ];
  const [isLoading,setIsLoading] = useState(false)
  const [data, setData] = useState({
    TotalPlanned: 0,
    TotalRemaining: 0,
    TotalCompleted: 0,
    Total:0,
  });

  const getSummaryInfoes = async()=>{
    setIsLoading(true)
    try {
       const response:any =  await getSummaryCardInfoes(initialFilter)
       if(response?.Value)
       {
        setData(response.Value)
       }
    } catch (err:any) {
      showServiceErrorMessage(err,{},"Özet bilgileri",true)
    }
    finally{
      setIsLoading(false)
    }
  }
  useEffect(() => {
    getSummaryInfoes()
   
  }, []);
  

  return (
    <>
      <Col span={24} className="!flex justify-between">
          <Text className="!font-bold">
            Sevkiyatlar

          </Text>
          <Text>{`(Toplam Talep Edilen ${data?.Total||0}m³)`}</Text>
        </Col>
        <Col span={24} >
        <SummaryCardList/>
        </Col>
      <Col span={24} >
      <Table
        columns={columns}
        loading={dailyTranasactions.isLoading || dailyTranasactions.isFetching}
        dataSource={
          dailyTranasactions.data ? dailyTranasactions.data.Value : []
        }
        
        rowKey={"Id"}
        scroll={{ x: 400 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
        
          total: dailyTranasactions.data?.FilteredCount || 0,
       
          pageSize: 40,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      </Col>
    </>
  );
};

export default DailyTransactionTableList;
