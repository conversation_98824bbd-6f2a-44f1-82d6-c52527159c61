import { Col, Steps } from "antd";
import { FC } from "react";

interface TodayCustomerSubTrtansactionsProps {
  record: any;
}

const TodayCustomerSubTrtansactions: FC<
  TodayCustomerSubTrtansactionsProps
> = () => {
  return <>
  
  <Col xs={24} >
  <Steps
      progressDot
      current={1}
      direction="vertical"
      items={[
        {
          title: 'Bşalık',
          description: 'Açıklama',
        },
        {
          title: 'Finished',
          description: 'This is a description. This is a description.',
        },
        {
          title: 'In Progress',
          description: 'This is a description. This is a description.',
        },
        {
          title: 'Waiting',
          description: 'This is a description.',
        },
        {
          title: 'Waiting',
          description: 'This is a description.',
        },
      ]}
    />
  </Col>
  </>;
};

export default TodayCustomerSubTrtansactions;
