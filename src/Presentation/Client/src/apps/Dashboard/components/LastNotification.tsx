import React, { useEffect } from "react";
import { List, Typography, Col, Tooltip } from "antd";
import { useGetNotifications } from "apps/Notification/ServerSideStates";
import MazakaCard from "apps/Common/MazakaCart";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetDashboardRefetching } from "../ClientState";

const LastNotifications = () => {
  const { isRefetching } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch();
  const notifications = useGetNotifications({
    PageSize: -1,
    SorProperty: "InsertDate",
    StartInsertDate: dayjs().format("YYYY-MM-DD"),
    EndInsertDate: dayjs().format("YYYY-MM-DD"),
    SortType: "desc",
    IncludeProperties:["User"]
  });
  useEffect(() => {
    notifications.refetch();
  }, []);
  useEffect(() => {
    if (isRefetching) {
    notifications.refetch()
      dispatch(hanldleSetDashboardRefetching({ status: false }));
    }
  }, [isRefetching]);

  useEffect(()=>{
    if(notifications.isError)
    {
      showServiceErrorMessage(notifications.error,{},"Dashboard:Last Notification",true)
    }


},[notifications.isError])

  const { Text } = Typography;
  return (
    <MazakaCard span={24} className="!h-[420px] !overflow-scroll" >

        <Col span={24}>
          <Text className="!font-bold">Son Bildirimler</Text>
        </Col>
        <Col span={24}>
          <List
            dataSource={
              notifications.data ? notifications.data.Data.slice(0, 20) : []
            }
            loading={notifications.isLoading ||notifications.isFetching}
            renderItem={(notification: any) => (
              <List.Item>
                <Col xs={24} className="!flex gap-2 !items-center justify-between !w-full">
                  <Col xs={24} md={12} lg={18} className="">
                    <div>
                      <Text className="!font-bold">{notification.Title}</Text>
                    </div>
                    <div>
                    {
                      notification?.Note&&
                      <>
                      {
                       notification?.Note.length<=50?<>
                      <Text className="!text-xs">{ notification.Note}</Text>
                       </>:<>
                       <Tooltip title={ notification.Note} >
                          <Text className="!text-xs">{notification.Note?.slice(0,50)||""}</Text>
                       </Tooltip>
                       </>
                      }
                      
                      </>
                    }
                     
                    </div>
                    <div>
                      <Text className="!text-indigo-500">{notification?.User?.Name}</Text>
                    </div>
                  </Col>
                  <Col  xs={24} md={12} lg={6}  className="!flex items-center justify-end">
                    <Text className="!text-xs !text-gray-400">
                      
                    
                      {dayjs(notification.InsertDate).format("YYYY-MM-DD")}
                    </Text>
                  </Col>
                </Col>
              </List.Item>
            )}
          />
        </Col>
    
    </MazakaCard>
  );
};

export default LastNotifications;
