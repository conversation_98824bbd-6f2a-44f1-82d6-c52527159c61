
import { Col, List, Row,  Typography } from "antd";
import MazakaCard from "apps/Common/MazakaCart";
import { RequestTransactionAndDetails } from "apps/Plan/Models";
import { useGetTransactionRequests } from "apps/Plan/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { FC, useEffect, useState } from "react";
import dayjs  from 'dayjs';
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetDashboardRefetching } from "../ClientState";


interface NewRequestWithoutContractListProps{
  type?:any[]
}
const ContractTypeRequestList:FC<NewRequestWithoutContractListProps> = ({type}) => {
  const { isRefetching } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch()
  const transactionRequests = useGetTransactionRequests({
    PageSize: -1,
    IncludeProperties:["Product","Building.Company"],
    StartDesiredTime:dayjs().format("YYYY-MM-DD"),
    EndDesiredTime:dayjs().format("YYYY-MM-DD"),
    StatusIds:[type]
  });
  const { Text } = Typography;
  const [data,setData] = useState<RequestTransactionAndDetails[]>([])
  useEffect(()=>{
    if(transactionRequests.isError)
    {
      showServiceErrorMessage(transactionRequests.error,{},"Dashboar:All Transction",true)
    }
   


},[transactionRequests.isError])
useEffect(()=>{
  // Every rendered this component needed to send reqeust 
  transactionRequests.refetch()
},[])

useEffect(()=>{
  if(isRefetching)
  {
    transactionRequests.refetch()
    dispatch(hanldleSetDashboardRefetching({ status: false }));
  }
 },[isRefetching])

useEffect(()=>{
  const dataList = transactionRequests.data?transactionRequests.data.Data.slice(0, 20):[]

  setData([...dataList])

},[transactionRequests.data,transactionRequests.isFetching])
  return (
    <>
      <MazakaCard className="!h-[420px] overflow-auto" loading={transactionRequests.isLoading || transactionRequests.isFetching}>
        <Col span={24}>
          <Row>
            <Col span={24}>
              <Text className="!font-bold">{type?.includes(2)?"Talepler Listesi":"Onay Bekleyen Talepler"}</Text>
            </Col>
            <Col span={24}>
              <List
                loading={transactionRequests.isLoading ||transactionRequests.isFetching}
                dataSource={
                  data
                }
                renderItem={(item: any) => {
                  return (
                    <List.Item>
                      <Col xs={24}>
                        <Row>
                          <Col xs={24} md={12} lg={10}>
                          <div className="!flex justify-start">
                              <Text className="!font-bold !text-xs">
                              {item?.Building?.Name||""}
                              </Text>
                            </div>
                            <div className="!flex justify-start">
                              <Text className="!text-xs ">
                                {item?.Building?.Company?.Name||""}
                              </Text>
                             
                            </div>
                            {/* <div className="!flex justify-start">
                              <Text className="!text-xs !text-indigo-500">
                                {item?.Building?.AuditPerson?.PhoneNumber||""}
                              </Text>
                             
                            </div> */}
                          </Col>
                          <Col xs={24} md={12} lg={14} className="">
                            <div className="!flex justify-end">
                              <Text className="">
                                {item?.DesiredTotalConcrete || 0}m³-{item?.Product?.Name||""}
                              </Text>
                            </div>
                            <div className="!flex justify-end gap-2">
                              <Text className="!text-xs !text-gray-400">
                              {`${dayjs(item.DesiredDateTime).format("YYYY-MM-DD")}`}
                              </Text>
                              <Text className="!text-xs !text-gray-400">
                              {`${dayjs(item.DesiredDateTime).format("HH:mm")}`}
                              </Text>
                           
                            </div>
                          </Col>
                        </Row>
                      </Col>
                    </List.Item>
                  );
                }}
              />
            </Col>
          </Row>
        </Col>
      </MazakaCard>
    </>
  );
};

export default ContractTypeRequestList;
