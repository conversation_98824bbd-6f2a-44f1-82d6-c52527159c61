import MazakaCard from "apps/Common/MazakaCart";
import { Col, Empty, Typography } from "antd";
import { useEffect, useState } from "react";
import { getProductsToShippedInfoes } from "../Services";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";
import CustomNoData from "apps/Common/CustomNoData";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetDashboardRefetching } from "../ClientState";

const TodayProductsChart = () => {
  const { isRefetching } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch();
  const { Text } = Typography;
  const [isLoading, setIsLoading] = useState(false);
  const [dataList, setDataList] = useState<any[]>([]);
  const initialFilter = {
    ApprovedStartDate: dayjs().format("YYYY-MM-DD"),
    ApprovedEndDate: dayjs().format("YYYY-MM-DD"),
  };
  useEffect(() => {
    if (isRefetching) {
      getCompanyProductShipped();
      dispatch(hanldleSetDashboardRefetching({ status: false }));
    }
  }, [isRefetching]);

  const getCompanyProductShipped = async () => {
    setIsLoading(true);
    try {
      const response: any = await getProductsToShippedInfoes(initialFilter);
      if (response?.Value) {
        setDataList(
          response.Value.map((item: any) => {
            return {
              Toplam: item.Total,
              Name: item.ProductName,
            };
          })
        );
      }
    } catch (err: any) {
      showServiceErrorMessage(err, {}, "Dashboard:TotalProduct", true);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getCompanyProductShipped();
  }, []);

  return (
    <>
      <MazakaCard
        className="!h-[420px] overflow-auto !border-none"
        xs={24}
        gutter={[20, 20]}
        loading={isLoading}
      >
        <Col span={24}>
          <Text className="!font-bold">Sevkiyata Çıkacak Ürünler </Text>
        </Col>

        {dataList && dataList.length > 0 ? (
          <>
            <Col span={24}>
              <div style={{ width: "100%", height: 300 }}>
                <ResponsiveContainer>
                  <BarChart data={dataList}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="Name" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="Toplam" fill="#8884d8" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </Col>
          </>
        ) : (
          <>
            <CustomNoData />
          </>
        )}
      </MazakaCard>
    </>
  );
};

export default TodayProductsChart;
