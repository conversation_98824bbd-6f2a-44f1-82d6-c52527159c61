import MazakaCard from "apps/Common/MazakaCart";
import { Col, Typography } from "antd";
import { useEffect, useState } from "react";
import { getTotalCompanyRequestChart } from "../Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";
import CustomNoData from "apps/Common/CustomNoData";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetDashboardRefetching } from "../ClientState";

const CustomerShipmentChart = () => {
  const { isRefetching } = useSelector((state: RootState) => state.dashboard);
  const dispatch = useDispatch()
  const { Text } = Typography;
  const [isLoading, setIsLoading] = useState(false);
  const [dataList, setDataList] = useState<any[]>([]);
  const initialFilter = {
    ApprovedStartDate: dayjs().format("YYYY-MM-DD"),
    ApprovedEndDate: dayjs().format("YYYY-MM-DD"),
  };

  const getCompanyRequests = async () => {
    setIsLoading(true);
    try {
      const response: any = await getTotalCompanyRequestChart(initialFilter);
      if (response?.Value) {
        setDataList(response.Value.map((item: any) => ({
          Toplam: item.Total,
          Company: item.Company || "",
          ShortCompany: item.Company?.slice(0, 5) || "", // Shortened version for display
        })));
      }
    } catch (err: any) {
      showServiceErrorMessage(err, {}, "Dashboard:getCompanyRequests", true);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getCompanyRequests();
  }, []);

  useEffect(()=>{
    if(isRefetching)
    {
      getCompanyRequests()
      dispatch(hanldleSetDashboardRefetching({ status: false }));
    }
   },[isRefetching])

  // Custom tooltip to display the full company name on hover
  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip !bg-white !p-4">
          <p className="!text-xs">{payload[0].payload.Company}</p> {/* Display full name */}
          <p className="!text-indigo-500">Toplam: {payload[0].value}</p>
        </div>
      );
    }
    return null;
  };

  return (
    <MazakaCard className="!h-[420px] overflow-auto !border-none" xs={24} gutter={[20,20]} lg={12} loading={isLoading}>
      <Col span={24}>
        <Text className="!font-bold">Sevkiyat Gidecek Müşteriler</Text>
      </Col>

      {dataList && dataList.length > 0 ? (
        <Col span={24}>
          <div style={{ width: '100%', height: 300 }}>
            <ResponsiveContainer>
              <BarChart data={dataList}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="ShortCompany" /> {/* Use shortened name here */}
                <YAxis />
                <Tooltip content={<CustomTooltip />} />
                <Legend />
                <Bar dataKey="Toplam" fill="#8884d8" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Col>
      ) : (
        <CustomNoData />
      )}
    </MazakaCard>
  );
};

export default CustomerShipmentChart;
