import { get} from "services/BaseClient/Client"

import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Dashboard/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";






export const getSummaryCardInfoes = async (filter:any): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getSummaryCartInfoes}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  };

  export const  getProductsToShippedInfoes = async (filter:any): Promise<DataResponse<any>> => {
    const query = CreateUrlFilter(filter)
    const url = `${endpoints.getProductsToShippedInfoes}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  };

  export const getTotalCompanyRequestChart = async (filter:any): Promise<DataResponse<any>> => {
    const query = CreateUrlFilter(filter)
    const url = `${endpoints.getCompanyRequestChartInfoes}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  };

  export const getStationOutputChart= async (filter:any): Promise<DataResponse<any>> => {
    const query = CreateUrlFilter(filter)
    const url = `${endpoints.getStationOutputChartInfoes}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  };