import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  isRefetching:false,
};

const dashboardSlice = createSlice({
  name: "notificationSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetDashboardRefetching: (state, action) => {
      let data = action.payload;
      state.isRefetching = data.status;
    },
   
  },
});

export const {
    hanldleSetDashboardRefetching
} = dashboardSlice.actions;
export default dashboardSlice;
