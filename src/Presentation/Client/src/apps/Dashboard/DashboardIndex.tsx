import { Col, Row, Space, Tag, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import TodayCustomerTableList from "./components/TodayCustomerList";
import LastCustomerNotifications from "./components/LastCustomerAndNotifications";
import CustomerShipmentPlot from "./components/CustomerShipmentChart";
import ConcreteOutputPlants from "./components/ConcreteOutputPlants";
import MazakaCard from "apps/Common/MazakaCart";
import ContractTypeRequestList from "./components/ContractTypeRequestList";
import { MazakaButton } from "apps/Common/MazakaButton";
import { ReloadOutlined } from "@ant-design/icons";
import { useDispatch,  } from "react-redux";
import { hanldleSetDashboardRefetching } from "./ClientState";
import { useEffect } from "react";

const DashboardIndex = () => {
  const { Text } = Typography;
  
  const dispatch = useDispatch();
  useEffect(() => {
    const intervalId = setInterval(() => {
      dispatch(hanldleSetDashboardRefetching({ status: true }));
    }, 60000); 

    return () => clearInterval(intervalId); 
  }, [dispatch]);
  return (
    <>
      <MazakaLayout
        title={
          <>
            <Space>
              <Text className="!font-bold !text-2xl">Dashboard</Text>

              <Tag className="!bg-indigo-500 !text-white !rounded-lg">
                Bugün
              </Tag>
            </Space>
          </>
        }
        headDescription={"Bilgilerinizin özetini görebilirsiniz "}
      >
        <Row className="!mt-4" gutter={[10, 10]}>
          <Col xs={24} className="!flex justify-end">
            <MazakaButton
              onClick={() => {
                dispatch(hanldleSetDashboardRefetching({ status: true }));
              }}
              icon={<ReloadOutlined />}
            >
              Yenile
            </MazakaButton>
          </Col>
          <MazakaCard
            xs={24}
            lg={8}
            gutter={[10, 10]}
            className="!h-full !overflow-scroll !max-h-[1280px]"
            
          >
            <TodayCustomerTableList />
          </MazakaCard>

          <Col xs={24} lg={16}>
            <Row gutter={[5, 10]}>
              <Col xs={24} lg={16}>
                <Row gutter={[10, 10]}>
                  <ConcreteOutputPlants />
                  <CustomerShipmentPlot />
                </Row>
              </Col>
              <Col xs={24} lg={8}>
                <ContractTypeRequestList type={[2]} />
              </Col>

              <Col xs={24} lg={24}>
                <Row gutter={[10, 10]}>
                  <LastCustomerNotifications />
                </Row>
              </Col>
            </Row>
          </Col>
        </Row>
      </MazakaLayout>
    </>
  );
};

export default DashboardIndex;
