import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import {
  Col,
  Drawer,
  Dropdown,
  Modal,
  Spin,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { ItemType } from "antd/es/menu/interface";
import { MazakaBull } from "apps/Common/MazakaBull";
import { SecretText } from "apps/Common/SecretString";
import endpoints from "apps/User/EndPoints";
import { openNotificationWithIcon } from "helpers/Notifications";
import { FC, lazy, Suspense, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetUsers } from "../ServerSideStates";
import { deleteUser, updateUserWithPut } from "../Services";
import { useQueryClient } from "react-query";
import { hanldleSetUserFilter } from "../ClientSideStates";
const AddOrUpdateUserForm = lazy(
  () => import("../Components/AddOrUpdate/AddOrUpdateUserForm")
);

interface UserTableListProps {
  companyId?: string;
}

const UserTableList: FC<UserTableListProps> = ({ companyId }) => {
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const dispatch = useDispatch();
  const { filter } = useSelector((state: RootState) => state.driver);
  const users = useGetUsers(
    companyId
      ? {
          PageSize: -1,
          CompanyId: companyId,
          IncludeProperties: [
            "UserDepartment",
            "UserRole.Role",
            "Company",
            "BuildingUser.Building",
          ],
        }
      : filter
  );
  const queryClient = useQueryClient();

  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await setSelectedRecord(record);
        if (companyId) {
          let currentFilter: any = { ...filter };
          currentFilter["IsCompanyUser"] = true;
          currentFilter["PageSize"] = 20;
          await dispatch(
            hanldleSetUserFilter({ filter: { ...currentFilter } })
          );
        }
        setIsEditDrawer(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          if (
            record?.RoleId === "734354f3-c622-4750-a11c-04ab6f0fa497" ||
            record?.RoleId === "d9530b95-6cc4-4e13-8225-6d65d5b1e617"
          ) {
            //kaplipci yapidenetim personel
            record["BuildingUser"] = [];
            await updateUserWithPut(record);
          } else {
            await deleteUser(record);
          }
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getUserListFilter,
            exact: false,
          });
        } catch (error: any) {
          openNotificationWithIcon(
            "error",
            error?.Message ? error.Message : "İşlem başarısız"
          );
          console.log(
            "Something went wrong during the deletion of product brands",
            error
          );
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetUserFilter({ filter: { ...newFilter } }));
  };
  const { Text } = Typography;

  const columns = [
    {
      title: "Adı",
      dataIndex: "Name",
      key: "Name",
      sorter: (a: any, b: any) => a?.Name?.localeCompare(b?.Name),
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{`${value || ""} ${record?.Surname || ""}`}</Text>
            </div>
            <div className="!text-primary">
              <SecretText text={record.PhoneNumber} textType="phone" />
            </div>
            {!record?.Email?.includes("tokgoz.com") && (
              <>
                <div className="!text-primary">
                  <SecretText text={record.Email} textType="email" />
                </div>
              </>
            )}
          </>
        );
      },
    },

    ...(filter?.IsCompanyUser && !companyId
      ? [
          {
            title: "Firma",
            dataIndex: ["Company", "Name"],
            key: "CompanyName",
            width: "25%",
            sorter: (a: any, b: any) => (a?.Company?.Name || '').localeCompare(b?.Company?.Name || ''),
            render: (value: string) => {
              if (value) {
                return (
                  <>
                    {value?.length <= 10 ? (
                      <>
                        <Text>{value}</Text>
                      </>
                    ) : (
                      <>
                        <Tooltip title={value}>
                          <Text>{value.slice(0, 10)}</Text>
                        </Tooltip>
                      </>
                    )}
                  </>
                );
              }
              return <></>;
            },
          },
        ]
      : []),

    ...(filter?.IsCompanyUser || companyId
      ? [
          {
            title: "Şantiyeler",
            dataIndex: "BuildingUser",
            key: "BuildingUser",
            width: "20%",
            render: (value: any[]) => {
              if (value?.length > 0) {
                return (
                  <div className="!flex !flex-wrap gap-1 !max-h-[200px] !overflow-scroll">
                    {value.map((item) => {
                      return (
                        <>
                          <Tag color="blue">{item?.Building?.Name}</Tag>
                        </>
                      );
                    })}
                  </div>
                );
              }
              return <></>;
            },
          },
        ]
      : []),

    {
      title: "Rol",
      dataIndex: ["Role", "Name"],
      key: "role",
      sorter: (a: any, b: any) => (a?.Role?.Name || '').localeCompare(b?.Role?.Name || '')
    },
    {
      title: "Durum",
      dataIndex: "Active",
      sorter: (a: any, b: any) => Number(a?.Active ?? 0) - Number(b?.Active ?? 0),
      render: (status: boolean) => {
        return (
          <>
            <MazakaBull type={status ? "Success" : "Error"} />
          </>
        );
      },
      key: "Active",
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "5%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        dataSource={
          users.data
            ? users.data.Data.filter((item: any) => {
                if (companyId) {
                  return item;
                }

                if (
                  filter.hasOwnProperty("IsCompanyUser") &&
                  !filter.IsCompanyUser
                ) {
                  const excludeRoles = [
                    "d9530b95-6cc4-4e13-8225-6d65d5b1e617",
                    "734354f3-c622-4750-a11c-04ab6f0fa497",
                  ];
                  return (
                    !item.CompanyId &&
                    !item.CompanyName &&
                    !excludeRoles.includes(item.RoleId)
                  );
                } else if (filter?.PageSize === -1) {
                  console.log(!item.CompanyId && item.CompanyName);
                  return !item.CompanyId && item.CompanyName;
                }

                return item;
              })
            : []
        }
        loading={users.isLoading || users.isFetching}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={
          companyId
            ? false
            : filter?.PageSize === -1
            ? false
            : {
                position: ["bottomRight"],
                className: "!px-0",
                onChange: handleChangePagination,
                total: users.data?.FilteredCount || 0,
                current: users.data?.PageIndex,
                pageSize: users.data?.PageSize,
                showLessItems: true,
                size: "small",
                showSizeChanger: true,
                locale: { items_per_page: "" },
                showTotal: (e) => `${e}`,
              }
        }
      />
      <Drawer
        title="Kullanıcıyı Güncelle"
        open={isShowEditDrawer}
        onClose={() => {
          setIsEditDrawer(false);
        }}
      >
        <>
          {isShowEditDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateUserForm
                selectedRecord={selectedRecord}
                setSelectedRecord={setSelectedRecord}
                onFinish={() => {
                  setIsEditDrawer(false);
                  queryClient.resetQueries({
                    queryKey: endpoints.getUserListFilter,
                    exact: false,
                  });
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </Col>
  );
};

export default UserTableList;
