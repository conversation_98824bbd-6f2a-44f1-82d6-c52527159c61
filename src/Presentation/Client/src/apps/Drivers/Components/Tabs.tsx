import { Tabs, TabsProps } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetUserFilter } from "../ClientSideStates";
import { useEffect, useState } from "react";

const UserTabs = () => {
  const onChange = (key: string) => {
    let currentFilter: any = { ...filter };
    if (key === "1") {
      currentFilter["PageSize"] = 20;
      currentFilter["RoleIds"] = ["98b3e5cc-cf74-4ee9-bc6a-6871c51a6469"];

    }
    dispatch( hanldleSetUserFilter({filter:{...currentFilter}}))
  };
  const dispatch = useDispatch();
  const { filter } = useSelector((state: RootState) => state.driver);
  const items: TabsProps["items"] = [
    {
      key: "1",
      label: "Şoförler",
    }
  ];
  useEffect(()=>{


  },[filter])
  const [activeKey,setActiveKey] = useState<string>("1")
  useEffect(()=>{
    if(filter?.IsCompanyUser)
    {
      setActiveKey("2")
    }
    if(!filter?.IsCompanyUser)
    {
      setActiveKey("1")
    }
    if(filter?.PageSize===-1)
    {
      setActiveKey("3")
    }
    if(filter?.RoleIds){
      setActiveKey("4")
    }


  },[filter])
  return (
    <>
      <Tabs
        size="small"
        className="!text-gray-300"
        activeKey={activeKey}
        defaultActiveKey="1"
        onChange={onChange}
        items={items}
      />
    </>
  );
};

export default UserTabs;
