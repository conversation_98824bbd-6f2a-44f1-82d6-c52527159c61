import { <PERSON><PERSON>, Col, Form, Row, Spin, Typography } from "antd";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import GeneralPhoneNumber from "apps/Common/GeneralPhoneNumber";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaSwitch } from "apps/Common/MazakaSwitch";
import {
  addBuildingUser,
  addUser,
  getUserListFilter,
  updateUserWithPut,
} from "apps/User/Services";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect, useState } from "react";
import { useQueryClient } from "react-query";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import endpoints from "apps/User/EndPoints";
import { MazakaPassword } from "apps/Common/MazakaPassword";
import GeneralDepartmentInput from "apps/Common/GeneralDepartmentInput";
import { handleSetAccountInfo } from "apps/Account/ClientSideStates";
import { DeleteOutlined, PlusOutlined } from "@ant-design/icons";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";
import AddOrUpdateIndex from "apps/Company/components/AddOrUpdate/AddOrUpdateIndex";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import GeneralUserRolesInput from "apps/Common/GeneralRolesInput";
import { MazakaButton } from "apps/Common/MazakaButton";
import { generateRandomPassword } from "helpers/GenerateRandomPass";

const AddOrUpdateUserForm: FC<any> = ({
  selectedRecord,
  setSelectedRecord,
  onFinish,
  companyData,
}) => {
  const { filter } = useSelector((state: RootState) => state.driver);
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { userInfo } = useSelector((state: RootState) => state.account);
  const dispatch = useDispatch();
  const [selectedRole, setSelectedRole] = useState<string | null>(null);

  const handleBuildingUser = async (userId: string) => {
    let formValues = form.getFieldsValue();
    let data = [];
    if (formValues?.BuildingIds?.length > 0) {
      data = formValues.BuildingIds.map((buildingId: string) => {
        return {
          UserId: userId,
          BuildingId: buildingId,
          IsAuditPerson: formValues["RoleId"]==="44a832a5-fbe2-43eb-8c27-0e5050abd9ea"?true:false,//siret yetkilisi
        };
      });

      try {
        await Promise.all(data.map((item: any) => addBuildingUser(item)));
      } catch (error) {
        showServiceErrorMessage(error, mazakaForm, "BuildingUser");
      }
    }
  };

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    formValues["PhoneNumber"] = formValues["PhoneNumber"].replace(/\D/g, "");
    if (formValues["PhoneNumber"]) {
      formValues["PhoneNumber"] =
        formValues["PhoneNumber"][0] === "0"
          ? formValues["PhoneNumber"].slice(1)
          : formValues["PhoneNumber"];
    }
    if (formValues["RoleId"] === "aaaa544b-b5f7-4bad-8c68-22c87005bfac") {
      formValues["IsMaster"] = true;
    }

    try {
      if (selectedRecord) {
        if (!filter?.IsCompanyUser && filter?.PageSize !== -1) {
          const departmantIds = formValues["DepartmentIds"] || [];

          const userDepartment = departmantIds.map((id: string) => {
            return {
              UserId: selectedRecord["Id"],
              DepartmentId: id,
            };
          });

          formValues["UserDepartment"] = userDepartment;

          delete formValues["DepartmentIds"];
        } else {
          const buildingIds = formValues["BuildingIds"] || [];
          const userBuilding = buildingIds.map((id: string) => {
            return {
              UserId: selectedRecord["Id"],
              BuildingId: id,
              IsAuditPerson: formValues["RoleId"]==="44a832a5-fbe2-43eb-8c27-0e5050abd9ea"?true:false,//siret yetkilisi
            };
          });

          formValues["BuildingUser"] = formValues["IsMaster"]
            ? []
            : userBuilding;
          delete formValues["BuildingIds"];
        }
        const data = { ...selectedRecord };
        formValues["Id"] = selectedRecord["Id"];
        if (!formValues?.Password) {
          delete formValues["Password"];
          delete data["Password"];
        }
        if (formValues["RoleId"] === "734354f3-c622-4750-a11c-04ab6f0fa497") {
          delete formValues["CompanyId"];
          delete data["CompanyId"];
          delete data["Company"];
        }
        const response: any = await updateUserWithPut({
          ...data,
          ...formValues,
        });
        if (response?.Value?.Id === userInfo?.Id) {
          dispatch(handleSetAccountInfo({ data: response.Value }));
        }
      } else {
        if (companyData) {
          formValues["CompanyId"] = companyData?.Id;
          formValues[" CompanyName"] = companyData?.Name;
        }
        if (!formValues?.Password) {
          formValues["Password"] = "Omid37180#";
        }
        if (
          formValues["RoleId"] === "734354f3-c622-4750-a11c-04ab6f0fa497" ||
          formValues["RoleId"] === "d9530b95-6cc4-4e13-8225-6d65d5b1e617"
        ) {
          delete formValues["CompanyId"];
        }
        const response: any = await addUser(formValues);
        handleBuildingUser(response?.Value?.Id);
      }

      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getUserListFilter,
        exact: false,
      });
      if (!companyData) {
        setSelectedCompanyId(null);
      }
      setSelectedRole(null);
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "User");

      console.log("Something went wrong during the add or update User", error);
    }
  };

  useEffect(() => {
    if (selectedRecord) {
      setInitialData(selectedRecord);
    }
  }, [selectedRecord]);

  const setInitialData = (selectedRecord: any) => {
    let data = { ...selectedRecord };
    data["DepartmentIds"] = data.UserDepartment.map(
      (item: any) => item.DepartmentId
    );
    data["BuildingIds"] = data?.BuildingUser.map(
      (item: any) => item?.BuildingId
    );
    data["Email"] = data.Email?.includes("tokgoz.com") ? undefined : data.Email;
    setSelectedCompanyId(companyData ? companyData.Id : data["CompanyId"]);
    if (data["RoleId"]) {
      setSelectedRole(data["RoleId"]);
    }
    if (data["RoleId"] === "aaaa544b-b5f7-4bad-8c68-22c87005bfac") {
      setIsShowBuildingUser(false);
    } else {
      setIsShowBuildingUser(true);
    }

    form.setFieldsValue({ ...data });
    form.setFieldValue("CompanyName", data?.CompanyName || undefined);
  };

  useEffect(() => {
    if (companyData) {
      setSelectedCompanyId(companyData?.Id);
    }
  }, [companyData]);

  const [isShowBuildingUser, setIsShowBuildingUser] = useState(false);

  const { Text } = Typography;
  const [isShowAddCompanyDrawer, setIsShowAddCompanyDrawer] = useState(false);
  const [selectedCompanyId, setSelectedCompanyId] = useState<string | null>(
    null
  );
  const [isLoadingUserInfoes, setIsLoadingUserInfoes] = useState(false);

  const getUserInfoes = async (value: string) => {
    setIsLoadingUserInfoes(true);

    let phone = value.replace(/\D/g, "");
    try {
      const response = await getUserListFilter({
        PhoneNumber: phone,
        PageSize: -1,
      });
      if (response?.Data?.length > 0) {
        setSelectedRecord(response?.Data[0]);
      } else {
        setSelectedRecord(null);
        setSelectedRole(null);
        if (companyData) {
          setSelectedCompanyId(companyData.Id);
        }
        setIsShowBuildingUser(false);
        form.resetFields([
          "Name",
          "Surname",
          "Email",
          "RoleId",
          "BuildingIds",
          "CompanyId",
          "DepartmentIds",
        ]);
      }
    } catch (error) {
      showServiceErrorMessage(error, {}, "getUserInfoes", true);
    } finally {
      setIsLoadingUserInfoes(false);
    }
  };




  return (
    <>
      <Col span={24}>
        <MazakaForm
          initialValues={{ Active: true, IsAuditPerson: false }}
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 10]}>
            <GeneralPhoneNumber
              name="PhoneNumber"
              label={"Telefon"}
              xs={24}
              className="!m-0"
              rules={[{ required: true, message: "" }]}
              onChange={(e: any) => {
                let value = e.target.value;
                if (!selectedRecord && value && !value.includes("_")) {
                  getUserInfoes(value);
                }
              }}
            />
            {isLoadingUserInfoes ? (
              <>
                <Spin />
              </>
            ) : (
              <>
                <MazakaInput
                  className="!m-0"
                  label={"Adı"}
                  placeholder="Adı"
                  name={"Name"}
                  xs={24}
                  rules={[{ required: true, message: "" }]}
                />
                <MazakaInput
                  className="!m-0"
                  label={"Soyadı"}
                  placeholder="Soyadı"
                  name={"Surname"}
                  xs={24}
                  rules={[{ required: true, message: "" }]}
                />
                {filter?.IsCompanyUser ||
                filter?.PageSize === -1 ||
                filter?.RoleIds ? (
                  <>
                    {!companyData && (
                      <>
                        <Col span={24}>
                          <Row>
                            {selectedRole &&
                              selectedRole !==
                                "734354f3-c622-4750-a11c-04ab6f0fa497" &&
                              selectedRole !==
                                "d9530b95-6cc4-4e13-8225-6d65d5b1e617" && ( //kalipci ve yapidenetim
                                <>
                                  <GeneralCompanies
                                    name="CompanyId"
                                    label={"Firma"}
                                    placeholder="Firma"
                                    className="!m-0"
                                    xs={24}
                                    onChange={(value: string, obj: any) => {
                                      form.setFieldValue(
                                        "CompanyName",
                                        obj.label
                                      );
                                      setSelectedCompanyId(value);
                                    }}
                                  />
                                  {!selectedRecord?.CompanyId &&
                                    filter?.PageSize === -1 && (
                                      <>
                                        <Col span={24}>
                                          <Button
                                            type="link"
                                            className="!flex items-center !pl-0"
                                          >
                                            <div className="!flex items-center gap-1">
                                              <PlusOutlined />
                                              <Text
                                                onClick={() => {
                                                  setIsShowAddCompanyDrawer(
                                                    true
                                                  );
                                                }}
                                                className="!text-primary !text-xs"
                                              >
                                                Firma Oluştur
                                              </Text>
                                            </div>
                                          </Button>
                                        </Col>
                                      </>
                                    )}
                                </>
                              )}
                          </Row>
                        </Col>
                        {filter?.PageSize === -1 && (
                          <>
                            <MazakaInput
                              name="CompanyName"
                              label={"Firma Adı"}
                              placeholder="Firma Adı"
                              className="!m-0"
                              xs={24}
                              disabled
                            />
                          </>
                        )}
                      </>
                    )}
                    <GeneralUserRolesInput
                      name="RoleId"
                      label={"Yetki Grubu"}
                      placeholder="Yetki Grubu"
                      className="!m-0"
                      xs={24}
                      onChange={(value: string) => {
                        console.log(value);
                        if (value) {
                          setSelectedRole(value);
                        }
                        if (value === "aaaa544b-b5f7-4bad-8c68-22c87005bfac") {
                          setIsShowBuildingUser(false);
                        } else {
                          setIsShowBuildingUser(true);
                        }
                      }}
                      rules={[{ required: true, message: "" }]}
                         externalFilteredData={[
                          "734354f3-c622-4750-a11c-04ab6f0fa497", // Yapı Denetim Personeli (küçük harf)
                          "44a832a5-fbe2-43eb-8c27-0e5050abd9ea", // Şantiye Görevlisi (küçük harf)
                          "aaaa544b-b5f7-4bad-8c68-22c87005bfac", // Şirket Açılışı (küçük harf)
                          "f6bcd1b9-328e-4008-9da2-2ce37f35940e", // Satış Temsilcisi (küçük harf)
                          "d9530b95-6cc4-4e13-8225-6d65d5b1e617", // Kalıpçı (küçük harf)
                          "5351cc0b-20d7-4dcc-8fba-90c797b3f5b8", // Santral Çalışanı (küçük harf)
                          "0f2a72f5-7dcf-4301-9240-ab806d804007", // Süper Admin (küçük harf)
                          "439aeada-02d0-4962-9dfd-bc41363461a3",  // Admin (küçük harf)
                        ]}
                    />
                  </>
                ) : (
                  <>
                    
                    <GeneralUserRolesInput
                      name="RoleId"
                      label={"Yetki Grubu"}
                      placeholder="Yetki Grubu"
                      className="!m-0"
                      xs={24}
                      rules={[{ required: true, message: "" }]}
                      onChange={(value: string | undefined) => {
                        console.log(value)
                        if (value) {
                          setSelectedRole(value);
                        }
                      }}
                         externalFilteredData={[
                          "734354f3-c622-4750-a11c-04ab6f0fa497", // Yapı Denetim Personeli (küçük harf)
                          "44a832a5-fbe2-43eb-8c27-0e5050abd9ea", // Şantiye Görevlisi (küçük harf)
                          "aaaa544b-b5f7-4bad-8c68-22c87005bfac", // Şirket Açılışı (küçük harf)
                          "f6bcd1b9-328e-4008-9da2-2ce37f35940e", // Satış Temsilcisi (küçük harf)
                          "d9530b95-6cc4-4e13-8225-6d65d5b1e617", // Kalıpçı (küçük harf)
                          "5351cc0b-20d7-4dcc-8fba-90c797b3f5b8", // Santral Çalışanı (küçük harf)
                          "0f2a72f5-7dcf-4301-9240-ab806d804007", // Süper Admin (küçük harf)
                          "439aeada-02d0-4962-9dfd-bc41363461a3",  // Admin (küçük harf)
                        ]}
                    />
                  </>
                )}
                {(filter?.IsCompanyUser || filter?.PageSize === -1) &&
                  isShowBuildingUser && (
                    <>
                      <>
                        <GeneralBuildingInput
                          name="BuildingIds"
                          label="Şantiye"
                          placeholder="Şantiye"
                          className="!m-0"
                          xs={24}
                          externalValueId={selectedCompanyId || ""}
                          mode="multiple"
                          rules={[{ required: true, message: "" }]}
                        />
                      </>
                    </>
                  )}

                <MazakaInput
                  className="!m-0"
                  label={"E-Posta"}
                  placeholder="E-Posta"
                  name={"Email"}
                  xs={24}
                  rules={[
                    { type: "email", message: "Geçersiz mail formatı" },

                    {
                      required:
                        filter.hasOwnProperty("IsCompanyUser") &&
                        !filter.IsCompanyUser &&
                        selectedRole !== "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469"
                          ? true
                          : false,
                      message: "",
                    },
                  ]}
                />

                {filter?.IsCompanyUser === false && (
                  <>
                    <MazakaPassword
                      name={"Password"}
                      label={"Şifre"}
                      xs={24}
                      className="!m-0"
                      prefix={
                        <>
                          <Text
                            className="!text-xs text-indigo-400 hover:cursor-pointer"
                            onClick={() => {
                              const newPassword = generateRandomPassword();
                              form.setFieldsValue({ Password: newPassword });
                            }}
                          >
                            Şifre Üret
                          </Text>
                        </>
                      }
                      rules={[
                        {
                          required:
                            !selectedRecord &&
                            selectedRole !==
                              "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469",
                          message: "",
                        },

                        {
                          pattern:
                            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()\-_=+{};:,<.>]).{6,}$/,
                          message:
                            "Şifre formati hatalı: 1 kücük harf 1 büyük harf ve bir sayı toplam 6 karakter",
                        },
                      ]}
                    />
                  </>
                )}

                <MazakaSwitch
                  name={"Active"}
                  label="Durum"
                  onChange={(status) => {
                    form.setFieldValue("Active", status);
                  }}
                  className="!m-0"
                />
               
                <Col xs={24} className="!flex justify-end gap-1">
                  <MazakaButton
                    htmlType="submit"
                    processType={formActions.submitProcessType}
                  >
                    Kaydet
                  </MazakaButton>
                  <MazakaButton
                    className="!bg-rose-500 !text-white"
                    icon={<DeleteOutlined />}
                    htmlType="button"
                    onClick={() => {
                      form.resetFields();
                    }}
                  >
                    Sıfırla
                  </MazakaButton>
                </Col>
              </>
            )}
          </Row>
        </MazakaForm>
      </Col>

      <MazakaDrawer
        title="Yeni Firma Ekle"
        placement="right"
        open={isShowAddCompanyDrawer}
        toggleVisible={() => {
          setIsShowAddCompanyDrawer(!isShowAddCompanyDrawer);
        }}
        layoutType="strecth"
      >
        <AddOrUpdateIndex />
      </MazakaDrawer>
    </>
  );
};

export default AddOrUpdateUserForm;
