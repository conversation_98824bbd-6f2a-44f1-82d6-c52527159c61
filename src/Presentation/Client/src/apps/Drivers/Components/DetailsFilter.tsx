import { Col, Form, Row } from "antd";
import GeneralPhoneNumber from "apps/Common/GeneralPhoneNumber";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetUserFilter } from "../ClientSideStates";
import GeneralUserRolesInput from "apps/Common/GeneralRolesInput";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { filter } = useSelector((state: RootState) => state.driver);
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    formValues["PhoneNumber"] = formValues["Phone"]
      ? formValues["Phone"].replace(/\D/g, "")
      : undefined;

    if (formValues["PhoneNumber"]) {
      formValues["PhoneNumber"] =
        formValues["PhoneNumber"][0] === "0"
          ? formValues["PhoneNumber"].slice(1)
          : formValues["PhoneNumber"];
    }
    let currentFilter = { ...filter };
    for (let key in formValues) {
      if (!formValues[key]) {
        delete formValues[key];
      }
      if (!currentFilter?.key) {
        delete currentFilter[key];
      }
    }
    const newFilter = { ...currentFilter, ...formValues };
    await dispatch(hanldleSetUserFilter({ filter: newFilter }));
    mazakaForm.setSuccess(1000, () => {}, "İşlem Başarılı");
    onFinish();
  };

  useEffect(() => {
    let data = { ...filter };
    if (data?.Phone) {
      data["PhoneNumber"] = data["PhoneNumber"].replace(
        /^0090(\d{3})(\d{3})(\d{4})$/,
        "+90 ($1) $2 $3"
      );
    }
    form.setFieldsValue({
      Email: data?.Email || undefined,
      Phone: data?.PhoneNumber || undefined,
      RoleId: data?.RoleId || undefined,
    });
  }, [filter]);
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 20]}>
            <MazakaInput
              className="!m-0"
              label={"E-Posta"}
              placeholder="E-Posta"
              name={"Email"}
              xs={24}
              rules={[{ type: "email", message: "Geçersiz mail formatı" }]}
            />
            <GeneralPhoneNumber
              name="Phone"
              label={"Telefon"}
              xs={24}
              className="!m-0"
            />

            <Col xs={24}>
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                Filtrele
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
