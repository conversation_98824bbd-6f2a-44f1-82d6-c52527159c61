import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect } from "react";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";
import GeneralPhoneNumber from "apps/Common/GeneralPhoneNumber";
import MazakaCard from "apps/Common/MazakaCart";
import { updateUserWithPut } from "../Services";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import { handleSetAccountInfo } from "apps/Account/ClientSideStates";
import { useQueryClient } from "react-query";
import endpoints from "apps/User/EndPoints";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";


const Profile: FC<GeneralAddOrUpdateFormProps> = ({
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { userInfo } = useSelector((state: RootState) => state.account);
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const dispatch = useDispatch()
  useEffect(() => {
    form.setFieldsValue({ ...userInfo });
    form.setFieldValue("CompanyName", "Tokgöz");
  }, [userInfo]);
  const handleOnFinish = async () => {
    mazakaForm.setLoading();

    let formValues = form.getFieldsValue();
    formValues["PhoneNumber"] = formValues["PhoneNumber"].replace(/\D/g, "");
  
    delete formValues["CompanyName"];
    formValues["CompanyId"] = null;
    formValues["RoleId"] = userInfo["RoleId"];

    try {
     const response:any =  await updateUserWithPut({...userInfo,...formValues});
     if(response?.Value)
     {
      dispatch( handleSetAccountInfo({data:response.Value}))
     }
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getUserListFilter,
        exact: false,
      });
    } catch (error: any) {
      showServiceErrorMessage(error,mazakaForm,"profile")
      
    }
  };
  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });
    }
  }, [selectedRecord]);
  const tokgozRoleList = [
    { label: "Admin", value: "439aeada-02d0-4962-9dfd-bc41363461a3" },
    { label: "Satıcı", value: "f6bcd1b9-328e-4008-9da2-2ce37f35940e" },
    { label: "İstasyon", value: "5351cc0b-20d7-4dcc-8fba-90c797b3f5b8" },
    { label: "Şoför", value: "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469" },
    { label: "Muhasebeci", value: "832735cd-3bdb-42c6-9501-cf34f71890a9" },
  ];

  return (
    <>
      <MazakaCard
        span={24}
        title="Profil"
        description="Profil sayfası bilgilerinizin güncelleyebilmenizi sağlar"
      >
        <Col span={24}>
          <MazakaForm form={form} onFinish={handleOnFinish} {...formActions}>
            <Row gutter={[20, 20]}>
              <MazakaInput
                className="!m-0"
                label={"Adı"}
                placeholder="Adı"
                name={"Name"}
                xs={24}
                md={12}
                lg={6}
                rules={[{ required: true, message: "" }]}
              />
              <MazakaInput
                className="!m-0"
                label={"Soyadı"}
                placeholder="Soyadı"
                name={"Surname"}
                xs={24}
                md={12}
                lg={6}
                rules={[{ required: true, message: "" }]}
              />

              <MazakaInput
                className="!m-0"
                label={"E-Posta"}
                placeholder="E-Posta"
                name={"Email"}
                xs={24}
                md={12}
                lg={6}
                rules={[
                  { type: "email", message: "Geçersiz mail formatı" },
                  { required: true, message: "" },
                ]}
              />
              <GeneralPhoneNumber
                name="PhoneNumber"
                label={"Telefon"}
                xs={24}
                md={12}
                lg={6}
                className="!m-0"
                rules={[{ required: true, message: "" }]}
              />

              <MazakaSelect
                name="RoleId"
                label={"Rol"}
                placeholder="Rol"
                className="!m-0"
                xs={24}
                md={12}
                options={tokgozRoleList}
                rules={[{ required: true, message: "" }]}
                disabled={true}
              />
              <MazakaInput
                className="!m-0"
                label={"Firma"}
                placeholder="Firma"
                name={"CompanyName"}
                xs={24}
                md={12}
                disabled
              />
            </Row>
          </MazakaForm>
        </Col>
      </MazakaCard>
    </>
  );
};

export default Profile;
