import { Route } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";
const DriverIndex  = lazy(() => import('./DriverIndex'))
const Profile  = lazy(() => import('./Components/Profile'))


export const driverRouteList = [


  <Route key={"DriverRouteList"}>
    <Route
      path={"drivers/list"}
      element={
       <Suspense fallback={<Spin/>} >

         <DriverIndex />
       </Suspense>
      
      }
    />
    <Route
      path={"drivers/profile"}
      element={
        <Suspense fallback={<Spin/>} >

          <Profile />
        </Suspense>
        
      }
    />
  </Route>,

];
