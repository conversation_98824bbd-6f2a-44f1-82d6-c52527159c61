import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  filter: {
    PageIndex: 1,
    PageSize: 20,
    IsCompanyUser:false,
    IncludeProperties:["UserDepartment","UserRole.Role","Company","BuildingUser.Building"],
    SortProperty:"Active",
    SortType:"ASC",
    RoleIds:["98b3e5cc-cf74-4ee9-bc6a-6871c51a6469"]
  },
  showIsNewCustomer:false,
};

const driverSlice = createSlice({
  name: "driverSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetUserFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    hanldleSetIsNewCustomer: (state, action) => {
      let data = action.payload;
      state.showIsNewCustomer = data.status;
    },
    handleResetAllFieldsUser: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterUser: (state) => {
       state.filter = {
        PageIndex: 1,
        PageSize: 20,
        IsCompanyUser:false,
        IncludeProperties:["UserDepartment","UserRole.Role","Company","BuildingUser.Building"],
        SortProperty:"Active",
        SortType:"ASC",
        RoleIds:["98b3e5cc-cf74-4ee9-bc6a-6871c51a6469"]

      }
      },
  },
});

export const { handleResetAllFieldsUser,handleResetFilterUser,hanldleSetUserFilter, hanldleSetIsNewCustomer } = driverSlice.actions;
export default driverSlice;
