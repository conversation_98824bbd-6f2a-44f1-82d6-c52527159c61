import {deleteRequest, get, patch, post, put} from "services/BaseClient/Client"
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/User/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";
import { AddUserFormModel, UserListAndDetails } from "apps/Account/Models";




export const getUserListFilter = async (filter: any): Promise<DataResponse<UserListAndDetails>> => {
 
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getUserListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<UserListAndDetails>>(url, config);
  };


export const getBuildingUserListFilter = async (filter: any): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getBuildingUserListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<UserListAndDetails>>(url, config);
  };
  export const getUserRoleList = async (): Promise<DataResponse<any>> => {
    
      const url = `${endpoints.getUserRoleListFilter}?`;
      const config = headers.content_type.application_json;
      return get<DataResponse<UserListAndDetails>>(url, config);
    };
  


  export const getUserDepartmentListFilter = async (filter: any): Promise<DataResponse<any>> => {
    if(filter){
      
      const query = CreateUrlFilter(filter)
        const url = `${endpoints.getUserDepartmentListFilter}?${query}`;
        const config = headers.content_type.application_json;
        return get<DataResponse<any>>(url, config);
    }
    else{
      return new Promise((resovle:any, reject) => {
        resovle([]);
      });
    }
    };

  export const addUser = async (data: any): Promise<DataResponse<AddUserFormModel>> => {
    const url = `${endpoints.addUser}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<UserListAndDetails>>(url, data, config);
  };
  export const addBuildingUser = async (data: any): Promise<DataResponse<AddUserFormModel>> => {
    const url = `${endpoints.addBuildingUser}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<UserListAndDetails>>(url, data, config);
  };
  export const addUserDepartment = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.addUserDepartment}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<any>>(url, data, config);
  };
  export const updateUserWithPut = async (data: any): Promise<DataResponse<UserListAndDetails>> => {
    const url = `${endpoints.updateUserWithPut}`;
    const config = headers.content_type.application_json;
    return put<DataResponse<UserListAndDetails>>(url, data, config);
  };

  export const updateUserWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateUserWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
  };
  export const deleteUser = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.deleteUser}/${data.Id}`;
    const config = headers.content_type.application_json;
    return deleteRequest<DataResponse<UserListAndDetails>>(url, data, config);
  };

  