import {get, post} from "services/BaseClient/Client"
import endPoints from "apps/Account/EndPoints"
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import { Token } from "./Models";



export const getToken = async (data: any): Promise<DataResponse<Token>> => {
    const url = `${endPoints.getToken}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<Token>>(url, data, config);
  };

  export const getUserInfo = async (): Promise<DataResponse<any>> => {
    const url = `${endPoints.userInfo}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  };

  export const forgotPassword = async (data:any): Promise<DataResponse<any>> => {
    const url = `${endPoints.forgotPassword}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<any>>(url,data ,config);
  };
  export const resetPassword = async (data:any): Promise<DataResponse<any>> => {
    const url = `${endPoints.resetPassword}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<any>>(url,data ,config);
  }
