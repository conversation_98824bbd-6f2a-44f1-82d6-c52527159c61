import "swiper/css";
import "swiper/css/pagination";
import "apps/Account/styles/LoginAdvertising.css";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, A11y } from "swiper/modules";
import slider1 from "assets/Images/TokGoz/slider1.png";
import slider2 from "assets/Images/TokGoz/slider2.png";
import slider3 from "assets/Images/TokGoz/slider3.png";

export const LoginAdvertisingSlider = () => {
  const sliderJson = [
    {
      id: 1,
      image: slider1,
      imageType: "png",
    },
    {
      id: 2,
      image: "https://turkiyebeton.com/data/post/715/image.jpg",
      imageType: "png",
    },
    {
      id: 3,
      image: "https://assets.bwbx.io/images/users/iqjWHBFdfxIU/ir8TwU4Xwn0U/v1/-1x-1.jpg",
      imageType: "png",
    },
  ];

  return (
    <Swiper
      className="advertising-slider h-screen"
      modules={[Pagination, A11y]}
      spaceBetween={50}
      slidesPerView={1}
      direction={"vertical"}
      autoplay={{ delay: 3000 }}
      pagination={{ clickable: true }}
    >
      {sliderJson.map((item) => {
        return (
          <>
            <SwiperSlide key={item.id}>
              <img src={item.image} alt={`${item.id}`} />
            </SwiperSlide>
          </>
        );
      })}
    </Swiper>
  );
};
