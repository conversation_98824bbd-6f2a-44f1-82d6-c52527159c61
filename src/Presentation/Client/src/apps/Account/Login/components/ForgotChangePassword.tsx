import { Col, Form, Row, Typography } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaPassword } from "apps/Common/MazakaPassword";
import useMazakaForm from "hooks/useMazakaForm";
import { FC } from "react";
import LoginLogoImage from "./LoginLogoImage";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { resetPassword } from "apps/Account/Services";

interface ForgotChangePasswordProps {
  email: string;
  onFinish: any;
}
const ForgotChangePassword: FC<ForgotChangePasswordProps> = (props: any) => {
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form, {
    submitText: "Şifreyi <PERSON>ğiştir",
  });
  const { Title } = Typography;
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    try {
      formValues["Email"] = props.email;
      await resetPassword(formValues);
      mazakaForm.setSuccess(1000, () => {}, "Şifreniz değiştirildi");
      props.onfinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ForgotResetPassword");
    }
  };
  return (
    <>
      <Col span={24}>
        <LoginLogoImage />
        <Col span={24} className="!flex justify-center">
          <Title level={4}>Şifremi Değiştir</Title>
        </Col>
        <MazakaForm
          form={form}
          initialValues={{ remember: true }}
          onFinish={handleOnFinish}
          submitButtonXPosition="left"
          {...formActions}
        >
          <Row gutter={[20, 20]}>
            <MazakaInput
              span={24}
              label={"Kod"}
              placeholder={"Kod"}
              name="ResetCode"
              rules={[{ required: true, message: "" }]}
              className="!m-0"
            />

            <MazakaPassword
              name={"NewPassword"}
              rules={[{ required: true, message: "" }, {
                pattern:
                  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()\-_=+{};:,<.>]).{6,}$/,
                message: "Sifre formati Hatali 1 kucuk harf 1 buyuk harf ve bir sayi toplam 6 karakter",
              },]}
              placeholder={"Yeni Şifre"}
              label={"Yeni Şifre"}
              span={24}
              className="!m-0"
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ForgotChangePassword;
