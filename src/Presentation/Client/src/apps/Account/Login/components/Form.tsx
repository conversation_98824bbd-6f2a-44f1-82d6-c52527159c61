import { Col, Form, Row, Typography } from "antd";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaPassword } from "apps/Common/MazakaPassword";
import useMazakaForm from "hooks/useMazakaForm";
import { FC } from "react";
import { useNavigate } from "react-router-dom";
import LoginLogoImage from "./LoginLogoImage";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import { getToken } from "apps/Account/Services";
import { openNotificationWithIcon } from "helpers/Notifications";

interface LoginFormProps {
  setIsShowForgotPassword: React.Dispatch<React.SetStateAction<boolean>>;
}

const LoginForm: FC<LoginFormProps> = (props) => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form, {
    submitText: "Giriş Yap",
  });
  const { Text, Title } = Typography;

  const handleOnFinish = async () => {
 
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    try {
      const response:any= await getToken(formValues);
      mazakaForm.setSuccess(
        1000,
        () => {
          
          localStorage.setItem("access_token",response.AccessToken)
          navigate("/dashboard");
        },
       "Giriş Başarılı"
      );
    
    
    
    } catch (error: any) {
      
      mazakaForm.setFailed(2000,error?.Value?.Message|| "Email ve ya şifre hatalı");
      openNotificationWithIcon("error","Email ve ya şifre hatalı")
     
      console.log("Somethings wrong during login to dashboard", error);

    }
  };
  return (
    <>
      <Row className="!w-full !px-4">
        <Col span={24}>
          <MazakaForm submitButtonVisible={false} form={form} onFinish={handleOnFinish}  >
            <LoginLogoImage />
            <Col span={24} className="!flex justify-center">
              <Title level={4}>Giriş</Title>
            </Col>
            <Row gutter={[10, 20]}>
              <MazakaInput
                xs={24}
                name={"Email"}
                className="!m-0"
                rules={[
                  {
                    type: "email",
                    message: "E-Posta Formatı Geçersiz",
                  },
                  { required: true, message: "" },
                ]}
                placeholder={"E-Posta"}
              />
              <Col span={24}>
                <Row gutter={[10, 10]}>
                  <MazakaPassword
                    name={"Password"}
                    rules={[{ required: true, message: "" }, {
                      // pattern:
                      //   /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()\-_=+{};:,<.>]).{6,}$/,
                      // message: "Sifre formati Hatali 1 kucuk harf 1 buyuk harf ve bir sayi toplam 6 karakter",
                    },]}
                    placeholder={"Şifre"}
                    className="!m-0"
                    span={24}
                  />
                  <Col span={24} className="!flex justify-end">
                    <Text
                      onClick={() => {
                        props.setIsShowForgotPassword(true);
                      }}
                      className="!text-[#4d37c4] !text-xs hover:cursor-pointer"
                    >
                      Şifremi Unuttum
                    </Text>
                  </Col>
                </Row>
              </Col>
              <Col span={24}>
                <MazakaButton htmlType="submit" block processType={formActions.submitProcessType}>
                  Giriş Yap
                </MazakaButton>
              </Col>
            </Row>
          </MazakaForm>
        </Col>
      </Row>
    </>
  );
};

export default LoginForm;
