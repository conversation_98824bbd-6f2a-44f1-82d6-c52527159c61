import { Col, Form, Row, Typography, <PERSON><PERSON>, Modal } from "antd";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useState } from "react";
import LoginLogoImage from "./LoginLogoImage";
import ForgotChangePassword from "./ForgotChangePassword";
import { MazakaButton } from "apps/Common/MazakaButton";
import { forgotPassword } from "apps/Account/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";

interface ForgotPasswordFormProps {
  setIsShowForgotPassword: React.Dispatch<React.SetStateAction<boolean>>;
}

const ForgotPassword: FC<ForgotPasswordFormProps> = (props) => {
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form, {
    submitText: "E-Posta Gönder",
  });
  const { Text, Title } = Typography;

  const [isShowChangePasswordModal, setIsChangePasswordModal] = useState(false);
  const [selectedEmail,setSelectedEmail] = useState<string |null>(null)

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
 
    setSelectedEmail(formValues["Email"])
    try {
      await forgotPassword(formValues);
      mazakaForm.setSuccess(1000, () => {}, "E-Posta Gönderildi");
      
      setIsChangePasswordModal(true);
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ForgotPassword");
    }
  };
  return (
    <>
      <Row className="!w-full !px-4">
        <Col span={24}>
          <Form form={form} onFinish={handleOnFinish}>
            <LoginLogoImage />
            <Col span={24} className="!flex justify-center">
              <Title level={4}>Şifremi Unuttum</Title>
            </Col>
            <Row gutter={[10, 20]}>
              <Col span={24}>
                <Row gutter={[10, 10]}>
                  <MazakaInput
                    xs={24}
                    name={"Email"}
                    className="!m-0"
                    rules={[
                      {
                        type: "email",
                        message: "E-Posta Formatı Geçersiz",
                      },
                      { required: true, message: "" },
                    ]}
                    placeholder={"E-Posta"}
                  />
                  <Col span={24} className="!flex justify-end">
                    <Text
                      onClick={() => {
                        props.setIsShowForgotPassword(false);
                      }}
                      className="!text-[#4d37c4] !text-xs hover:cursor-pointer"
                    >
                      Giriş Yap
                    </Text>
                  </Col>
                </Row>
              </Col>
              <Col span={24}>
                <MazakaButton block htmlType="submit" processType={formActions.submitProcessType}>
                  E-Posta Gönder
                </MazakaButton>
              </Col>
            </Row>
          </Form>
        </Col>
      </Row>
    
        <>
      <Modal
        title={""}
        open={isShowChangePasswordModal}
        onCancel={() => {
          setIsChangePasswordModal(false);
        }}
        footer={false}
      >
        <ForgotChangePassword
        email={selectedEmail||""}
          onFinish={() => {
            form.resetFields(["email"]);
            props.setIsShowForgotPassword(false);
          }}
        />
      </Modal>
        </>
      
    </>
  );
};

export default ForgotPassword;
