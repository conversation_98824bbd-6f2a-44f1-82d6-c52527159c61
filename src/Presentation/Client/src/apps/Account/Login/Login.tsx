import { Col, Image, Row } from "antd";
import LoginForm from "./components/Form";
import ForgotPassword from "./components/ForgotPassword";
import { useEffect, useState } from "react";
import BackgroundImage from "assets/Images/TokGoz/SignInIntroduction.webp"
import { useNavigate } from "react-router-dom";

const Login = () => {
  const [isShowForgotPassword, setIsShowForgotPassword] = useState(false);
  const navigate = useNavigate()
  useEffect(()=>{
    const token = localStorage.getItem("access_token");
    if(token)
    {
      navigate("/dashboard")
    }
  },[])
  return (
    <>
    
      <div className="login ">
        <Row>
          <Col
            xs={24}
            sm={24}
            lg={14}
            className="h-screen flex justify-center items-center !w-full"
          >
            <Image preview={false} src={BackgroundImage} width={"100%"} height={"100%"} style={{objectFit:"cover"}} />
          </Col>
          <Col
            xs={24}
            sm={24}
            lg={10}
            className="login-form-area bg-neutral-200 !flex justify-center items-center "
          >
            {/* <div className="absolute top-0 right-0 m-10 w-22">
            <SelectLanguage />
          </div> */}
            <div className="w-full  lg:!w-1/2 xl:!w-1/2 xxl:!w-1/2 !bg-white !h-[400px] !flex rounded-lg ">
              {isShowForgotPassword ? (
                <ForgotPassword
                  setIsShowForgotPassword={setIsShowForgotPassword}
                />
              ) : (
                <LoginForm setIsShowForgotPassword={setIsShowForgotPassword} />
              )}
            </div>
          </Col>
        </Row>
      </div>
    </>
  );
};

export default Login;
