import { Route } from "react-router-dom";
import { Spin } from "antd";
import React, { lazy, Suspense } from 'react'
const Login = lazy(() => import('apps/Account/Login/Login'))


export const accountRouteList = [
  <Route key={"AccountRouteList"}>
    <Route
      path={"account/login"}
      element={
        <Suspense fallback={<Spin/>}>
          <Login />
        </Suspense>
      }
    />
  </Route>,
];
