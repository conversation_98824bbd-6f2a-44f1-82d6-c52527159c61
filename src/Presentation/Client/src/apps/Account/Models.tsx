import { CompanyListAndDetails } from "apps/Company/Models";


interface BaseSharedUser{
  
  Active: boolean;
  Email: string;
  Password: string;
  Name: string;
  Surname: string;
  CompanyName: string;
  PhoneNumber: string;
  Company:CompanyListAndDetails,
  CompanyId:string,
}
export interface AddUserFormModel extends BaseSharedUser{

}

export interface UserListAndDetails extends BaseSharedUser{
Id: string;

}

export interface Token {
    access_token: string;
    expires_in: string;
    refresh_token: string;
    scope: string;
    token_type: string;
  }