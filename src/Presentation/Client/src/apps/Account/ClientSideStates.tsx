import { createSlice } from "@reduxjs/toolkit";

interface AccountInitialStateModel {
  userInfo:any;
}
const accountInitialState: AccountInitialStateModel = {
  userInfo:null
};

const accountSlice = createSlice({
  name: "accountSlice ",
  initialState: accountInitialState,
  reducers: {
    handleSetAccountInfo: (state, action) => {
      state.userInfo = action.payload.data;
    },
   
    // handleResetBlogPost: (state) => {
    //   Object.assign(state, blogPostInitialState);
    // },
  },
});

export const {
  handleSetAccountInfo,
} = accountSlice.actions;
export default accountSlice;
