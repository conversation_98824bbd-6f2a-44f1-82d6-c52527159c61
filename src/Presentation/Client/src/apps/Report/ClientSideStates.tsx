import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  delayFilter: {
    PageIndex: 1,
    PageSize: -1,
  },
  vehicleFilter: {
    PageIndex: 1,
    PageSize: -1,
  },
  dailyManagerFilter: {
    PageIndex: 1,
    PageSize: -1,
  },
  dailyCustomerFilter: {
    PageIndex: 1,
    PageSize: -1,
  },
  requestTransactionFilter: {
    PageIndex: 1,
    PageSize: -1,
  },
  returnReportFilter: {
    PageIndex: 1,
    PageSize: -1,
  },
  guaranteedPriceFilter: {
    PageIndex: 1,
    PageSize: -1,
  },
};

const reportSlice = createSlice({
  name: "reportSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetReportFilter: (state, action) => {
      let data = action.payload;
      switch (data.type) {
        case "delayFilter":
          state.delayFilter = data.filter;
          break;
        case "vehicleFilter":
          state.vehicleFilter = data.filter;
          break;
        case "dailyManagerFilter":
          state.dailyManagerFilter = data.filter;
          break;
        case "dailyCustomerFilter":
          state.dailyCustomerFilter = data.filter;
          break;
        case "requestTransactionFilter":
          state.requestTransactionFilter = data.filter;
          break;
        case "returnReportFilter":
          state.returnReportFilter = data.filter;
          break;
        case "guaranteedPriceFilter":
          state.guaranteedPriceFilter = data.filter;
          break;
      }
    },
    handleResetReportFilter: (state, action) => {
      const type = action.payload;
      switch (type) {
        case "delayFilter":
          state.delayFilter = {
            PageIndex: 1,
            PageSize: -1,
          };
          break;
        case "vehicleFilter":
          state.vehicleFilter = {
            PageIndex: 1,
            PageSize: -1,
          };
          break;
        case "dailyManagerFilter":
          state.dailyManagerFilter = {
            PageIndex: 1,
            PageSize: -1,
          };
          break;

        case "dailyCustomerFilter":
          state.dailyCustomerFilter = {
            PageIndex: 1,
            PageSize: -1,
          };
          break;
        case "requestTransactionFilter":
          state.requestTransactionFilter = {
            PageIndex: 1,
            PageSize: -1,
          };
          break;
        case "returnReportFilter":
          state.returnReportFilter = {
            PageIndex: 1,
            PageSize: -1,
          };
          break;
        
          case "guaranteedPriceFilter":
            state.guaranteedPriceFilter = {
              PageIndex: 1,
              PageSize: -1,
            };
            break;
        
      }
    },

    handleResetAllFieldsReportDelayFilter: (state) => {
      Object.assign(state, InitialState);
    },
  },
});

export const {
  handleResetAllFieldsReportDelayFilter,
  hanldleSetReportFilter,
  handleResetReportFilter,
} = reportSlice.actions;
export default reportSlice;
