import { Col, Table } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

import { hanldleSetReportFilter } from "apps/Report/ClientSideStates";
import { useGetDelayRequestReport } from "apps/Report/ServerSideStates";
import { useEffect } from "react";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";

const DelayRequestTableList = () => {
  const { delayFilter } = useSelector((state: RootState) => state.report);
  const dispatch = useDispatch();

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...delayFilter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(
      hanldleSetReportFilter({ filter: newFilter, type: "delayFilter" })
    );
  };

  const delayRequests: any = useGetDelayRequestReport(delayFilter);
  useEffect(() => {
    // Every rendered this component needed to send reqeust
    delayRequests.refetch();
  }, []);

  useEffect(() => {
    if (delayRequests.isError) {
      showServiceErrorMessage(
        delayRequests.error,
        {},
        "DelayRequestReport",
        true
      );
    }
  }, [delayRequests.isError]);

  const columns = [
    {
      title: "Santral",
      dataIndex: "Station",
      key: "StationName",
      sorter: (a: any, b: any) => a?.Station?.localeCompare(b?.Station)
    },
    {
      title: "Firma",
      dataIndex: "Company",
      key: "Company",
      width:"15%",
      sorter: (a: any, b: any) => a?.Company?.localeCompare(b?.Company)
    },
    {
      title: "Şantiye",
      dataIndex: "Building",
      key: "Building",
      sorter: (a: any, b: any) => a?.Building?.localeCompare(b?.Building)

    },
    {
      title: "Talep Tarihi",
      dataIndex: "RequestedDate",
      key: "RequestedDate",
      render: (text: any) => new Date(text).toLocaleDateString("tr-TR"),
      sorter: (a: any, b: any) => a?.RequestedDate?.localeCompare(b?.RequestedDate)

    },
    {
      title: "Onaylanma Tarihi",
      dataIndex: "ApprovedDate",
      key: "ApprovedDate",
      render: (text: any) => new Date(text).toLocaleDateString("tr-TR"),
      sorter: (a: any, b: any) => a?.ApprovedDate?.localeCompare(b?.ApprovedDate)

    },
    {
      title: " Toplam Talep(m³)",
      dataIndex: "DesiredTotalConcrete",
      key: "DesiredTotalConcrete",
      sorter: (a: any, b: any) => Number(a?.DesiredTotalConcrete ?? 0) - Number(b?.DesiredTotalConcrete ?? 0)
    },
    {
      title: "Tamamlanan(m³)",
      dataIndex: "CompletedTotalConcrete",
      key: "CompletedTotalConcrete",
      sorter: (a: any, b: any) => Number(a?.CompletedTotalConcrete ?? 0) - Number(b?.CompletedTotalConcrete ?? 0)
    },
    {
      title: "Net Fark(m³)",
      key: "DifferenceConcrete",
      render: (_: any, record: any) => {
        const difference = (record.DesiredTotalConcrete ?? 0) - (record.CompletedTotalConcrete ?? 0);
        const color = difference > 0 ? "green" : difference < 0 ? "red" : "black";
        return <span style={{ color }}>{difference.toFixed(0)}</span>;
      },
      sorter: (a: any, b: any) =>
        ((a.DesiredTotalConcrete ?? 0) - (a.CompletedTotalConcrete ?? 0)) -
        ((b.DesiredTotalConcrete ?? 0) - (b.CompletedTotalConcrete ?? 0)),
    },
    {
      title: "Gecikme Süresi(DK)",
      dataIndex: "DelayTime",
      key: "DelayTime",
      sorter: (a: any, b: any) => Number(a?.DelayTime ?? 0) - Number(b?.DelayTime ?? 0)
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={delayRequests.isLoading || delayRequests.isFetching}
        dataSource={delayRequests.data ? delayRequests.data.Value : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: delayRequests.data?.FilteredCount || 0,
          current: delayRequests.data?.PageIndex,
          pageSize: delayRequests.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </Col>
  );
};

export default DelayRequestTableList;
