
import {  Col, Drawer, Row } from "antd";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { useState } from "react";
import DetailsFilter from "./Components/DetailsFilter";
import DelayRequestTableList from "./Components/DelayRequestTableList";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { handleResetReportFilter } from "apps/Report/ClientSideStates";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";


const DelayRequestReportIndex = () => {
 
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] = useState(false);
  const { delayFilter } = useSelector((state: RootState) => state.report);
  return (
    <>
      <MazakaLayout
        title={"Gecikmiş Sevkiyat Raporu"}
        headDescription={"Gecikmiş sevkiyat rapor sayfası, sistemde kayıtlı tüm gecikmiş sevkiyat listesini görüntülemenizi sağlar"}
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <MazakaDetailsFilterButton setIsShowDetailsFilter={setIsShowFilterDetailsDrawer} />
            {
              Object.entries(delayFilter).length>2&&
              <>
            <MazakaClearFilterButton type="delayFilter" actionFunk={handleResetReportFilter} />
              </>
            }
            
          </Col>
          <Col span={24}>
         <DelayRequestTableList/>
          </Col>
        </Row>
      </MazakaLayout>
      <Drawer title="Detaylı Filtre" open={isShowFilterDetailsDrawer} onClose={()=>{setIsShowFilterDetailsDrawer(false)}}>
        <DetailsFilter 
        onFinish={()=>{
          setIsShowFilterDetailsDrawer(false)
        }}
        />
    </Drawer>

     
    </>
  );
};

export default DelayRequestReportIndex;
