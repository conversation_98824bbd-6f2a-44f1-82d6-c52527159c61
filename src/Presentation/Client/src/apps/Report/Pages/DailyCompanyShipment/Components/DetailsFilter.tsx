import { Col, Form, Row } from "antd";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import GeneralCarInput from "apps/Common/GeneralCarInput";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import GeneralProductInput from "apps/Common/GeneralProductInput";
import GeneralUserInput from "apps/Common/GeneralUserInput";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaRangePicker } from "apps/Common/MazakaRangePicker";
import { hanldleSetReportFilter } from "apps/Report/ClientSideStates";
import dayjs from "dayjs";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const DetailsFilter:FC<GeneralDetailsFilterProps> = ({onFinish}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const {   dailyCustomerFilter} = useSelector((state: RootState) => state.report);
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    let currentFilter = {...dailyCustomerFilter}
    for (let key in formValues) {
      if (!formValues[key]) {
        delete formValues[key];
      }
      if(!currentFilter?.key)
      {
        delete currentFilter[key]
      }
    }
    if (formValues?.Date && formValues["Date"][0] && formValues["Date"][1]) {
      formValues["ApprovedStartDate"] = dayjs(formValues["Date"][0]).format(
        "YYYY-MM-DD"
      );
      formValues["ApprovedEndDate"] = dayjs(formValues["Date"][1]).format(
        "YYYY-MM-DD"
      );
      delete formValues["Date"]
    } else {
      mazakaForm.setFailed(
        2000,
        "Tarih aralığnda Başlangıç ve bitiş tarihi zorunlu "
      );
    }
    if (formValues?.InsertDate && formValues["InsertDate"][0] && formValues["InsertDate"][1]) {
      formValues["InsertStartDate"] = dayjs(formValues["InsertDate"][0]).format(
        "YYYY-MM-DD"
      );
      formValues["InsertEndDate"] = dayjs(formValues["InsertDate"][1]).format(
        "YYYY-MM-DD"
      );
      delete formValues["InsertDate"]
    } else {
      mazakaForm.setFailed(
        2000,
        "Tarih aralığnda Başlangıç ve bitiş tarihi zorunlu "
      );
    }
    const newFilter = { ...currentFilter, ...formValues };
    await dispatch(hanldleSetReportFilter({ filter: newFilter,type:"dailyCustomerFilter" }));
    mazakaForm.setSuccess(1000, () => {}, "Başarılı");
    onFinish()
  };

  useEffect(()=>{
    const startDate = dailyCustomerFilter["ApprovedStartDate"]
    ? dayjs(dailyCustomerFilter["ApprovedStartDate"])
    : null;
    const startInsertDate = dailyCustomerFilter["InsertStartDate"]
    ? dayjs(dailyCustomerFilter["InsertStartDate"])
    : null;
    const endInsertDate = dailyCustomerFilter["InsertEndDate"]
    ? dayjs(dailyCustomerFilter["InsertEndDate"])
    : null;
  const endDate = dailyCustomerFilter["ApprovedEndDate"]
    ? dayjs(dailyCustomerFilter["ApprovedEndDate"])
    : null;
  const dateRange = startDate && endDate ? [startDate, endDate] : [];
  const insertDateRange = startInsertDate && endInsertDate ? [startInsertDate, endInsertDate] : [];
    form.setFieldsValue({
      CompanyId:dailyCustomerFilter?.CompanyId||undefined,
      Date:dateRange || undefined,
      InsertDate:insertDateRange || undefined,
      BuildingId:dailyCustomerFilter.BuildingId || undefined,
      ProductId:dailyCustomerFilter.ProductId || undefined,
      DriverId:dailyCustomerFilter.DriverId || undefined,
      VehicleId:dailyCustomerFilter.VehicleId || undefined,
    })
  },[dailyCustomerFilter])
  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} submitButtonVisible={false}>
          <Row gutter={[0, 10]}>
          <GeneralCompanies
              name="CompanyId"
              label={"Firma"}
              placeholder="Firma"
              className="!m-0"
              xs={24}
              allowClear={true}
            />
            
            <GeneralBuildingInput
              name="BuildingId"
              label={"Şantiye"}
              placeholder="Şantiye"
              className="!m-0"
              xs={24}
              allowClear={true}
            />
            
            <GeneralProductInput
              name="ProductId"
              label={"Ürün"}
              placeholder={"Ürün"}
              className="!m-0"
              xs={24}
              allowClear={true}
            />
            <GeneralUserInput
              name="DriverId"
              label={"Şoför"}
              placeholder="Şoför"
              className="!m-0"
              allowClear
              xs={24}
              externalFilter={{
                PageSize: -1,
                RoleId: "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469",
              }}
            />
            <GeneralCarInput
              name="VehicleId"
              label={"Araç"}
              placeholder="Araç"
              className="!m-0"
              span={24}
              allowClear={true}
            />
             <MazakaRangePicker
              name={"InsertDate"}
              label="Sevkiyat Tarih Aralığı"
              xs={24}
              className="!m-0"
            />


            <Col xs={24} >
              <MazakaButton htmlType="submit" processType={formActions.submitProcessType} >
                Filtrele
              </MazakaButton>
            </Col>
           
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
