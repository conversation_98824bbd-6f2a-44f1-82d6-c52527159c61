import { Col, Drawer, Row } from "antd";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { useState } from "react";
import DetailsFilter from "./Components/DetailsFilter";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { handleResetReportFilter } from "apps/Report/ClientSideStates";
import DetailCompanyShipmentTableList from "./Components/DailyCustomerReportTableList";


const DetailCompanyShipmentIndex = () => {
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
    const { dailyCustomerFilter } = useSelector((state: RootState) => state.report);
  return (
    <>
      <MazakaLayout
        title={"Detaylı Firma Sevkiyat Raporu"}
        headDescription={
          "Detaylı firma sevkiyat sayfası, yapılan sevkiyatların görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <MazakaDetailsFilterButton
              setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
            />
             {
              Object.entries(dailyCustomerFilter).length>2&&
              <>
            <MazakaClearFilterButton type="dailyCustomerFilter" actionFunk={handleResetReportFilter} />
              </>
            }
          </Col>
          <Col span={24}>
          <DetailCompanyShipmentTableList/>
          </Col>
        </Row>
      </MazakaLayout>
      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
      <DetailsFilter 
        onFinish={()=>{
          setIsShowFilterDetailsDrawer(false)
        }}
        />
      </Drawer>
    </>
  );
};

export default DetailCompanyShipmentIndex;
