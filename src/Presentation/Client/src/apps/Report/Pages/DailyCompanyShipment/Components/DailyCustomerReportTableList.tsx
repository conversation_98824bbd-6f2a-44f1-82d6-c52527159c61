
import { Col, Table,} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import {hanldleSetReportFilter} from "apps/Report/ClientSideStates";
import {   useGetDetailedCompanyShipmentReport,  } from "apps/Report/ServerSideStates";
import { useEffect } from "react";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";


const DetailCompanyShipmentTableList = () => {
 
  const { dailyCustomerFilter} = useSelector((state: RootState) => state.report);
  const dispatch = useDispatch();
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...dailyCustomerFilter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetReportFilter({ filter: newFilter,type:"dailyCustomerFilter" }));
  };

  const detailCompanyShipments:any = useGetDetailedCompanyShipmentReport( dailyCustomerFilter);
  useEffect(()=>{
    // Every rendered this component needed to send reqeust 
    detailCompanyShipments.refetch()
  },[])

  useEffect(()=>{
      if(detailCompanyShipments.isError)
      {
        showServiceErrorMessage(detailCompanyShipments.error,{},"DailyCustomerReport",true)
      }


  },[detailCompanyShipments.isError])

  const columns = [
   
    {
        title: "Firma",
        dataIndex:"CompanyName",
        key: "CompanyName",
        width:"30%",
        sorter: (a: any, b: any) => a?.CompanyName?.localeCompare(b?.CompanyName)

      },
    {
      title: "Şantiye",
      dataIndex:"BuildingName" ,
      key: "Building",
      sorter: (a: any, b: any) => a?.BuildingName?.localeCompare(b?.BuildingName)

     
    },
    {
      title: "Ürün",
      dataIndex:"Product" ,
      key: "Product",
      sorter: (a: any, b: any) => a?.Product?.localeCompare(b?.Product)

     
    },
    {
        title: "İrsaliye No",
        dataIndex: "DocumentNo",
        key: "DocumentNo",
        sorter: (a: any, b: any) => a?.DocumentNo?.localeCompare(b?.DocumentNo)

      },
      {
        title: "Sevk Tarihi",
        dataIndex: "InsertDate",
        key: "InsertDate",
        render:(value:string)=>{
          if(value)
          {
            return(
              <>
              <div>
                {dayjs(value).format("DD.MM.YYYY")}
              </div>
              <div>
                {dayjs(value).format("HH:mm")}
              </div>
              </>
            )

          }
          return <></>
        },
        sorter: (a: any, b: any) => a?.InsertDate?.localeCompare(b?.InsertDate)
      },
      {
        title: "Şoför",
        dataIndex: "DriverName",
        key: "DriverName",
        sorter: (a: any, b: any) => a?.DriverName?.localeCompare(b?.DriverName)

      },
      {
        title: "Plaka",
        dataIndex: "VehiclePlate",
        key: "VehiclePlate",
        sorter: (a: any, b: any) => a?.VehiclePlate?.localeCompare(b?.VehiclePlate)

      },
      {
        title: "Miktar(m³)",
        dataIndex: "SendingAmount",
        key: "SendingAmount",
        sorter: (a: any, b: any) => Number(a?.SendingAmount ?? 0) - Number(b?.SendingAmount ?? 0)

      },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={detailCompanyShipments.isLoading || detailCompanyShipments.isFetching}
        dataSource={detailCompanyShipments.data ? detailCompanyShipments.data.Value : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
      
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: detailCompanyShipments.data?.FilteredCount || 0,
       
          pageSize: 100,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
     
    </Col>
  );
};

export default DetailCompanyShipmentTableList;
