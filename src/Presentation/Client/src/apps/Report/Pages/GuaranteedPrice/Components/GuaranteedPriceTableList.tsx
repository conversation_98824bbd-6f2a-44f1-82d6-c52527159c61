import { Col, Progress, Table, Tag, Typography } from "antd";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetGuaranteedPriceReports } from "apps/Report/ServerSideStates";
import { useEffect } from "react";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";

const GuaranteedPriceTableList = () => {
  const { guaranteedPriceFilter } = useSelector(
    (state: RootState) => state.report
  );
  const {Text} = Typography

  const guaranteedPrice: any = useGetGuaranteedPriceReports(
    guaranteedPriceFilter
  );
  useEffect(() => {
    // Every rendered this component needed to send reqeust
    guaranteedPrice.refetch();
  }, []);

  useEffect(() => {
    if (guaranteedPrice.isError) {
      showServiceErrorMessage(
        guaranteedPrice.error,
        {},
        "GuaranteedPriceReport",
        true
      );
    }
  }, [guaranteedPrice.isError]);

  const columns = [
  
    {
      title: "Firma",
      dataIndex: "Company",
      key: "CompanyName",
      sorter: (a: any, b: any) => a?.Company?.localeCompare(b?.Company)
    },
    {
      title: "Şantiyeler",
      dataIndex: "Buildings",
      key: "Buildings",
      render:(values:string[])=>{
      if(values)
      {

        return(
          <div className="!flex flex-wrap gap-2">
            {
              values.map((item:string)=>{
                return(
                  <>
                  <Tag color="green" >{item}</Tag>
                  </>
                )
              })
            }
          
          </div>
        )
      }
      return <></>

      },
    },
    {
      title: "Şözleşme",
      dataIndex: "Contract",
      key: "Contract",
      sorter: (a: any, b: any) => a?.Contract?.localeCompare(b?.Contract)
    },
    {
        title: "Ürün",
        dataIndex: "ProductName",
        key: "ProductName",
        sorter: (a: any, b: any) => a?.ProductName?.localeCompare(b?.ProductName)
      },
    {
      title: "Miktar(m³)",
      dataIndex: "ProductAmount",
      key: "ProductAmount",
      sorter: (a: any, b: any) => Number(a?.ProductAmount ?? 0) - Number(b?.ProductAmount ?? 0)
    },
    {
      title: "Tamamlanan",
      key: "TotalConcreteSent",
      sorter: (a: any, b: any) => {
        const totalA = a?.ProductAmount || 0;
        const totalB = b?.ProductAmount || 0;
        const leftA = a?.LeftAmout ?? null;
        const leftB = b?.LeftAmout ?? null;
    
        const percentA =
          totalA > 0 && leftA !== null
            ? (totalA - leftA) / totalA
            : 0;
        const percentB =
          totalB > 0 && leftB !== null
            ? (totalB - leftB) / totalB
            : 0;
    
        return percentA - percentB;
      },
      render: (value: any, record: any) => {
        const totalConcreteRequested = record.ProductAmount||0;
        const totalConcreteSent = totalConcreteRequested-(record?.LeftAmout||0);
        const percent = totalConcreteRequested > 0 &&record?.LeftAmout!==null
        ? Math.round((totalConcreteSent / totalConcreteRequested) * 100 * 100) / 100 
        : 0;
  
    
        return (
          <>
            <Progress
              type="circle"
              percent={Math.min(percent, 100)} 
              size="small"
            />
          </>
        );
      },
    },
    {
        title: "Net Fark(m³)",
        dataIndex: "LeftAmout",
        key: "LeftAmout",
        sorter: (a: any, b: any) => Number(a?.LeftAmout ?? 0) - Number(b?.LeftAmout ?? 0)
      },
     
      {
        title: "Şözleşme Tarih Aralığı",
        dataIndex: "ContractStartDate",
        key: "ContractStartDate",
        sorter: (a: any, b: any) => a?.ContractStartDate?.localeCompare(b?.ContractStartDate),
        render:(value:string,record:any)=>{
            return(
                <>
                    <div>
                        {
                            value&&
                            <>
                                <div>
                                    <Text>{dayjs(value).format("DD.MM.YYYY")}</Text>
                                </div>
                            </>
                        }
                        {
                             record?.ContractEndDate&&
                             <>
                                 <div>
                                     <Text>{dayjs(record?.ContractEndDate).format("DD.MM.YYYY")}</Text>
                                 </div>
                             </>
                        }
                    </div>
                </>
            )
        }
      },

      {
        title: "Fiyat Koruma Bitiş Tarihi",
        dataIndex: "PriceGuaranteeDate",
        key: "TotalConcreteRequested",
        sorter: (a: any, b: any) => a?.PriceGuaranteeDate?.localeCompare(b?.PriceGuaranteeDate),
        render:(value:string,record:any)=>{
            return(
                <>
                    <div>
                        {
                            value&&
                            <>
                                <div>
                                    <Text>{dayjs(value).format("DD.MM.YYYY")}</Text>
                                </div>
                            </>
                        }
                       
                    </div>
                </>
            )
        }
      },
   
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={guaranteedPrice.isLoading || guaranteedPrice.isFetching}
        dataSource={guaranteedPrice.data ? guaranteedPrice.data.Value : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",

          total: guaranteedPrice.data?.FilteredCount || 0,

          pageSize: 100,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </Col>
  );
};

export default GuaranteedPriceTableList;
