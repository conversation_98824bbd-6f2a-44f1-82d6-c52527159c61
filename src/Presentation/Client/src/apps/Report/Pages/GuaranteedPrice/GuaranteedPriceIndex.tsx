
import {  Col, Drawer, Row } from "antd";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { handleResetReportFilter } from "apps/Report/ClientSideStates";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import DetailsFilter from "./Components/DetailsFilter";
import GuaranteedPriceTableList from "./Components/GuaranteedPriceTableList";



const GuaranteedPriceIndex = () => {
 
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] = useState(false);
  const { guaranteedPriceFilter } = useSelector((state: RootState) => state.report);
  return (
    <>
      <MazakaLayout
        title={"Garanti Fiyatı Bitecek Sözleşmeler Raporu"}
        headDescription={"Garanti Fiyatı Bitecek olan Sözleşmeler Rapor sayfası, garanti tarihi bitmiş olan sözleşmeleri görüntülemenizi sağlar"}
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <MazakaDetailsFilterButton setIsShowDetailsFilter={setIsShowFilterDetailsDrawer} />
            {
              Object.entries( guaranteedPriceFilter).length>2&&
              <>
            <MazakaClearFilterButton type="guaranteedPriceFilter" actionFunk={handleResetReportFilter} />
              </>
            }
          </Col>
          <Col span={24}>
          <GuaranteedPriceTableList/>
          </Col>
        </Row>
      </MazakaLayout>
      <Drawer title="Detaylı Filtre" open={isShowFilterDetailsDrawer} onClose={()=>{setIsShowFilterDetailsDrawer(false)}}>
        <DetailsFilter
       onFinish={()=>{
        setIsShowFilterDetailsDrawer(false)
       }}
        />
    </Drawer>

     
    </>
  );
};

export default GuaranteedPriceIndex;
