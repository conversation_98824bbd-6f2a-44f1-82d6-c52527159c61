import { Col, Form, Row } from "antd";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import GeneralCarInput from "apps/Common/GeneralCarInput";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import GeneralProductInput from "apps/Common/GeneralProductInput";
import GeneralStationInput from "apps/Common/GeneralStationInput";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaRangePicker } from "apps/Common/MazakaRangePicker";
import { hanldleSetReportFilter } from "apps/Report/ClientSideStates";
import dayjs from "dayjs";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const {  returnReportFilter} = useSelector((state: RootState) => state.report);
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    let currentFilter = { ...returnReportFilter };
    for (let key in formValues) {
      if (!formValues[key]) {
        delete formValues[key];
      }
      if (!currentFilter?.key) {
        delete currentFilter[key];
      }
    }
    if (formValues?.Date && formValues["Date"][0] && formValues["Date"][1]) {
      formValues["StartDate"] = dayjs(formValues["Date"][0]).format(
        "YYYY-MM-DD"
      );
      formValues["EndDate"] = dayjs(formValues["Date"][1]).format(
        "YYYY-MM-DD"
      );
      delete formValues["Date"]
    } else {
      mazakaForm.setFailed(
        2000,
        "Tarih aralığnda Başlangıç ve bitiş tarihi zorunlu "
      );
    }
    
    const newFilter = { ...currentFilter, ...formValues };
    await dispatch(
      hanldleSetReportFilter({ filter: newFilter, type: "returnReportFilter" })
    );
    mazakaForm.setSuccess(1000, () => {}, "Başarılı");
    onFinish();
  };



  useEffect(() => {
    const startDate = returnReportFilter["StartDate"]
    ? dayjs(returnReportFilter["StartDate"])
    : null;
  const endDate = returnReportFilter["EndDate"]
    ? dayjs(returnReportFilter["EndDate"])
    : null;
  const dateRange = startDate && endDate ? [startDate, endDate] : [];
    form.setFieldsValue({
      VehicleId: returnReportFilter?.VehicleId|| undefined,
      BuildingId: returnReportFilter?.BuildingId || undefined,
      StationId: returnReportFilter?.StationId || undefined,
      Date:dateRange.length>0?dateRange:undefined,
      CompanyId:returnReportFilter?.CompanyId || undefined,
      ProductId:returnReportFilter?.ProductId || undefined
    });
  }, [returnReportFilter,form]);

 
  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} submitButtonVisible={false}>
          <Row gutter={[0, 10]}>
          <GeneralCompanies
              name="CompanyId"
              label={"Firma"}
              placeholder="Firma"
              className="!m-0"
              xs={24}
              allowClear={true}
            />

            <GeneralStationInput
              name="StationId"
              label={"İstasyon"}
              placeholder="İstasyon"
              className="!m-0"
              xs={24}
              allowClear={true}   
            />

            <GeneralCarInput
              name="VehicleId"
              label={"Araç"}
              placeholder="Araç"
              className="!m-0"
              span={24}
              allowClear={true}
            />
            <GeneralProductInput
              name="ProductId"
              label={"Ürün"}
              placeholder={"Ürün"}
              className="!m-0"
              xs={24}
              allowClear={true}
            />
            <MazakaRangePicker
              name={"Date"}
              label="Tarih Aralığı"
              xs={24}
              className="!m-0"
              
            />
              <Col xs={24} >
              <MazakaButton htmlType="submit" processType={formActions.submitProcessType} >
                Filtrele
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
