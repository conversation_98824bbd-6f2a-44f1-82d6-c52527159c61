import { Col, Drawer, <PERSON> } from "antd";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { useState } from "react";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { handleResetReportFilter } from "apps/Report/ClientSideStates";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import DetailsFilter from "./Components/DetailsFilter";
import ReturnReportTableList from "./Components/ReturnReportTableList";

const DelayRequestReportIndex = () => {
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
  const { returnReportFilter } = useSelector(
    (state: RootState) => state.report
  );
  
  return (
    <>
      <MazakaLayout
        title={"İade Raporu"}
        headDescription={
          "İade rapor sayfası, iade edilmiş sevkiyatların listesini görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <MazakaDetailsFilterButton
              setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
            />
            {Object.entries(returnReportFilter).length > 2 && (
              <>
                <MazakaClearFilterButton
                  type="returnReportFilter"
                  actionFunk={handleResetReportFilter}
                />
              </>
            )}
          </Col>
          <Col span={24}><ReturnReportTableList/></Col>
        </Row>
      </MazakaLayout>
      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
        <DetailsFilter
          onFinish={() => {
            setIsShowFilterDetailsDrawer(false);
          }}
        />
      </Drawer>
    </>
  );
};

export default DelayRequestReportIndex;
