
import { Col, Table } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

import {  hanldleSetReportFilter } from "apps/Report/ClientSideStates";
import {  useGetReturnReports } from "apps/Report/ServerSideStates";
import { useEffect } from "react";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";


const ReturnReportTableList = () => {
 
  const { returnReportFilter} = useSelector((state: RootState) => state.report);
  const dispatch = useDispatch();

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...returnReportFilter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetReportFilter({ filter: newFilter,type:"returnReportFilter" }));
  };

  const returnReports:any = useGetReturnReports(returnReportFilter);
  useEffect(() => {
    // Every rendered this component needed to send reqeust
     returnReports.refetch();
  }, []);

  useEffect(() => {
    if ( returnReports.isError) {
      showServiceErrorMessage(
         returnReports.error,
        {},
        "ReturnReport",
        true
      );
    }
  }, [ returnReports.isError]);

  const columns = [
    {
      title: "Firma",
      dataIndex:"Company",
      key: "Company",
      sorter: (a: any, b: any) => a?.Company?.localeCompare(b?.Company)
    },
    {
      title: "İstasyon",
      dataIndex:"Station",
      key: "Station",
      sorter: (a: any, b: any) => a?.Station?.localeCompare(b?.Station)
    },
    {
      title: "Araç",
      dataIndex:"Vehicle",
      key: "Vehicle",
      sorter: (a: any, b: any) => a?.Vehicle?.localeCompare(b?.Vehicle)
    },
    {
      title: "Ürün",
      dataIndex:"Product",
      key: "Product",
      sorter: (a: any, b: any) => a?.Product?.localeCompare(b?.Product)
    },
    {
      title: "İstenilen Toplam Beton(m³)",
      dataIndex: "SendingAmount",
      key: "SendingAmount",
      sorter: (a: any, b: any) => Number(a?.SendingAmount ?? 0) - Number(b?.SendingAmount ?? 0)
    },
    {
      title: "İade Miktarı(m³)",
      dataIndex: "ReturnAmount",
      key: "ReturnAmount",
      sorter: (a: any, b: any) => Number(a?.ReturnAmount ?? 0) - Number(b?.ReturnAmount ?? 0)
    },
    {
      title: "Tarih",
      dataIndex: "ReturnDate",
      key: "ReturnDate",
      sorter: (a: any, b: any) => a?.ReturnDate?.localeCompare(b?.ReturnDate),
      render: (text: any) => {
        if(text)
        {
          return new Date(text).toLocaleDateString("tr-TR")
        }
        return <></>
      },
    },


   
    
    
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={returnReports.isLoading || returnReports.isFetching}
        dataSource={returnReports.data ? returnReports.data.Value : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
      
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: returnReports.data?.FilteredCount || 0,
          current: returnReports.data?.PageIndex,
          pageSize: returnReports.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
     
    </Col>
  );
};

export default ReturnReportTableList;
