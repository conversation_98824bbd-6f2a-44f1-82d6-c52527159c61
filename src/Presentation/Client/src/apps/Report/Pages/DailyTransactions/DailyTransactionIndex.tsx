import { Col, Drawer, Row } from "antd";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { useState } from "react";
import DetailsFilter from "./Components/DetailsFilter";
import DailyTransactionTableList from "./Components/DailyTransactionTableList";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { handleResetReportFilter } from "apps/Report/ClientSideStates";

const DailyTransactionIndex = () => {
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
    const {  dailyManagerFilter } = useSelector((state: RootState) => state.report);
  return (
    <>
      <MazakaLayout
        title={"Firma Çıkış Raporu"}
        headDescription={
          "Firma çıkış rapor sayfası, sistemde kayıtlı tüm bügüne ait yapılan sevkiyat  listesini görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <MazakaDetailsFilterButton
              setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
            />
             {
              Object.entries(dailyManagerFilter).length>2&&
              <>
            <MazakaClearFilterButton type="dailyManagerFilter" actionFunk={handleResetReportFilter} />
              </>
            }
          </Col>
          <Col span={24}>
            <DailyTransactionTableList />
          </Col>
        </Row>
      </MazakaLayout>
      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
       <DetailsFilter 
        onFinish={()=>{
          setIsShowFilterDetailsDrawer(false)
        }}
        />
      </Drawer>
    </>
  );
};

export default DailyTransactionIndex;
