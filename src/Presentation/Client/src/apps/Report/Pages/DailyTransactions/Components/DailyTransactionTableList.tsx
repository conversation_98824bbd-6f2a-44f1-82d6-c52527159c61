
import { Col, Table,} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import {  hanldleSetReportFilter} from "apps/Report/ClientSideStates";
import {  useGetDailyManagerReports, } from "apps/Report/ServerSideStates";
import { useEffect } from "react";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";

const DailyTransactionTableList = () => {
 
  const {dailyManagerFilter} = useSelector((state: RootState) => state.report);
  const dispatch = useDispatch();
  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...dailyManagerFilter, PageIndex: pageNum, PageSize: pageSize };
  dispatch(hanldleSetReportFilter({ filter: newFilter,type:"dailyManagerFilter " }));
  };

  const dailyTranasactions:any = useGetDailyManagerReports(dailyManagerFilter);
  useEffect(()=>{
    // Every rendered this component needed to send reqeust 
    dailyTranasactions.refetch()
  },[])

  useEffect(()=>{
      if(dailyTranasactions.isError)
      {
        showServiceErrorMessage(dailyTranasactions.error,{},"DailyTransactionReport",true)
      }


  },[dailyTranasactions.isError])

  const columns = [
    {
      title: "Santral",
      dataIndex:"Station",
      key: "StationName",
      sorter: (a: any, b: any) => a?.Station?.localeCompare(b?.Station)
    },
    {
        title: "Firma",
        dataIndex:"Company",
        key: "CompanyName",
        sorter: (a: any, b: any) => a?.Company?.localeCompare(b?.Company)
      },
    {
      title: "Şantiye",
      dataIndex:"Building" ,
      key: "Building",
      sorter: (a: any, b: any) => a?.Building?.localeCompare(b?.Building)
     
    },
    {
        title: "Talep Edilen(m³)",
        dataIndex: "TotalConcreteRequested",
        key: "TotalConcreteRequested",
        sorter: (a: any, b: any) => Number(a?.TotalConcreteRequested ?? 0) - Number(b?.TotalConcreteRequested ?? 0)
        
      },
      {
        title: "Tamamlanan(m³)",
        dataIndex: "TotalConcreteSent",
        key: "TotalConcreteSent",
        sorter: (a: any, b: any) => Number(a?.TotalConcreteSent ?? 0) - Number(b?.TotalConcreteSent ?? 0)
      },
      {
        title: "Net Fark(m³)",
        dataIndex: "TotalConcreteRemaining",
        key: "TotalConcreteRemaining",
        render: (value: number) => {
          const color = value > 0 ? "green" : value < 0 ? "red" : "black";
          return <span style={{ color }}>{value.toFixed(2)}</span>;
        },
        sorter: (a: any, b: any) => Number(a?.TotalConcreteRemaining ?? 0) - Number(b?.TotalConcreteRemaining ?? 0)
      },
  
    
    
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={dailyTranasactions.isLoading || dailyTranasactions.isFetching}
        dataSource={dailyTranasactions.data ? dailyTranasactions.data.Value : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
      
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: dailyTranasactions.data?.FilteredCount || 0,
          current: dailyTranasactions.data?.PageIndex,
          pageSize: dailyTranasactions.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
     
    </Col>
  );
};

export default DailyTransactionTableList;
