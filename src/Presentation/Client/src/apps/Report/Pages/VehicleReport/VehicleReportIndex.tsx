
import {  Col, Drawer, Row } from "antd";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { useState } from "react";
import DetailsFilter from "./Components/DetailsFilter";
import VehicleReportTableList from "./Components/VehicleReportTableList";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { handleResetReportFilter } from "apps/Report/ClientSideStates";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";



const VehicleReportIndex = () => {
 
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] = useState(false);
  const { vehicleFilter } = useSelector((state: RootState) => state.report);
  return (
    <>
      <MazakaLayout
        title={"Araçların Günlük Dağıtım Raporu "}
        headDescription={"Araçların günlük dağıtım rapor  sayfası, Araçların sevkiyat durumlarını görüntülemenizi sağlar"}
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <MazakaDetailsFilterButton setIsShowDetailsFilter={setIsShowFilterDetailsDrawer} />
            {
              Object.entries(vehicleFilter).length>2&&
              <>
            <MazakaClearFilterButton type="vehicleFilter" actionFunk={handleResetReportFilter} />
              </>
            }
          </Col>
          <Col span={24}>
         <VehicleReportTableList/>
          </Col>
        </Row>
      </MazakaLayout>
      <Drawer title="Detaylı Filtre" open={isShowFilterDetailsDrawer} onClose={()=>{setIsShowFilterDetailsDrawer(false)}}>
        <DetailsFilter 
       onFinish={()=>{
        setIsShowFilterDetailsDrawer(false)
       }}
        />
    </Drawer>

     
    </>
  );
};

export default VehicleReportIndex;
