import { Col, Table, Typography } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

import { hanldleSetReportFilter } from "apps/Report/ClientSideStates";
import { useGetVehicleReport } from "apps/Report/ServerSideStates";
import { useEffect } from "react";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import dayjs from "dayjs";

const VehicleReportTableList = () => {
  const { vehicleFilter } = useSelector((state: RootState) => state.report);
  const dispatch = useDispatch();

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = {
      ...vehicleFilter,
      PageIndex: pageNum,
      PageSize: pageSize,
    };
    dispatch(
      hanldleSetReportFilter({ filter: newFilter, type: "vehicleFilter" })
    );
  };

  const vehicleRequests: any = useGetVehicleReport(vehicleFilter);
  useEffect(() => {
    // Every rendered this component needed to send reqeust
    vehicleRequests.refetch();
  }, []);
  const {Text} = Typography

  useEffect(() => {
    if (vehicleRequests.isError) {
      showServiceErrorMessage(
        vehicleRequests.error,
        {},
        "DailyVehicletReport",
        true
      );
    }
  }, [vehicleRequests.isError]);

  const columns = [
    {
      title: "Araç",
      dataIndex: "Vehicle",
      key: "vehicle",
      sorter: (a: any, b: any) => a?.Vehicle?.localeCompare(b?.Vehicle)
    },
    {
      title: "Firma",
      dataIndex: "Company",
      key: "Company",
      sorter: (a: any, b: any) => a?.Company?.localeCompare(b?.Company)
    },
    {
      title: "Şantiye",
      dataIndex: "Building",
      key: "Building",
      sorter: (a: any, b: any) => a?.Building?.localeCompare(b?.Building)
    },

    {
      title: "Sefer Sayısı",
      dataIndex: "TotalTransaction",
      key: "TotalTransaction",
      sorter: (a: any, b: any) => Number(a?.TotalTransaction ?? 0) - Number(b?.TotalTransaction ?? 0)
    },
    {
      title: "Toplam Gönderilen(m³)",
      dataIndex: "TotalSendingAmount",
      key: "TotalSendingAmount",
      sorter: (a: any, b: any) => Number(a?.TotalSendingAmount ?? 0) - Number(b?.TotalSendingAmount ?? 0)
    },

    {
      title: "Onaylama Tarihi",
      dataIndex: "ApprovedDate",
      key: "ApprovedDate",
      sorter: (a: any, b: any) => a?.ApprovedDate?.localeCompare(b?.ApprovedDate),
      render: (value: any) => {
        if (value) {
          return(
            <>
          <div>

          <div>
            <Text>{new Date(value).toLocaleDateString("tr-TR")}</Text>
          </div>
          <div>
          <Text>{dayjs(value).format("HH:mm")}</Text>
          </div>
          </div>
            </>
          )
  
        }
        return <></>;
      },
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={vehicleRequests.isLoading || vehicleRequests.isFetching}
        dataSource={vehicleRequests.data ? vehicleRequests.data.Value : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: vehicleRequests.data?.FilteredCount || 0,
          current: vehicleRequests.data?.PageIndex,
          pageSize: vehicleRequests.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </Col>
  );
};

export default VehicleReportTableList;
