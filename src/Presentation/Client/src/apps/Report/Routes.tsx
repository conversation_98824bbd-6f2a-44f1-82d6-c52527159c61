import { Spin } from "antd";
import React, { lazy, Suspense } from "react";
import { Route } from "react-router-dom";

// Lazy load each component
const DelayReportIndex = lazy(() => import("./Pages/DelayReport/DelayReportIndex"));
const VehicleReportIndex = lazy(() => import("./Pages/VehicleReport/VehicleReportIndex"));
const DailyRequestReportIndex = lazy(() => import("./Pages/DailyTransactions/DailyTransactionIndex"));
const DetailCompanyShipmentIndex = lazy(() => import("./Pages/DailyCompanyShipment/DetailCompanyShipmentIndex"));
const ReturnReportIndex = lazy(() => import("./Pages/ReturnReport/ReturnReportIndex"));
const GuaranteedPriceIndex = lazy(() => import("./Pages/GuaranteedPrice/GuaranteedPriceIndex"));

// Replace <Spint /> with your actual spinner component if it's named differently
const Spint = () => <div>Loading...</div>; // Temporary fallback component

export const reportRouteList = [
  <Route key="reportRouteList">
    <Route
      path="/report/delay/list"
      element={
        <Suspense fallback={<Spint />}>
          <DelayReportIndex />
        </Suspense>
      }
    />
    <Route
      path="/report/vehicle/list"
      element={
        <Suspense fallback={<Spin />}>
          <VehicleReportIndex />
        </Suspense>
      }
    />
    <Route
      path="/report/Daily/transaction"
      element={
        <Suspense fallback={<Spin />}>
          <DailyRequestReportIndex />
        </Suspense>
      }
    />
    <Route
      path="/report/daily/customer"
      element={
        <Suspense fallback={<Spin />}>
          <DetailCompanyShipmentIndex/>
        </Suspense>
      }
    />
   
    <Route
      path="/report/return/list"
      element={
        <Suspense fallback={<Spin />}>
          <ReturnReportIndex />
        </Suspense>
      }
    />
    <Route
      path="/report/guaranteed/list"
      element={
        <Suspense fallback={<Spin />}>
          <GuaranteedPriceIndex />
        </Suspense>
      }
    />
  </Route>,
];
