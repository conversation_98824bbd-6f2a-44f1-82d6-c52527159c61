import { get } from "services/BaseClient/Client";
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Report/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";

export const getDailyManagerReport = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getDailyManagerReportListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getReturnReport = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getReturnReportListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const getAllRequestReport = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getAllTransactionRequestReportListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const getDailyCustomerReport = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getDailyCustomerReporttListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getDetailedCompanyShipment = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getDetailedCompanyShipment}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};


export const getDelayRequestReport = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getDelayRequestReportListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getVehicleReport = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getDailyVehicleReportListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const getGuaranteedPrice = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getGuaranteedPriceListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getStationOccupancyRate = async (filter:any): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter)
  const url = `${endpoints.getConcreteOptionList}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};