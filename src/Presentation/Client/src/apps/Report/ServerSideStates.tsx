import { useQuery } from "react-query";
import endpoints from "apps/Report/EndPoints";
import { getAllRequestReport, getDailyCustomerReport, getDailyManagerReport, getDelayRequestReport, getDetailedCompanyShipment, getGuaranteedPrice, getReturnReport, getVehicleReport } from "./Services";




export const useGetDailyManagerReports = (filter?: any) => {
  const query = useQuery(
    [endpoints.getDailyManagerReportListFilter, filter],
    () => {
      return getDailyManagerReport(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};


export const useGetAllRequestReports = (filter?: any) => {
  const query = useQuery(
    [endpoints.getAllTransactionRequestReportListFilter, filter],
    () => {
      return getAllRequestReport(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetDelayRequestReport = (filter?: any) => {
  const query = useQuery(
    [endpoints.getDelayRequestReportListFilter, filter],
    () => {
      return getDelayRequestReport(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetCustomerReport = (filter?: any) => {
  const query = useQuery(
    [endpoints.getDailyCustomerReporttListFilter, filter],
    () => {
      return getDailyCustomerReport(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetDetailedCompanyShipmentReport = (filter?: any) => {
  const query = useQuery(
    [endpoints.getDetailedCompanyShipment, filter],
    () => {
      return getDetailedCompanyShipment(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};


export const useGetReturnReports= (filter?: any) => {
  const query = useQuery(
    [endpoints.getReturnReportListFilter, filter],
    () => {
      return getReturnReport(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetVehicleReport = (filter?: any) => {
  const query = useQuery(
    [endpoints.getDailyVehicleReportListFilter, filter],
    () => {
      return getVehicleReport(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetGuaranteedPriceReports = (filter?: any) => {
  const query = useQuery(
    [endpoints.getGuaranteedPriceListFilter, filter],
    () => {
      return getGuaranteedPrice(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

