import { Route } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";
const DepartmentIndex= lazy(() => import('./DepartmentIndex'))

export const departmentRouteList = [
  <Route key={"departmentRouteList"}>
    <Route
      path={"/department/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <DepartmentIndex/>
        </Suspense>
      }
    />
  </Route>,
];
