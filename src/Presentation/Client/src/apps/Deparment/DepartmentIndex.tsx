import { ArrowLeftOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";
import DepartmentListTable from "./Components/DepartmentListTable";
import { useQueryClient } from "react-query";
import endpoints from "apps/Deparment/EndPoints";
import { useNavigate } from "react-router-dom";
const AddOrUpdateDepartment = lazy(
  () => import("./Components/AddOrUpdate/AddOrUpdateDepartment")
);

const DepartmentIndex = () => {
  const { Text } = Typography;
  const queryClient = useQueryClient();

  const [isShowAddDepartmentDrawer, setIsShowAddDepartmentDrawer] =
    useState(false);
    const navigate = useNavigate()
  return (
    <>
      <MazakaLayout
        title={"Departman Listesi"}
        headDescription={
          "Departman sayfası, sistemde tüm kayıtlı  departmanların görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <Button
              onClick={() => {
                setIsShowAddDepartmentDrawer(true);
              }}
              className="!flex items-center"
              type="primary"
            >
              <div className="!flex items-center gap-1">
                <PlusOutlined />
                <Text className="!text-white">Departman Ekle</Text>
              </div>
            </Button>
            <Button
              onClick={() => {
                navigate(-1)
              }}
              className="!flex items-center"
              type="text"
            >
              <div className="!flex items-center gap-1">
                < ArrowLeftOutlined/>
                <Text className="">Geriye Dön</Text>
              </div>
            </Button>
          </Col>
          <Col span={24}>
            <DepartmentListTable />
          </Col>
        </Row>
      </MazakaLayout>

      <Drawer
        title="Yeni Departman Ekle"
        open={isShowAddDepartmentDrawer}
        onClose={() => {
          setIsShowAddDepartmentDrawer(false);
        }}
      >
        <>
          {isShowAddDepartmentDrawer && (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateDepartment
                onFinish={() => {
                  setIsShowAddDepartmentDrawer(false);
                  queryClient.resetQueries({
                    queryKey: endpoints.getDepartmentListFilter,
                    exact: false,
                  });
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </>
  );
};

export default DepartmentIndex;
