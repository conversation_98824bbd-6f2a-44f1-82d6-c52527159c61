import { Col, Form, Row, } from "antd";
import GeneralPhoneNumber from "apps/Common/GeneralPhoneNumber";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { addDepartment, updateDepartmentWithPut } from "apps/Deparment/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";
import { FC, useEffect } from "react";

const AddOrUpdateDepartment: FC<GeneralAddOrUpdateFormProps> = ({
  selectedRecord,
  onFinish,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  useEffect(() => {
    if (selectedRecord) {
      let data = { ...selectedRecord };
      if (data?.PhoneNumber) {
        data["PhoneNumber"] = data["PhoneNumber"].replace(
          /^0090(\d{3})(\d{3})(\d{4})$/,
          "+90 ($1) $2 $3"
        );
      }
      form.setFieldsValue({ ...data });
    }
  }, [selectedRecord]);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();


    formValues["PhoneNumber"] = formValues["PhoneNumber"].replace(/\D/g, '')
    if(formValues["PhoneNumber"])
      {
  
        formValues["PhoneNumber"]=formValues["PhoneNumber"][0]==="0"?formValues["PhoneNumber"].slice(1):formValues["PhoneNumber"]
      }
    try {
      if (selectedRecord) {
        formValues["Id"] = selectedRecord["Id"]
        await updateDepartmentWithPut({  ...formValues });
      } else {
        await addDepartment(formValues);
      }
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm,"department");
    }
  };
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          initialValues={{ Active: true }}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
            <MazakaInput
              xs={24}
              label="Adı"
              className="!m-0"
              name={"Name"}
              placeholder="Adı"
              rules={[{ required: true, message: "" }]}
            />
          
            <GeneralPhoneNumber
              name="PhoneNumber"
              label={"Telefon"}
              xs={24}
              className="!m-0"
              rules={[{ required: true, message: "" }]}
            />

           
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateDepartment;
