import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Col, Drawer, Dropdown, Modal, Spin, Table } from "antd";
import { ItemType } from "antd/es/menu/interface";
import { openNotificationWithIcon } from "helpers/Notifications";
import { lazy, Suspense, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import endpoints from "apps/Deparment/EndPoints";
import { useQueryClient } from "react-query";
import { SecretText } from "apps/Common/SecretString";
import { deleteDepartment } from "../Services";
import { useGetDepartments } from "../ServerSideStates";
import { hanldleSetDepartmentFilter } from "../ClientSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
const AddOrUpdateDepartment = lazy(
  () => import("./AddOrUpdate/AddOrUpdateDepartment")
);

const DepartmentListTable = () => {
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const { filter } = useSelector((state: RootState) => state.department);
  const dispatch = useDispatch();
  const queryClient = useQueryClient();
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await setSelectedRecord(record);
        setIsEditDrawer(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteDepartment(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getDepartmentListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error,{},"Department",true)
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetDepartmentFilter({ filter: newFilter }));
  };

  const companies = useGetDepartments(filter);

  const columns = [
    {
      title: "Adı",
      dataIndex: "Name",
      key: "Name",
    },

    {
      title: "Telefon",
      dataIndex: "PhoneNumber",
      key: "PhoneNumber",
      render: (value: string) => {
        return (
          <>
            <div className="!text-primary">
              <SecretText text={value} textType="phone" />
            </div>
          </>
        );
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={companies.isLoading || companies.isFetching}
        dataSource={companies.data ? companies.data.Data : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: companies.data?.FilteredCount || 0,
          current: companies.data?.PageIndex,
          pageSize: companies.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <Drawer
        title="Departmanı Güncelle"
        open={isShowEditDrawer}
        onClose={() => {
          setIsEditDrawer(false);
        }}
      >
        <>
          {isShowEditDrawer&& (
            <Suspense fallback={<Spin />}>
              <AddOrUpdateDepartment
                selectedRecord={selectedRecord}
                onFinish={() => {
                  setIsEditDrawer(false);
                  queryClient.resetQueries({
                    queryKey: endpoints.getDepartmentListFilter,
                    exact: false,
                  });
                }}
              />
             
            </Suspense>
          )}
        </>
      </Drawer>
    </Col>
  );
};

export default DepartmentListTable;
