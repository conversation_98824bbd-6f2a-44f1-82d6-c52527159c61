import { createSlice } from "@reduxjs/toolkit";
const InitialState: any = {
  filter: {
    PageIndex: 1,
    PageSize: 20,
  },
};

const departmentSlice = createSlice({
  name: "DepartmentSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetDepartmentFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    handleResetAllFieldsDepartment: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterDepartment: (state) => {
       state.filter = {
        PageIndex: 1,
        PageSize: 20,
      }
      },
  },
});

export const { handleResetAllFieldsDepartment,handleResetFilterDepartment,hanldleSetDepartmentFilter } = departmentSlice.actions;
export default departmentSlice;
