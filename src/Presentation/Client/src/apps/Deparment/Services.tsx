import {deleteRequest, get, patch, post, put} from "services/BaseClient/Client"
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Deparment/EndPoints"
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";
import { AddDepartmentFormModel, DepartmentAndDetails } from "./Models";



export const getDepartmentListFilter = async (filter: any): Promise<DataResponse<DepartmentAndDetails[]>> => {
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getDepartmentListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<any>>(url, config);
  };


  export const addDepartment = async (data: any): Promise<DataResponse<AddDepartmentFormModel>> => {
    const url = `${endpoints.addDepartment}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<DepartmentAndDetails>>(url, data, config);
  };
  
  export const updateDepartmentWithPut = async (data: any): Promise<DataResponse<DepartmentAndDetails>> => {
    const url = `${endpoints.updateDepartmentWithPut}`;
    const config = headers.content_type.application_json;
    return put<DataResponse<DepartmentAndDetails>>(url, data, config);
  };
  export const updateDepartmentWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateDepartmentWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
  };
  export const deleteDepartment = async (data: DepartmentAndDetails): Promise<DataResponse<DepartmentAndDetails>> => {
    const url = `${endpoints.deleteDepartment}/${data.Id}`;
    const config = headers.content_type.application_json;
    return deleteRequest<DataResponse<DepartmentAndDetails>>(url, data, config);
  };

