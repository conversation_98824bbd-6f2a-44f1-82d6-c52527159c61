import { useQuery } from "react-query";
import endpoints from "apps/Deparment/EndPoints";
import { getDepartmentListFilter } from "./Services";


export const useGetDepartments = (filter: any) => {
  const query = useQuery(
    [endpoints.getDepartmentListFilter, filter],
    () => {
      return getDepartmentListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

