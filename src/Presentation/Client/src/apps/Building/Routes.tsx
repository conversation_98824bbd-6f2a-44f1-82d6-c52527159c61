import { Route } from "react-router-dom";
import { Spin } from "antd";
import React, { lazy, Suspense } from 'react'
const BuildingIndex= lazy(() => import('apps/Building/BuildingIndex'))




export const buildingRouteList = [
  <Route key={"buildingRouteList"}>
   
    <Route
      path={"/building/list"}
      element={
       <Suspense fallback={<Spin/>}>

         <BuildingIndex/>
       </Suspense>
     
      }
    />
  
  </Route>,
];
