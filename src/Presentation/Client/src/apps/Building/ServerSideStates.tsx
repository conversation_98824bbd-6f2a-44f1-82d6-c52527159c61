import { useQuery } from "react-query";
import endpoints from "apps/Building/Endpoints";
import { getBuildingListFilter } from "apps/Building/Services";

export const useGetBuildings = (filter: any) => {
  const query = useQuery(
    [endpoints.getBuildingListFilter, filter],
    () => {
      return getBuildingListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};