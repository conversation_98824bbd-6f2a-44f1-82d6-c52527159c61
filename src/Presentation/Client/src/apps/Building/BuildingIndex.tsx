import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { useState } from "react";
import BuildingTableList from "./components/BuildingTableList";
import { useQueryClient } from "react-query";
import endpoints from "apps/Building/Endpoints";
import React, { lazy, Suspense } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { handleResetFilterBuilding } from "./ClientSideStates";
import DetailsFilter from "./components/DetailsFilter";
const AddOrUpdateBuildingForm = lazy(
  () => import("./components/AddOrUpdateBuilding/AddOrUpdateBuildingForm")
);

const BuildingIndex = () => {
  const { Text } = Typography;
  const [isShowAddStationDrawer, setIsShowAddStationDrawer] = useState(false);
  const { filter } = useSelector((state: RootState) => state.building);
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] = useState(false);
  const queryClient = useQueryClient();
  return (
    <>
      <MazakaLayout
        title={"Şantiye Listesi"}
        headDescription={
          "Şantiye sayfası, sistemde tüm kayıtlı şantiyelerin listesini görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]}>
          <Col span={24} className="!flex justify-end gap-2">
            <Button
              onClick={() => {
                setIsShowAddStationDrawer(true);
              }}
              className="!flex items-center"
              type="primary"
            >
              <div className="!flex items-center gap-1">
                <PlusOutlined />
                <Text className="!text-white">Şantiye Ekle</Text>
              </div>
            </Button>
            <MazakaDetailsFilterButton
                  setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
                />
                {Object.entries(filter).length > 5 && (
                  <>
                    <MazakaClearFilterButton
                      actionFunk={ handleResetFilterBuilding}
                     
                    />
                  </>
                )}
          </Col>
          <Col span={24}>
            <BuildingTableList />
          </Col>
        </Row>
      </MazakaLayout>

      <Drawer
        title="Yeni Şantiye Ekle"
        open={isShowAddStationDrawer}
        onClose={() => {
          setIsShowAddStationDrawer(false);
        }}
      >
        {isShowAddStationDrawer && (
          <>
            <Suspense fallback={<Spin />}>
              <AddOrUpdateBuildingForm
                onFinish={() => {
                  setIsShowAddStationDrawer(false);
                  queryClient.resetQueries({
                    queryKey: endpoints.getBuildingListFilter,
                    exact: false,
                  });
                }}
              />
            </Suspense>
          </>
        )}
      </Drawer>
        <Drawer
          title="Detaylı Filtre"
          open={isShowFilterDetailsDrawer}
          onClose={() => {
            setIsShowFilterDetailsDrawer(false);
          }}
        >
          <>
            {isShowFilterDetailsDrawer && (
              <Suspense fallback={<Spin />}>
                <DetailsFilter
                  onFinish={() => {
                    setIsShowFilterDetailsDrawer(false);
                  }}
                />
              </Suspense>
            )}
          </>
        </Drawer>
    </>
  );
};

export default BuildingIndex;
