import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  filter: {
    PageIndex: 1,
    PageSize: 20,
    IncludeProperties: ["Company.CompanyUser.User","BuildingUser.User"],
    SortProperty:"Active",
    SortType:"ASC"
  },
};

const buildingSlice = createSlice({
  name: "BuildingSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetBuildingFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    handleResetAllFieldsBuilding: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterBuilding: (state) => {
       state.filter = {
        PageIndex: 1,
        PageSize: 20,
        IncludeProperties: ["Company.CompanyUser.User"],
        SortProperty:"Active",
        SortType:"ASC"
      }
      },
  },
});

export const { handleResetAllFieldsBuilding,handleResetFilterBuilding,hanldleSetBuildingFilter } = buildingSlice.actions;
export default buildingSlice;
