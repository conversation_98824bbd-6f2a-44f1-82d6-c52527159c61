import { UserListAndDetails } from "apps/Account/Models";
import { CompanyListAndDetails } from "apps/Company/Models";

interface LocationFields{
    Id:string;
    Name:string;
}
interface BaseSharedBuilding {
    
    Active: boolean;
    Name: string;
    CompanyId: string;
    Company: CompanyListAndDetails;
    Address: string;
    StateProvinceId: string;
    DistrictId: string;
    CityId: string;
    AuditCompany: string;
    AuditPerson: UserListAndDetails;
    AuditPersonId: string;
    AuditPersonPhone: string;
  }
  export interface AddBuildingFormModel extends BaseSharedBuilding{

  }

  export interface BuildingListAndDetails extends BaseSharedBuilding{
 Id: string;
 StateProvince:LocationFields;
 Disctrict:LocationFields
 City:LocationFields
  }