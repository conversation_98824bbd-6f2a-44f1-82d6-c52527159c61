import { Col, Form, Row } from "antd";
import GeneralPhoneNumber from "apps/Common/GeneralPhoneNumber";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetBuildingFilter } from "../ClientSideStates";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { filter } = useSelector((state: RootState) => state.building);
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
   
    let currentFilter = { ...filter };
    for (let key in formValues) {
      if (!formValues[key]) {
        delete formValues[key];
      }
      if (!currentFilter?.key) {
        delete currentFilter[key];
      }
    }
    const newFilter = { ...currentFilter, ...formValues };

    await dispatch(
      hanldleSetBuildingFilter({ filter: newFilter, })
    );
    mazakaForm.setSuccess(1000, () => {}, "İşlem Başarılı");
    onFinish();
  };

  useEffect(() => {
    let data = { ...filter };
      
    form.setFieldsValue({
    CompanyId:data?.CompanyId || undefined,
    Ids:data?.Ids || undefined
    });
  }, [filter]);
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
       

          <Row gutter={[0, 20]}>
          <GeneralCompanies
              name="CompanyId"
              label={"Firma"}
              placeholder="Firma"
              className="!m-0"
              xs={24}
              allowClear={true}
            />
          <GeneralBuildingInput
            name="Ids"
            label={"Şantiye"}
            placeholder="Şantiye"
            className="!m-0"
            allowClear={true}
            mode="multiple"
          />
          
            <Col xs={24}>
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                Filtrele
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
