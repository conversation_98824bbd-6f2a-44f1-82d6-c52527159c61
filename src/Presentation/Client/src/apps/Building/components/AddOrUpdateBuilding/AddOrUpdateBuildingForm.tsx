import {
  <PERSON><PERSON>,
  Col,
  Form,
  Modal,
  Row,
  Typography,

} from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect, useState } from "react";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { PlusOutlined } from "@ant-design/icons";
import CustomGoogleMap from "apps/Common/GoogleMap";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import { MazakaSwitch } from "apps/Common/MazakaSwitch";
import { addBuilding, updateBuildingWithPut } from "apps/Building/Services";
import GeneralUserInput from "apps/Common/GeneralUserInput";
import GeneralStationInput from "apps/Common/GeneralStationInput";

import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import LocationSearchInput from "apps/Common/GeneralPlaceSearchInput";

interface AddOrUpdateBuildingFormProps {
  selectedRecord?: any;
  onFinish?: any;
  externalCompanyId?: string;
}

const AddOrUpdateBuildingForm: FC<AddOrUpdateBuildingFormProps> = ({
  selectedRecord,
  onFinish,
  externalCompanyId,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const [isShowMap, setIsShowMap] = useState(false);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    if (!marker || !marker?.lat || !marker.lng) {
      mazakaForm.setFailed(2000, "Konum Secimi zorunlu");
      return false;
    }
    let formValues = form.getFieldsValue();
    formValues["Latitude"] = Number(marker.lat);
    formValues["Longitude"] = Number(marker.lng);
    // formValues["StateProvinceId"] = "fb01b76c-1335-4cbf-9631-08dcd7ed3bb2";

    try {
      if (selectedRecord) {
        const data = { ...selectedRecord };
        delete data["Company"];
        delete data["AuditPerson"];
        delete data["City"];
        delete data["District"];
        delete data["StateProvince"];
        delete data["Station"];
        await updateBuildingWithPut({ ...data, ...formValues });
      } else {
        if (externalCompanyId) {
          formValues["CompanyId"] = externalCompanyId;
        }
        await addBuilding(formValues);
      }
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      form.resetFields();
      setSelectedCompanyId(null);
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "Building");
    }
  };
  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });

      setSelectedCompanyId(selectedRecord["CompanyId"]);
      setMarker({
        lat: selectedRecord["Latitude"],
        lng: selectedRecord["Longitude"],
      });
    }
  }, [selectedRecord]);

  const { Text } = Typography;
  const [marker, setMarker] = useState<google.maps.LatLngLiteral | null>(null);
  const [selectedCompanyId, setSelectedCompanyId] = useState<string | null>(
    null
  );
  useEffect(()=>{
    if(externalCompanyId)
    {
      setSelectedCompanyId(externalCompanyId)
    }
  },[externalCompanyId])

  return (
    <>
      <Col span={24}>
        <MazakaForm
          initialValues={{ Active: true }}
          form={form}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
            <MazakaInput
              className="!m-0"
              label={"Adı"}
              placeholder="Adı"
              name={"Name"}
              xs={24}
              rules={[{ required: true, message: "" }]}
            />
            <GeneralStationInput
              name="StationId"
              label={"Varsayılan İstasyon"}
              placeholder="İstasyon"
              className="!m-0"
              xs={24}
              rules={[{ required: true, message: "" }]}
            />
            {!externalCompanyId && (
              <>
                <GeneralCompanies
                  name="CompanyId"
                  label={"Firma"}
                  placeholder="Firma"
                  className="!m-0"
                  xs={24}
                  rules={[{ required: true, message: "" }]}
                  onChange={(value: string) => {
                    setSelectedCompanyId(value);
                  }}
                />
              </>
            )}

            {selectedCompanyId && (
              <GeneralUserInput
                name="AuditPersonId"
                label={"Sorumlu Kişi"}
                placeholder="Sorumlu Kişi"
                className="!m-0"
                span={24}
               
                externalFilter={{
                  PageSize: -1,
                  CompanyId: selectedCompanyId,
                  RoleId: "44a832a5-fbe2-43eb-8c27-0e5050abd9ea",
                }} //Şantiye Görevlisi
              />
            )}
            <Col xs={24}>
              <Row gutter={[0, 10]}>
                <Col xs={24}>
                  <Text className="!text-black">Konumu Ara</Text>
                </Col>
                <Col xs={24}>
                  
                    <LocationSearchInput
                      xs={24}
                      setMarker={setMarker}
                      form={form}
                    />
               
                </Col>
              </Row>
            </Col>
            <Col span={24}>
              <Row>
                <MazakaTextArea
                  name="Address"
                  label={"Açık Adres"}
                  placeholder="Açık Adres"
                  xs={24}
                  className="!m-0"
                  disabled
                />
                <Col span={24}>
                  <Button type="link" className="!flex items-center !pl-0">
                    <div className="!flex items-center gap-1">
                      <PlusOutlined />
                      <Text
                        onClick={() => {
                          setIsShowMap(true);
                        }}
                        className="!text-primary !text-xs"
                      >
                        Haritadan Seç
                      </Text>
                    </div>
                  </Button>
                </Col>
              </Row>
            </Col>

            <MazakaSwitch
              xs={24}
              label={"Durum"}
              name={"Active"}
              onChange={(status) => {
                form.setFieldValue("Active", status);
              }}
            />

            <Modal
              title=""
              width={"70%"}
              open={isShowMap}
              onCancel={() => {
                setIsShowMap(false);
              }}
              onOk={() => {
                setIsShowMap(false);
              }}
            >
              <CustomGoogleMap
                form={form}
                type="building"
                marker={marker}
                setMarker={setMarker}
              />
            </Modal>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateBuildingForm;
