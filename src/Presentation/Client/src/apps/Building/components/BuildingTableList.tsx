import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Col, Drawer, Dropdown, Modal, Spin, Table, Tooltip, Typography } from "antd";
import { ItemType } from "antd/es/menu/interface";
import { openNotificationWithIcon } from "helpers/Notifications";
import { lazy, Suspense, useState } from "react";
// import AddOrUpdateBuildingForm from "./AddOrUpdateBuilding/AddOrUpdateBuildingForm";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetBuildings } from "../ServerSideStates";
import { hanldleSetBuildingFilter } from "../ClientSideStates";
import { deleteBuilding } from "../Services";
import endpoints from "apps/Building/Endpoints";
import { useQueryClient } from "react-query";
import { MazakaBull } from "apps/Common/MazakaBull";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";

import { FC } from "preact/compat";
import { Tag } from "antd/lib";
const AddOrUpdateBuildingForm = lazy(() => import('apps/Building/components/AddOrUpdateBuilding/AddOrUpdateBuildingForm'))



const BuildingTableList = (props:any) => {
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowEditDrawer, setIsEditDrawer] = useState<boolean>(false);
  const { filter } = useSelector((state: RootState) => state.building);
  const dispatch = useDispatch();
  const buildings = useGetBuildings(
    props.company
      ? {
          CompanyId: props.company?.Id,
          PageIndex: 1,
          PageSize: 20,
          IncludeProperties: ["Company.CompanyUser.User","BuildingUser.User" ],
          SortProperty:"Active",
          SortType:"ASC"
        }
      : filter
  );
  const queryClient = useQueryClient();
  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await setSelectedRecord(record);
        setIsEditDrawer(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteBuilding(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getBuildingListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "Building", true);
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetBuildingFilter({ filter: newFilter }));
  };
  const {Text} = Typography

  const columns = [
    {
      title: "Firma Adı",
      dataIndex: ["Company","Name"],
      key: "Name",
      width: "20%",
      sorter: (a: any, b: any) => {
        const aValue = a?.Company?.Name || '';
        const bValue = b?.Company?.Name || '';
        return aValue.localeCompare(bValue);
      }
    },
    {
      
      title: "Adı",
      dataIndex: "Name",
      key: "Name",
      width: "20%",
      sorter: (a: any, b: any) => a?.Name?.localeCompare(b?.Name),
      render:(value:string,record:any)=>{
        return(
          <>  

          <div>
          {
            value.length<=20?<>
            <Text>{value}</Text>
            </>:<>
            
            <Tooltip title={value}>
            <Text>{value.slice(0,20)}</Text>
            </Tooltip>
            </>

          }
          </div>
          {
            record?.Company&&
            <>
          <div>
          {
            record.Company.Name.length<=20?<>
            <Text className="!text-primary">{record.Company.Name}</Text>
            </>:<>
            
            <Tooltip title={record.Company.Name}>
            <Text className="!text-primary">{record.Company.Name.slice(0,20)}</Text>
            </Tooltip>
            </>

          }
          </div>
            </>
          }
          
          </>
        )
      },
    },
    {
      title: "Konum",
      render: (_: any, record: any) => {
        return (
          <>
            <div>
              <div>{record.Latitude}</div>
              <div>{record.Longitude}</div>
            </div>
          </>
        );
      },
      width: "10%",
    },

    {
      title: "Adres",
      dataIndex: "Address",
      key: "Address",
      width: "20%",
    },

    {
      title: "Şantiye Sorumlusu",
      dataIndex: "IsAuditPersonList",
      width: "20%",
      sorter: (a: any, b: any) => {
        const aLength = a?.IsAuditPersonList?.length || 0;
        const bLength = b?.IsAuditPersonList?.length || 0;
        return bLength - aLength; // Büyükten küçüğe (çok elemanlı önce)
      },
      render:(users:string[])=>{
      return(
          <div className="!flex flex-wrap gap-2" >
            
             {
              users.map((user:any)=>{
                return(
                  <Tag color="green" >{`${user}`}</Tag>
                )
              })
            }
          </div>
        )
      }
    },

    {
      title: "Durum",
      dataIndex: "Active",
      width: "5%",
      sorter: (a: any, b: any) => Number(b?.Active) - Number(a?.Active),
      render: (status: boolean) => {
        return <MazakaBull type={status ? "Success" : "Error"} />;
      },
    },

    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "5%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={buildings.isLoading || buildings.isFetching}
        dataSource={buildings.data ? buildings.data.Data : []}
        rowKey={"Id"}
        scroll={{ x:700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: buildings.data?.FilteredCount || 0,
          current: buildings.data?.PageIndex,
          pageSize: buildings.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <Drawer
        title="Şantiyeyi Güncelle"
        open={isShowEditDrawer}
        onClose={() => {
          setIsEditDrawer(false);
        }}
      >
        {
          isShowEditDrawer&&
          <Suspense fallback={<Spin/>}>
          <AddOrUpdateBuildingForm
            selectedRecord={selectedRecord}
            onFinish={() => {
              setIsEditDrawer(false);
              queryClient.resetQueries({
                queryKey: endpoints.getBuildingListFilter,
                exact: false,
              });
            }}
          />
          
          </Suspense>
        }
      </Drawer>
    </Col>
  );
};

export default BuildingTableList;
