import {deleteRequest, get, patch, post, put} from "services/BaseClient/Client"
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Building/Endpoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";
import { AddBuildingFormModel, BuildingListAndDetails } from "./Models";



export const getBuildingListFilter = async (filter: any): Promise<DataResponse<BuildingListAndDetails>> => {
  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getBuildingListFilter}?${query}`;
    const config = headers.content_type.application_json;
    return get<DataResponse<BuildingListAndDetails>>(url, config);
  };

  export const addBuilding = async (data: any): Promise<DataResponse<AddBuildingFormModel>> => {
    const url = `${endpoints.addBuilding}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<BuildingListAndDetails>>(url, data, config);
  };
  export const updateBuildingWithPut = async (data: any): Promise<DataResponse<AddBuildingFormModel>> => {
    const url = `${endpoints.updateBuildingWithPut}`;
    const config = headers.content_type.application_json;
    return put<DataResponse<BuildingListAndDetails>>(url, data, config);
  };
  export const updateBuildingWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateBuildingWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
  };
  export const deleteBuilding = async (data: any): Promise<DataResponse<{Id:string}>> => {
    const url = `${endpoints.deleteBuilding}/${data.Id}`;
    const config = headers.content_type.application_json;
    return deleteRequest<DataResponse<BuildingListAndDetails>>(url, data, config);
  };