import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";

interface AddOrUpdateFormProps {
  selectedRecord: any;
  setSelectedRecord: any;
  isEditMode: boolean;
  setIsEditMode: any;
  postService: any;
  updateService: any;
  getQueryEndPoint: string;
  formType?: "status" | "pompType";
}

const AddOrUpdateStatusForm: FC<AddOrUpdateFormProps> = ({
  selectedRecord,
  setSelectedRecord,
  isEditMode,
  setIsEditMode,
  postService,
  updateService,
  getQueryEndPoint,
  formType,
}) => {
  const [form] = Form.useForm();
  const queryClient = useQueryClient();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const handleOnFinish = async () => {
    try {
      mazakaForm.setLoading();
      let formValues = form.getFieldsValue();
      selectedRecord
        ? await updateService({ ...selectedRecord, ...formValues })
        : await postService(formValues);

      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
        queryClient.resetQueries({
          queryKey: getQueryEndPoint,
          exact: false,
        });
        setInitialFormStatus();
      });
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "Status");
    }
  };
  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });
    }
  }, [selectedRecord]);

  const setInitialFormStatus = () => {
    setSelectedRecord(null);
    setIsEditMode(false);
    form.resetFields();
  };
  if (!formType) {
    formType = "status";
  }
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          initialValues={{ Active: true }}
          {...formActions}
          cancelButtonOnClick={setInitialFormStatus}
          isCancelButton={isEditMode}
        >
          <Row gutter={[20, 10]}>
            {(() => {
              if (formType === "status") {
                return (
                  <>
                    <MazakaInput
                      className="!m-0"
                      label={"Adı"}
                      placeholder="Adı"
                      name={"Name"}
                      xs={24}
                      md={12}
                      lg={8}
                      rules={[{ required: true, message: "" }]}
                    />
                  </>
                );
              } else if (formType === "pompType") {
                return (
                  <>
                    <MazakaInput
                      className="!m-0"
                      label={"Adı"}
                      placeholder="Adı"
                      name={"Name"}
                      xs={24}
                      rules={[{ required: true, message: "" }]}
                    />
                    <MazakaTextArea
                      name="Description"
                      label={"Açıklama"}
                      placeholder="Açıklama"
                      xs={24}
                      className="!m-0"
                      rules={[{ required: true, message: "" }]}
                    />
                  </>
                );
              }
            })()}
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateStatusForm;
