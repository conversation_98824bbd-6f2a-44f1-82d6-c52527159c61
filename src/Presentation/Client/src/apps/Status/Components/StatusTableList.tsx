import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Col, Dropdown, Modal, Table } from "antd";
import { ItemType } from "antd/es/menu/interface";
import { openNotificationWithIcon } from "helpers/Notifications";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { FC, useMemo } from "react";
import { useQueryClient } from "react-query";
import { useDispatch } from "react-redux";

interface StatusTableListProps {
  selectedRecord: any;
  setSelectedRecord: any;
  isEditMode: boolean;
  setIsEditMode: any;
  getQueryObj: any;
  actionFilterFunc: any;
  filter: any;
  deleteService: any;
  getQueryEndPoint: string;
  formType?: "status" | "pompType";
}

const StatusTableList: FC<StatusTableListProps> = ({
  selectedRecord,
  setSelectedRecord,
  setIsEditMode,
  isEditMode,
  getQueryObj,
  actionFilterFunc,
  filter,
  deleteService,
  getQueryEndPoint,
  formType,
}) => {
  const dispatch = useDispatch();
  const queryClient = useQueryClient();

  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await setSelectedRecord(record);
        setIsEditMode(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];

  const columns = useMemo(() => {
    const baseColumns = [
      {
        title: "",
        dataIndex: "edit",
        key: "edit",
        width: "8%",
        render: (key: any, record: any) => (
          <Col className="text-end pr-2">
            <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
              <EllipsisOutlined className="text-xl" />
            </Dropdown>
          </Col>
        ),
      },
    ];

    if (formType === "status") {
      return [
        {
          title: "Adı",
          dataIndex: "Name",
        },
        ...baseColumns,
      ];
    } else if (formType === "pompType") {
      return [
        {
          title: "Adı",
          dataIndex: "Name",
        },
        {
          title: "Aaçıklama",
          dataIndex: "Description",
        },
        ...baseColumns,
      ];
    }
    return baseColumns;
  }, [formType]);

  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteService(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: getQueryEndPoint,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error,{},"Status",true)
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(actionFilterFunc({ filter: newFilter }));
  };

  return (
    <Col span={24}>
      <Table
        columns={columns}
        dataSource={getQueryObj.data ? getQueryObj.data.Data : []}
        loading={getQueryObj.isLoading || getQueryObj.isFetching}
        rowKey={"Id"}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: getQueryObj.data?.FilteredCount || 0,
          current: getQueryObj.data?.PageIndex,
          pageSize: getQueryObj.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </Col>
  );
};

export default StatusTableList;
