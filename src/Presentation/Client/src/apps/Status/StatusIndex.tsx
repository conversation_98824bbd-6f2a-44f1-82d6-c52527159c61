import MazakaCard from "apps/Common/MazakaCart";

import { FC, useState } from "react";
import AddOrUpdateStatusForm from "./Components/AddOrUpdateStatus";
import StatusTableList from "./Components/StatusTableList";

interface StatusIndexProps{
  postService:any,
  updateService:any,
  getQueryObj:any,
  getQueryEndPoint:string;
  filter:any,
  actionFilterFunc:any;
  deleteService:any;
  formType?: "status" | "pompType";

}

const StatusIndex:FC<StatusIndexProps> = (props) => {
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isEditMode, setIsEditMode] = useState<boolean>(false);
  return (
    <MazakaCard gutter={[10,20]}>
      <AddOrUpdateStatusForm
        selectedRecord={selectedRecord}
        setSelectedRecord={setSelectedRecord}
        isEditMode={isEditMode}
        setIsEditMode={setIsEditMode}
        postService={ props.postService}
        updateService={ props.updateService}
        getQueryEndPoint={props.getQueryEndPoint}
        formType={props.formType}

      />
      <StatusTableList
        selectedRecord={selectedRecord}
        setSelectedRecord={setSelectedRecord}
        isEditMode={isEditMode}
        setIsEditMode={setIsEditMode}
        filter={props.filter}
        actionFilterFunc={props.actionFilterFunc}
        getQueryObj={props.getQueryObj}
        deleteService={props.deleteService}
        getQueryEndPoint={props.getQueryEndPoint}
        formType={props.formType}
      />
    </MazakaCard>
  );
};

export default StatusIndex;
