import { Col, Form } from "antd";
import { SizeType } from "antd/lib/config-provider/SizeContext";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { openNotificationWithIcon } from "helpers/Notifications";
import { FC, useEffect, useState } from "react";
import { useDebouncedCallback } from "use-debounce";

interface CustomUpdateSwitchTableProps {
  name: string;
  record: any;
  service: any;
  useQueryObj: any;
  serviceKey: string;
  loading: boolean;
  setLoading: any;
  label?: string;
  placeholder?: string;
  type: "input" | "textArea";
  autoSize?: boolean;
  size?: SizeType;
  rules?:any;
}
const CustomUpdateInputTable: FC<CustomUpdateSwitchTableProps> = (props) => {
  let [form] = Form.useForm();
  

  const setInitialData = () => {
    if (props.name) {
      form.setFieldValue(props.name, props.record[`${props.serviceKey}`]);
    }
  };

  useEffect(() => {
    setInitialData();
  }, [props.record]);
  const handleUpdateInputProperty = async (value:any) => {
    try {
      let data = {
        Id: props.record.Id,
        patch: [{ path: props.serviceKey, value }],
      };
      await props.service(data);

      openNotificationWithIcon("success", "İşlem başarılı");
    } catch (error: any) {
      setInitialData();
      openNotificationWithIcon(
        "error",
        error?.Message ? error.Message : "İşlem başarısız"
      );
      console.log("Somethings wrong during CustomUpdateInput", error);
    }
  };
  const debounce = useDebouncedCallback((value:any) => {
    if (value) {
    //   handleUpdateInputProperty(value);
    }
  }, 1000);
  let [searchValue, setSearchValue] = useState<string | undefined>(undefined);

  return (
    <>
      <Col span={24}>
        <Form form={form}>
          {props.type === "input" ? (
            <>
              <MazakaInput
                label={props.label}
                placeholder={props.placeholder}
                className="!m-0"
                size={props.size || "small"}
                name={props.name}
                span={24}
                onChange={(e:any) => {
                  setSearchValue(e.target.value.trim());
                  debounce(e.target.value.trim());
                }}
                value={searchValue}
                rules={props.rules}
              />
            </>
          ) : (
            <>
              <MazakaTextArea
                autoSize={props.autoSize}
                label={props.label}
                placeholder={props.placeholder}
                className="!m-0"
                size="small"
                name={props.name}
                span={24}
                onChange={(e:any) => {
                  setSearchValue(e.target.value.trim());
                  debounce(e.target.value.trim());
                }}
                value={searchValue}
                rules={props.rules}
              />
            </>
          )}
        </Form>
      </Col>
    </>
  );
};

export default CustomUpdateInputTable;
