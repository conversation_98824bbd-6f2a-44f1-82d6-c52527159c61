import { Tabs, TabsProps } from 'antd'
import React, { FC } from 'react'

export interface MazakaTabsProps extends TabsProps {
    NavType: "default" | "arounded";
    IsCenter: boolean;
}

export const MazakaTab: FC<MazakaTabsProps> = (props) => {
    const tabsProps = { ...props } as any;
    delete tabsProps.IsCenter;
    delete tabsProps.NavType;
    return (
        <Tabs centered={props?.IsCenter} className={`mazaka-tab ${props.NavType}`} {...tabsProps} />
    )
}
