import { FC } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { normalizeString } from "helpers/TRNormalizedName";

const GeneralPriceTypeInput: FC<GeneralSelectInputs> = (props) => {
  // Statik fiyat tipi verileri
  const priceTypes = [
    {
      Id: 1,
      Name: "Fişli Fiyat"
    },
    {
      Id: 2,
      Name: "<PERSON>ş<PERSON>z Fiyat"
    },
    {
      Id: 3,
      Name: "Santral Bant Fiyatı"
    }
  ];

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={false}
        disabled={props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status = (normalizedLabel ?? "").includes(normalizedInput.toLowerCase());
          return status;
        }}
        label={props.label}
        options={priceTypes.map((item) => {
          return {
            key: item.Id,
            value: item.Id,
            label: item.Name,
          };
        })}
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralPriceTypeInput;