

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { useGetProducts } from "apps/Product/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";


const GeneralProductInput: FC<GeneralSelectInputs> = (props) => {
  const products = useGetProducts({PageSize:-1})
  useEffect(() => {
    if ( products.isError) {
      showServiceErrorMessage( products.error, {}, "GeneralProductInput", true);
    }
  }, [ products.isError]);

  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={products.isLoading || products.isFetching}
        disabled={products.isLoading || products.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={true}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          products.data
            ? products.data.Data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Name,
                 
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralProductInput;
