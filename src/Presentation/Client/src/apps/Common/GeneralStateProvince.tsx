

import useCapitalize from "hooks/useCapitalize";
import { FC, useEffect,} from "react";
import { MazakaSelect } from "./MazakaSelect";
import { useStateProvince } from "hooks/useLocations";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";

const GeneralProvinces: FC<GeneralSelectInputs> = (props) => {
  const stateProvinces = useStateProvince(props.externalValueId);
  let { capitalize } = useCapitalize();

  useEffect(() => {
    if ( stateProvinces.isError) {
      showServiceErrorMessage( stateProvinces.error, {}, "GeneralStateProvinceInput", true);
    }
  }, [ stateProvinces.isError]);

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={stateProvinces.isLoading || stateProvinces.isFetching}
        disabled={
          stateProvinces.isLoading ||
          stateProvinces.isFetching ||
          props.disabled
        }
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          stateProvinces.data
            ? stateProvinces.data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: capitalize(item.Name),
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralProvinces;
