import { Layout, Typography } from "antd";
import { Content } from "antd/lib/layout/layout";
import { FC } from "react";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaLayoutProps } from "models/Client/MazakaLayoutModel";

const { Text, Title } = Typography;

const staticPaddingX = "px-0 md:px-8";
const LayoutStyle = {
  wide: "w-full " + staticPaddingX,
  wideBackgroundNone: "w-full rounded",
  strecth: "w-full lg:!w-[1000px] px-0",
  strecthBackgroundNone: "w-full md:!w-3/4 lg:!w-[1000px]",
};

export const MazakaLayout: FC<MazakaLayoutProps> = (props) => {
  return (
    <>
      {(props.title || props.headDescription) && (
        <div className="!flex !items-start flex-col justify-between ">
          {props.title && (
            <Title level={3} className={"!mb-0 pt-8 " + staticPaddingX}>
              {props.title}
            </Title>
          )}
          {props.headDescription && (
            <Text type="secondary" className={"lg:max-w-2xl" + staticPaddingX}>
              {props.headDescription}
            </Text>
          )}
        </div>
      )}
      {props.buttonTitle && (
        <div className={"flex items-end flex-col pb-4 " + staticPaddingX}>
          <MazakaButton
            onClick={props.buttonOnClick ?? props.onClick}
            icon={props.buttonIcon}
          >
            {props.buttonTitle}
          </MazakaButton>
        </div>
      )}
      <Layout hasSider={props.hasSider} className={"items-center"}>
        <Content className={LayoutStyle[props.layoutType || "wide"]}>
          {props.children}
        </Content>
      </Layout>
    </>
  );
};
