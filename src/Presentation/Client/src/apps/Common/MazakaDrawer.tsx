import { MazakaDrawerProps } from "models/Client/MazakaDrawerProps";
import { Drawer } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { MazakaHeadLayout } from "apps/Common/MazakaHeadLayout";
// import "./MazakaDrawer.less";
import { FC } from "react";

export const MazakaDrawer: FC<MazakaDrawerProps> = (props) => {
  const handleOnClose = () => {
    props.toggleVisible();
  };

  return (
    <Drawer
      open={props.open}
      width="100%"
      id="mazaka-drawer"
      placement={props.placement}
      closable={false}
      onClose={handleOnClose}
      // className="mazaka-drawer"
      destroyOnClose
      afterOpenChange={props.afterOpenChange}
    >
      <MazakaHeadLayout
        title={props.title}
        description={props.text || props.description}
        layoutType={props.layoutType}
        toggleVisible={handleOnClose}
      />
      <MazakaLayout layoutType={props.layoutType}>
        {props.children}
      </MazakaLayout>
    </Drawer>
  );
};
