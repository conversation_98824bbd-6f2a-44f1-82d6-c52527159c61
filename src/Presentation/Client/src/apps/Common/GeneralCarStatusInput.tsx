import useCapitalize from "hooks/useCapitalize";
import { FC, useEffect } from "react";
import { MazakaSelect } from "./MazakaSelect";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";
import { useGetCarStatus } from "apps/Car/ServerSideStates";

const GeneralCarStatusInput: FC<GeneralSelectInputs> = (props) => {
  const carStatus:any = useGetCarStatus({ PageSize: -1 });

  let { capitalize } = useCapitalize();

  useEffect(() => {
    if (carStatus.isError) {
      showServiceErrorMessage(carStatus.error, {}, "GeneralCarStatusInput", true);
    }
  }, [carStatus.isError]);

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={carStatus.isLoading || carStatus.isFetching}
        disabled={carStatus.isLoading || carStatus.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
       
        label={props.label}
        options={
          carStatus.data
            ? carStatus.data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: capitalize(item.Name),
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralCarStatusInput;
