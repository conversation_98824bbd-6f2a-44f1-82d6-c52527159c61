import { FC } from "react";
import { NumericFormat } from 'react-number-format';
interface PriceFormatterProps{
    inputPrice:string |number
}
const PriceFormatter:FC<PriceFormatterProps >= ({inputPrice}) => {
    return ( <>
     <NumericFormat
      value={inputPrice}
      displayType="text"
      thousandSeparator="."
      decimalSeparator=","
      decimalScale={2}
      fixedDecimalScale={true}
      suffix=""
    />
    </> );
}
 
export default PriceFormatter;