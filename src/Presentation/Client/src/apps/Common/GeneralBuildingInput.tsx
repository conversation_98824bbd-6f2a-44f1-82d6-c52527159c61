

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";

import { useGetBuildings } from "apps/Building/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";


const GeneralBuildingInput: FC<GeneralSelectInputs> = (props) => {
  const buildings = useGetBuildings(props.externalValueId ? { CompanyId: props.externalValueId, PageSize: -1, IncludeProperties: ["Company"] } : { PageSize: -1, IncludeProperties: ["Company"] })
  useEffect(() => {
    if (buildings.isError) {
      showServiceErrorMessage(buildings.error, {}, "GeneralBuildingInput", true)
    }


  }, [buildings.isError])
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={buildings.isLoading || buildings.isFetching}
        disabled={buildings.isLoading || buildings.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any) => {
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status = (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          buildings.data
            ? buildings.data.Data.filter((item: any) => {
              if (!props.externalValueId) {
                return item?.Company?.Active
              }
              return item
            }).map((item: any) => {
              return {
                key: item.Id,
                value: item.Id,
                label: item.Name + " (" + item.Company?.Name + ")",

              };
            })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralBuildingInput;
