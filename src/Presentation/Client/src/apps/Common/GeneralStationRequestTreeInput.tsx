

import { FC, useEffect, useState, } from "react";
import { useGetProducts } from "apps/Product/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { useGetStations } from "apps/Station/ServerSideStates";
import { useGetTransactionRequests } from "apps/Plan/ServerSideStates";
import { Col, Form, TreeSelect } from "antd";
import { MazakaSelectProps } from "models/Client/MazakaSelectProps";
import dayjs from "dayjs";
import { normalizeString } from "helpers/TRNormalizedName";


const GeneralStationRequestTree: FC<MazakaSelectProps>  = (props) => {
  const products = useGetProducts({PageSize:-1})
  useEffect(() => {
    if ( products.isError) {
      showServiceErrorMessage( products.error, {}, "GeneralProductInput", true);
    }
  }, [ products.isError]);

  const stations = useGetStations({PageSize:-1,})
  const transactions = useGetTransactionRequests({PageSize:-1,
    StatusIds: [4],
     ApprovedStartDate: dayjs().format("YYYY-MM-DD"),
     ApprovedEndDate :dayjs().format("YYYY-MM-DD"),
    IncludeProperties:["Product","Building.Company",]

  })
  const [treeData,setTreeData] = useState<any[]>([])

  useEffect(() => {
    const transactionDataList = transactions.data ? transactions.data.Data : [];
    const stationDataList = stations.data ? stations.data.Data : [];
  
    
    const newTreeData = stationDataList.map((station: any) => ({
      title: station.Name      ,
      value: station.Id, 
      children: [],
    }));
  
  

   
    transactionDataList.forEach((item: any) => {
      const findStationIndex = newTreeData.findIndex((station: any) => station.value === item.StationId);
      if (findStationIndex !== -1) {
        newTreeData[findStationIndex].children.push({
          title: item?.Building?.Company?.Name+"-"+item?.Building?.Name+"-"+item?.Product?.Name+"-"+item.DesiredTotalConcrete+"m³", // İşlemin adı
          value: item.Id, 
          transaction:item
        });
      }
    });
  
    setTreeData(newTreeData);
  }, [transactions.data, stations.data]);

  const [selectedValue, setSelectedValue] = useState<string | undefined>(undefined);
  const onChange = (value: string) => {
    setSelectedValue(value);
  };

  const filterTreeNode = (inputValue: string, treeNode: any) => {
    const normalizedInput = normalizeString(inputValue.toLowerCase().trim()); // Girdi küçük harf ve normalize edilerek
    const normalizedTitle = normalizeString(treeNode.title.toLowerCase().trim());
    return normalizedTitle.includes(normalizedInput);
  };


  
  return (
    <>
    <Col
      id={props.id}
      className={props.colClassName}
      span={props.span ?? 24}
      md={props.md}
      sm={props.sm}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
      xs={props.xs}
    >
         <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        valuePropName={props.valuePropName}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        noStyle={props.noStyle}
        tooltip={props.tooltip}
      >
    <TreeSelect
    showSearch
    style={{ width: '100%' }}
    value={selectedValue}
    dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
    placeholder="Seç..."
    allowClear
    treeData={treeData.filter((item)=>item?.children?.length>0)}
    treeDefaultExpandAll
    onChange={onChange}
    filterTreeNode={filterTreeNode} 
  />

      </Form.Item>
    </Col>
    </>
  );
};

export default GeneralStationRequestTree;
