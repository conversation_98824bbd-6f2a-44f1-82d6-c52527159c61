import { Col, Form, Input } from "antd";
import { FC } from "react";
import { MazakaTextAreaProps } from "models/Client/MazakaTextAreaProps";

const { TextArea } = Input;

export const MazakaTextArea: FC<MazakaTextAreaProps> = (props) => {
  return (
    <Col
      span={props.span ?? 24}
      xs={props.xs}
      sm={props.sm}
      md={props.md}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        tooltip={props.tooltip}
        validateStatus={props.validateStatus}
        help={props.help}
      >
        <TextArea
          allowClear={props.allowClear}
          bordered={props.bordered}
          autoSize={props.autoSize}
          defaultValue={props.defaultValue}
          showCount={props.showCount}
          onPressEnter={props.onPressEnter}
          onResize={props.onResize}
          value={props.value}
          maxLength={props.maxLength}
          onChange={props.onChange}
          className={props.textAreaClassname}
          placeholder={props.placeholder}
          disabled={props.disabled}
        />
      </Form.Item>
    </Col>
  );
};
