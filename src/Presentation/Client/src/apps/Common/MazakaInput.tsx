import { Col, Form, Input } from "antd";
import { FC } from "react";
import { MazakaInputProps } from "models/Client/MazakaInputModel";

export const MazakaInput: FC<MazakaInputProps> = (props) => {
  return (
    <Col
      className={props.colClassName}
      span={props.span}
      xs={props.xs}
      sm={props.sm}
      md={props.md}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        tooltip={props.tooltip}
        noStyle={props.noStyle}
        help={props.help}
        validateStatus={props.validateStatus}
      >
        <Input
          className={`rounded ${props.inputClassName}`}
          onBlur={props.onBlur}
          onChange={props.onChange}
          value={props?.value}
        
          defaultValue={props.defaultValue}
          prefix={props?.prefix}
          suffix={props?.suffix}
          pattern={props.pattern}
          disabled={props?.disabled}
          min={props.min}
          max={props.max}
          
          bordered={props.bordered}
          size={props.size}
          type={props?.type}
          placeholder={props.placeholder}
          addonAfter={props.addonAfter}
          addonBefore={props.addonBefore}
        />
      </Form.Item>
    </Col>
  );
};
