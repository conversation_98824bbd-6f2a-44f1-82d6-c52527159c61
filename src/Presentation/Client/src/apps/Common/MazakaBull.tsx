import { Typography } from "antd";
import { FC, useEffect, useState } from "react";

const { Text } = Typography;
interface BullsTextProps {
  type: "Error" | "Success" | "Default" | "Warning" | "Processing";
  /**
   * @deprecated `contentText` is deprecated which will be removed in next major version. Please use
   *   `text` instead.
   */
  contentText?: string;
  text?: string;
  className?: string;
  hoverVisible?: "Active";
}

export const MazakaBull: FC<BullsTextProps> = (props) => {
  const [color, setColor] = useState<string>("bg-red-500");
  const [disabled, setDisabled] = useState<boolean>(false);
  const [isTextVisible, setIsTextVisible] = useState<boolean>(false);
  const hoverVisible = props.hoverVisible !== "Active";

  const handleMouseEnter = () => {
    if (hoverVisible) return;
    setIsTextVisible(true);
  };
  const handleMouseLeave = () => {
    if (hoverVisible) return;
    setIsTextVisible(false);
  };

  useEffect(() => {
    switch (props.type) {
      case "Default":
        setColor("bg-gray-500");
        setDisabled(true);
        break;
      case "Processing":
        setColor("bg-blue-500");
        setDisabled(false);
        break;
      case "Success":
        setColor("bg-green-500");
        setDisabled(false);
        break;
      case "Warning":
        setColor("bg-yellow-500");
        setDisabled(false);
        break;
      default:
        //"Error"
        setColor("bg-red-500");
        setDisabled(true);
        break;
    }
  }, [props.type]);

  return (
    <span
      className={`flex items-center gap-3 ${props.className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <Text className={`${color} w-2 h-2 rounded-full`}></Text>
      <Text
        disabled={disabled}
        className={`!p-0 !pt-0.5 pointer-events-none transition-all  ${
          props.hoverVisible === "Active"
            ? isTextVisible
              ? "opacity-100"
              : "opacity-0"
            : "opacity-100"
        }${props.className}`}
      >
        {props.contentText ?? props.text}
      </Text>
    </span>
  );
};
