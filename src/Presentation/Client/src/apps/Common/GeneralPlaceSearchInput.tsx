import React, { FC, useEffect, useState } from "react";
import { LoadScript, Autocomplete } from "@react-google-maps/api";
import { Col, FormInstance, Select } from "antd";

interface GeneralPlaceSearchInputProps {
  setMarker: (coords: { lat: number; lng: number }) => void;
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  form: FormInstance;
}

const GeneralPlaceSearchInput: FC<GeneralPlaceSearchInputProps> = ({
  setMarker,
  xs,
  sm,
  md,
  lg,
  xl,
  form,
}) => {
  const [autocomplete, setAutocomplete] =
    useState<google.maps.places.Autocomplete | null>(null);
  const [inputValue, setInputValue] = useState("");
  const [options, setOptions] = useState<{ value: string; label: string }[]>(
    []
  );
  const [autocompleteService, setAutocompleteService] =
    useState<google.maps.places.AutocompleteService | null>(null);

  const onLoad = (autocompleteInstance: google.maps.places.Autocomplete) => {
    setAutocomplete(autocompleteInstance);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      if (window.google && google.maps.places) {
        setAutocompleteService(new google.maps.places.AutocompleteService());
        clearInterval(interval);
      }
    }, 100);

    return () => clearInterval(interval);
  }, []);

  const handlePlaceChanged = () => {
    if (autocomplete) {
      const place = autocomplete.getPlace();
      if (place?.geometry?.location) {
        const lat = place.geometry.location.lat();
        const lng = place.geometry.location.lng();

        if (lat && lng) {
          try {
            if (place.formatted_address) {
              form?.setFieldValue("Address", place.formatted_address);
            }

            setMarker({ lat, lng });
            setInputValue(place.formatted_address || "");
          } catch (error) {
            console.error("Error fetching address details:", error);
          }
        }
      }
    }
  };

  const handleSearch = (value: string) => {
    setInputValue(value);

    if (autocompleteService && value) {
      autocompleteService.getPlacePredictions({ input: value }, (results) => {
        if (results) {
          setOptions(
            results.map((result) => ({
              value: result.place_id,
              label: result.description,
            }))
          );
        } else {
          setOptions([]);
        }
      });
    }
  };

  const handleSelect = async (value: string) => {
    if (autocompleteService) {
      setInputValue(value);
      const selectedOption = options.find((option) => option.value === value);

      if (selectedOption) {
        form?.setFieldValue("Address", selectedOption.label);

        const placesService = new google.maps.places.PlacesService(
          document.createElement("div")
        );
        placesService.getDetails({ placeId: value }, (place, status) => {
          if (
            status === google.maps.places.PlacesServiceStatus.OK &&
            place?.geometry?.location
          ) {
            const lat = place.geometry.location.lat();
            const lng = place.geometry.location.lng();
            setMarker({ lat, lng });
          }
        });
        setInputValue("");
      }
    }
  };

  return (
    <Col xs={xs} sm={sm} md={md} lg={lg} xl={xl} id="search-place-container">
      <LoadScript
        googleMapsApiKey={process.env.REACT_APP_GOOGLE_MAP_API || ""}
        libraries={["places"]}
        language="tr" 
      >
        <Autocomplete  onLoad={onLoad} onPlaceChanged={handlePlaceChanged}>
          <Select
            showSearch
            value={inputValue}
           
            onSearch={handleSearch}
            onSelect={handleSelect}
            options={options}
            className="!w-full place-search-input"
            filterOption={false} 
          />
        </Autocomplete>
      </LoadScript>
    </Col>
  );
};

export default GeneralPlaceSearchInput;
