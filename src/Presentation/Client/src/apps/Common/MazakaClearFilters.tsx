import { ClearOutlined } from "@ant-design/icons";
import { MazakaButton } from "./MazakaButton";
import { FC } from "react";

interface MazakaClearFiltersProps {
  buttonBlock?: boolean;
}

const MazakaClearFilters: FC<MazakaClearFiltersProps> = (props) => {
  return (
    <>
      <MazakaButton
        className=" focus:!text-white !flex !items-center hover:!bg-primary hover:!text-white"
        type="text"
        onClick={() => {}}
        icon={<ClearOutlined />}
        block={props.buttonBlock}
      >
        {"Filtreyi Temizle"}
      </MazakaButton>
    </>
  );
};

export default MazakaClearFilters;
