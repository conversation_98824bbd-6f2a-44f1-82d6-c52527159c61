import { Button } from "antd";
import { FC } from "react";
import { MazakaButtonModel } from "models/Client/MazakaButtonProps";

import { LoadingSvg } from "assets/Images/FormButtonLoading";

const processInfo = {
  success: "p.General.Success",
  error: "p.General.Error",
  default: "",
  loading: "p.General.Saving",
};

export const MazakaButton: FC<MazakaButtonModel> = ({
  children,
  className,
  type,
  processType,
  rightIcon,
  childrenForce,
  gapItem,
  ...props
}) => {
  

  return (
    <Button
      {...props}
      type={type || "primary"}
      className={`${className}  !transition-all mzk-button-${
        processType || "default"
      } ${
        (!!props.icon || rightIcon) && " !flex !items-center !justify-center"
      }`}
      size={props.size}
    >
      <span
        className={`!flex justify-center gap-${
          gapItem ? gapItem : "4"
        } items-center`}
      >
        {(() => {
          switch (processType) {
            case "loading":
              return (
                <>
                  {!!childrenForce ? children : ""}
                  <LoadingSvg />
                </>
              );
            case "success":
              return (
                <span className={"success"}>
                  {!!childrenForce ? children : "İşlem Başarılı"}
                </span>
              );
            case "error":
              return (
                <span className={"error"}>
                  {!!childrenForce ? children :"İşlem Başarısız" }
                </span>
              );
            default:
              return <>{children}</>;
          }
        })()}
      </span>
      {rightIcon && processType === "default" && (
        <span role="img" aria-label="plus" className="anticon anticon-plus ">
          {rightIcon}
        </span>
      )}
    </Button>
  );
};
