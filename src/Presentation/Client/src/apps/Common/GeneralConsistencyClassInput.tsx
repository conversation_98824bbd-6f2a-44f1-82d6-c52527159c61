

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { useGetConcereteConsistencyClasses,} from "apps/Plan/ServerSideStates";
import { normalizeString } from "helpers/TRNormalizedName";


const GeneralConsistencyInput: FC<GeneralSelectInputs> = (props) => {
  const consistencyClass:any= useGetConcereteConsistencyClasses()
  useEffect(()=>{
    if(consistencyClass.isError)
    {
      showServiceErrorMessage(consistencyClass.error,{},"GeneralConcreteConsistencyInput",true)
    }


},[consistencyClass.isError])


  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={consistencyClass.isLoading || consistencyClass.isFetching}
        disabled={consistencyClass.isLoading || consistencyClass.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          consistencyClass.data
            ? consistencyClass.data.filter((item:any)=>{
              return props.externalConcreteTypeSubItems?props.externalConcreteTypeSubItems?.includes(item.Name):item
            }).map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Name,
                 
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralConsistencyInput;
