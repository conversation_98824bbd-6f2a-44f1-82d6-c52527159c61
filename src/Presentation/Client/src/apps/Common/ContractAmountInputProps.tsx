import { FC, useState, useEffect } from "react";
import { Row, Col, Select, InputNumber, Form } from "antd";

interface ContractAmountInputProps {
  disabled?: boolean;
  className?: string;
  allowClear?: boolean;
  label?: string;
  placeholder?: string;
  onChange?: (type: string, value: number | null) => void;
  onTypeChange?: (type: string) => void; // Yeni prop eklendi
  value?: {
    type: string;
    amount: number | null;
  };
  rules?: any[];
  inputNameAmount?: string;
  inputNameQuantity?: string;
  selectedRecord?: {
    TotalWorth?: number | null;
    TotalAmount?: number | null;
  };
}

const ContractAmountInput: FC<ContractAmountInputProps> = (props) => {
  const [selectedType, setSelectedType] = useState<string>("amount");
  const [inputValue, setInputValue] = useState<number | null>(null);

  // Determine dropdown options based on selectedRecord
  const getTypeOptions = () => {
    const { selectedRecord } = props;

    // Eğer selectedRecord yoksa veya her ikisi de null ise tüm seçenekleri göster
    if (!selectedRecord || (selectedRecord.TotalWorth == null && selectedRecord.TotalAmount == null)) {
      return [
        {
          key: "amount",
          value: "amount",
          label: "Sözleşme Tutarı",
        },
        {
          key: "quantity",
          value: "quantity",
          label: "Sözleşme Miktarı",
        },
      ];
    }

    // Eğer TotalWorth varsa sadece amount seçeneğini göster
    if (selectedRecord.TotalWorth != null) {
      return [
        {
          key: "amount",
          value: "amount",
          label: "Sözleşme Tutarı",
        },
      ];
    }

    // Eğer TotalAmount varsa sadece quantity seçeneğini göster
    if (selectedRecord.TotalAmount != null) {
      return [
        {
          key: "quantity",
          value: "quantity",
          label: "Sözleşme Miktarı",
        },
      ];
    }

    return [];
  };

  // Check if dropdown should be visible
  const isDropdownVisible = () => {
    const options = getTypeOptions();
    return options.length > 1;
  };

  // Get selected type label
  const getSelectedTypeLabel = () => {
    const options = getTypeOptions();
    const selectedOption = options.find(option => option.value === selectedType);
    return selectedOption ? selectedOption.label : "";
  };

  // Handle dropdown change
  const handleTypeChange = (value: string) => {
    setSelectedType(value);
    setInputValue(null);

    if (props.onChange) {
      props.onChange(value, null);
    }

    // Parent component'e type değişikliğini bildir
    if (props.onTypeChange) {
      props.onTypeChange(value);
    }
  };

  // Handle input change
  const handleInputChange = (value: number | null) => {
    setInputValue(value);

    if (props.onChange) {
      props.onChange(selectedType, value);
    }
  };

  // Get input name based on selected type
  const getInputName = () => {
    if (selectedType === "amount") {
      return props.inputNameAmount || "TotalWorth";
    } else {
      return props.inputNameQuantity || "TotalAmount";
    }
  };

  // Get suffix based on selected type
  const getSuffix = () => {
    return selectedType === "amount" ? "TL" : "m³";
  };

  // Initialize values from props and selectedRecord
  useEffect(() => {
    const { selectedRecord } = props;

    if (selectedRecord) {
      if (selectedRecord.TotalWorth != null && selectedRecord.TotalAmount == null) {
        setSelectedType("amount");
        setInputValue(selectedRecord.TotalWorth);
        if (props.onTypeChange) {
          props.onTypeChange("amount");
        }
      }
      else if (selectedRecord.TotalAmount != null && selectedRecord.TotalWorth == null) {
        setSelectedType("quantity");
        setInputValue(selectedRecord.TotalAmount);
        if (props.onTypeChange) {
          props.onTypeChange("quantity");
        }
      }
    }
    
    // Props.value'dan gelen type'ı her zaman uygula
    if (props.value) {
      const { type, amount } = props.value;
      if (type && type !== selectedType) {
        setSelectedType(type);
      }
      if (amount !== undefined && amount !== inputValue) {
        setInputValue(amount);
      }
    }
  }, [props.value, props.selectedRecord]);

  // Parent'tan gelen type değişikliklerini dinle
  useEffect(() => {
    if (props.value?.type && props.value.type !== selectedType) {
      setSelectedType(props.value.type);
    }
  }, [props.value?.type]);

  const typeOptions = getTypeOptions();

  return (
    <Row gutter={[0, 8]} className={props.className} align="top">
      {props.label && (
        <Col xs={24} sm={24} md={24} lg={24} xl={24}>
          <div style={{ fontWeight: 500 }}>
            {props.label}
          </div>
        </Col>
      )}

      {isDropdownVisible() && (
        <Col xs={24} sm={24} md={24} lg={24} xl={12}>
          <div className="me-1">
            <Select
              value={selectedType}
              onChange={handleTypeChange}
              disabled={props.disabled}
              allowClear={props.allowClear}
              placeholder="Sözleşme Tipi Seçin"
              options={typeOptions}
              style={{ width: '100%' }}
            />
          </div>
        </Col>
      )}

      {!isDropdownVisible() ? (
        // Dropdown görünmediğinde label ve input yan yana
        <>
          <Col xs={24} sm={8} md={6} lg={8} xl={8}>
            <div style={{
              fontSize: '14px',
              color: '#333',
              fontWeight: 500,
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              paddingRight: '8px'
            }}>
              {getSelectedTypeLabel()}:
            </div>
          </Col>
          <Col xs={24} sm={16} md={18} lg={16} xl={16}>
            <Form.Item
              name={getInputName()}
              rules={props.rules}
            >
              <InputNumber
                value={inputValue}
                controls={false}
                onChange={handleInputChange}
                disabled={props.disabled}
                placeholder={props.placeholder || "Değer girin"}
                suffix={getSuffix()}
                style={{ 
                  width: '100%',
                  transition: 'none' // Hover uzama efektini engelle
                }}
                className="no-hover-expand"
                min={0}
                precision={selectedType === "amount" ? 2 : 3}
                formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                parser={(value) => {
                  if (!value) return 0;
                  return parseFloat(value.replace(/\$\s?|(,*)/g, '')) || 0;
                }}
                onKeyPress={(e) => {
                  if (!/[\d.]/.test(e.key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)) {
                    e.preventDefault();
                  }
                }}
              />
            </Form.Item>
          </Col>
        </>
      ) : (
        // Dropdown görünürken normal layout
        <Col xs={24} sm={24} md={24} lg={12} xl={12}>
          <Form.Item
            className="ms-1"
            name={getInputName()}
            rules={props.rules}
          >
            <InputNumber
              value={inputValue}
              onChange={handleInputChange}
              disabled={props.disabled}
              placeholder={props.placeholder || "Değer girin"}
              suffix={getSuffix()}
              style={{ 
                width: '100%',
                transition: 'none' // Hover uzama efektini engelle
              }}
              controls={false}
              className="no-hover-expand"
              min={0}
              precision={selectedType === "amount" ? 2 : 3}
              formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={(value) => {
                if (!value) return 0;
                return parseFloat(value.replace(/\$\s?|(,*)/g, '')) || 0;
              }}
              onKeyPress={(e) => {
                if (!/[\d.]/.test(e.key) && !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)) {
                  e.preventDefault();
                }
              }}
            />
          </Form.Item>
        </Col>
      )}
    </Row>
  );
};

export default ContractAmountInput;
