
import useCapitalize from "hooks/useCapitalize";
import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { useCities } from "hooks/useLocations";
import { MazakaSelect } from "./MazakaSelect";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";

const GeneralCities: FC<GeneralSelectInputs> = (props) => {
  const cities = useCities({PageSize:-1,DistrictId:props.externalValueId});
  let { capitalize } = useCapitalize();
  useEffect(()=>{
    if( cities.isError)
    {
      showServiceErrorMessage( cities.error,{},"GeneralCityInput",true)
    }


},[ cities.isError])

  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={cities.isLoading || cities.isFetching}
        disabled={cities.isLoading || cities.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          cities.data
            ? cities.data.Data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: capitalize(item.Name),
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralCities;
