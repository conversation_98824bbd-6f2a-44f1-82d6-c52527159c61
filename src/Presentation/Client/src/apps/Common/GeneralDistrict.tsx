
import useCapitalize from "hooks/useCapitalize";
import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import {  useDistricts, } from "hooks/useLocations";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";
const GeneralDistrict: FC<GeneralSelectInputs> = (props) => {


  const districts = useDistricts({PageSize:-1,StateProvinceId:"fb01b76c-1335-4cbf-9631-08dcd7ed3bb2"});
  let { capitalize } = useCapitalize();

  useEffect(() => {
    if ( districts.isError) {
      showServiceErrorMessage( districts.error, {}, "GeneralDistrictInput", true);
    }
  }, [ districts.isError]);
  

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={districts.isLoading || districts.isFetching}
        disabled={districts.isLoading || districts.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          districts.data
            ? districts.data.Data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: capitalize(item.Name),
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralDistrict;
