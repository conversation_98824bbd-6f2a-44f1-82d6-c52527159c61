import { Col, Form, Input, } from "antd";
import { FC } from "react";
import { PatternFormat } from "react-number-format";

interface GeneralPhoneNumberProps {
  name: string;
  xs?: number;
  sm?: number;
  md?: number;
  lg?: number;
  xl?: number;
  label?: string;
  rules?: any;
  className?: string;
  onChange?:any
}

const GeneralPhoneNumber: FC<GeneralPhoneNumberProps> = (props) => {
  const validatePhoneNumber = (_: any, value: string) => {
    if (value && value.includes("_")&& !value.includes("+90 (___) ___ ____")) {
      return Promise.reject(new Error("Format geçersiz"));
    }
    
    return Promise.resolve();
  };

  return (
    <Col xs={props.xs} sm={props.sm} md={props.md} lg={props.lg} xl={props.xl}>
      <Form.Item
        className={props.className}
        label={props.label}
        name={props.name}
        rules={[...(props.rules || []), { validator: validatePhoneNumber }]} // Add custom validator to rules
      >
        <PatternFormat
          format="0(###) ### ####"
          allowEmptyFormatting
          mask="_"
          customInput={Input}
          required={true}
          onChange={props.onChange}
         
        />
      </Form.Item>
    </Col>
  );
};

export default GeneralPhoneNumber;
