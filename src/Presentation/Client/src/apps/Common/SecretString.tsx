import { EyeInvisibleOutlined, EyeOutlined } from "@ant-design/icons";
import { Space } from "antd";
import React, { useEffect, useState } from "react";

type SecretTextType = "normal" | "email" | "phone";
interface SecretTextProps {
  text: string;
  textType: SecretTextType;
  /**
   * This parameter use when if you want to secret text from the begin in normal texts..
   */
  secretLenghOfset?: number;
  color?: string;
}

/**
 * @desc If you want to secret your text, you can use this function.
 *
 *
 * @param text string
 * @param textType "normal" | "email" | "phone"
 * @param secretLenghOfset number - This parameter use when if you want to secret text from the begin in normal texts..
 * @returns
 */
export const SecretText: React.FunctionComponent<SecretTextProps> = ({
  text,
  textType,
  secretLenghOfset = 3,
  color,
}) => {
  const [contentText, setContentText] = useState("");
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    if (visible) {
      setContentText(text);
      return;
    }
    switch (textType) {
      case "email":
        setContentText(onSecretEmail(text));
        break;
      case "phone":
        setContentText(onSecretPhone(text));
        break;
      default:
        setContentText(onSecretNormalText(text, secretLenghOfset));
        break;
    }
  }, [textType, secretLenghOfset, text, visible]);

  return (
    <>
      {text && text.length > 0 && (
        <>
          <div  className={`text-[${color}] !flex items-center !text-sm`}>
            {contentText}&nbsp;
            {visible ? (
              <EyeInvisibleOutlined
                className="cursor-pointer !mb-1"
                onClick={() => setVisible(false)}
              />
            ) : (
              <EyeOutlined
                className="cursor-pointer !mb-1"
                onClick={() => setVisible(true)}
              />
            )}
          </div>
        </>
      )}
    </>
  );
};

const onSecretEmail = (str?: string): string => {
  if (!str) return "-";
  const strArr = str.split("@");
  return (
    strArr[0]
      .split("")
      .reduce(
        (prev, current, currentIndex) =>
          prev + (currentIndex !== 0 ? "*" : current),
        ""
      ) +
    "@" +
    strArr[1]
  );
};
const onSecretNormalText = (str?: string, offsetLenght?: number): string => {
  if (!str) return "";
  const strOfset = str.slice(0, offsetLenght);
  const strSecret = str.slice(offsetLenght, str.length);
  return strOfset + strSecret.split("").reduce((prev) => prev + "*", "");
};

const onSecretPhone = (str?: string): string => {
  if (!str) return "";
  return str
    .split("")
    .reduce(
      (prev, current, currentIndex) =>
        prev + (currentIndex >= 5 && current !== " " ? "*" : current),
      ""
    );
};
