import { Card, Col, Row, Typography } from "antd";
import Meta from "antd/lib/card/Meta";
import { FC, useState, useEffect } from "react";
import { MazakaCardProps } from "models/Client/MazakaCartMode";



const { Text } = Typography;

const MazakaCardBase: FC<MazakaCardProps> = ({
  children,
  gutter,
  title,
  description,
  titlePosition,
  titleButton,
  loading,
  className,
  size,
  comingSoon,
  onMouseEnter,
  onMouseLeave,
  bodyStyle,
  isFetchingDataError,
}) => {
 
  const [textAlign, setTextAlign] = useState("text-left");
  const [metaTitleClass, setMetaTitleClass] = useState("justify-between");

  useEffect(() => {
    if (!!titleButton) {
      setTextAlign("text-left");
      setMetaTitleClass("justify-between");
    }
    switch (titlePosition) {
      case "Left":
        setTextAlign("text-left");
        setMetaTitleClass("justify-start");
        break;
      case "LeftOut":
        setTextAlign("text-right !w-24 break-words whitespace-normal");
        setMetaTitleClass("justify-start lg:!absolute lg:!-left-28 lg:!w-24");
        break;
      case "Right":
        setTextAlign("text-right");
        setMetaTitleClass("justify-end");
        break;
      case "RightOut":
        setTextAlign("text-right");
        setMetaTitleClass(
          "justify-end relative lg:!absolute lg:!-right-28 lg:!w-24"
        );
        break;
      default:
        break;
    }
  }, [titlePosition, titleButton]);

  return (
    <>
      <>
        <Card
          className={`w-full h-full ${
            comingSoon ? "coming-soon" : ""
          } ${className}`}
          size={size}
          loading={loading}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          bodyStyle={bodyStyle}
        >
          {title && (
            <Meta
              title={
                <div className={`w-full flex items-center ${metaTitleClass} `}>
                  <Text className={`${textAlign}`} strong>
                    {title}
                  </Text>
                  {titleButton}
                </div>
              }
              description={description}
              className={`pb-3 !block !w-full`}
            />
          )}
          <Row className="!h-full" gutter={gutter || [0, 0]}>
            {children}
          </Row>
        </Card>
      </>
    </>
  );
};

const MazakaCard: FC<MazakaCardProps> = (props) => {
  const standartCols = {
    xs: props.xs,
    sm: props.sm,
    md: props.md,
    lg: props.lg,
    xl: props.xl,
    xxl: props.xxl,
    span: props.span || 24,
  };

  return (
    <Col {...standartCols} id={props.id} className={`mazaka-card`}>
      <MazakaCardBase {...props} />
    </Col>
  );
};

export default MazakaCard;
