import { Col, Form, Select } from "antd";
import { FC } from "react";
import { MazakaSelectProps } from "models/Client/MazakaSelectProps";

export const MazakaSelect: FC<MazakaSelectProps> = (props) => {
  return (
    <Col
      id={props.id}
      className={props.colClassName}
      span={props.span ?? 24}
      md={props.md}
      sm={props.sm}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
      xs={props.xs}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        valuePropName={props.valuePropName}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        noStyle={props.noStyle}
        tooltip={props.tooltip}
      >
        <Select
          open={props.open}
          allowClear={props.allowClear}
          dropdownRender={props.dropdownRender}
          mode={props.mode}
          searchValue={props.searchValue}
          bordered={props.bordered}
          placeholder={props.placeholder}
          maxTagCount={props.maxTagCount}
          maxTagTextLength={props.maxTagTextLength}
          status={props.status}
          options={props.options}
          disabled={props.disabled}
          size={props.size}
          loading={props.loading}
          onSelect={props.onSelect}
          onDeselect={props.onDeselect}
          onChange={props.onChange}
          showSearch={props.showSearch}
          optionFilterProp={props.optionFilterProp}
          filterOption={props.filterOption}
          labelInValue={props.labelInValue}
          filterSort={props.filterSort}
          onSearch={props.onSearch}
          value={props.value}
          defaultValue={props.defaultValue}
          onClear={props.onClear}
        ></Select>
      </Form.Item>
    </Col>
  );
};
