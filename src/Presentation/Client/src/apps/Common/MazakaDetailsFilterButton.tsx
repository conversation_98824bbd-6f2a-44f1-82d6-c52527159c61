import { FilterOutlined } from "@ant-design/icons";
import { MazakaButton } from "./MazakaButton";
import { FC } from "react";

interface MazakaDetailsFilterButtonProps{
    setIsShowDetailsFilter:React.Dispatch<React.SetStateAction<boolean>>
    buttonBlock?:boolean
}


const MazakaDetailsFilterButton:FC<MazakaDetailsFilterButtonProps >= (props) => {
    return ( <>
    <MazakaButton
              className="  !flex !items-center  hover:!bg-primary hover:!text-white"
              type="text"
              icon={<FilterOutlined />}
              onClick={() => {
                props.setIsShowDetailsFilter(true);
              }}
              block={props.buttonBlock}
            >
              {"Detaylı Filtre"}
            </MazakaButton>
    </> );
}
 
export default MazakaDetailsFilterButton;