

import { FC, useEffect,} from "react";
import { MazakaSelect } from "./MazakaSelect";

import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { useGetContractTemplates } from "apps/Contract/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";

const GeneralContractTemplateInput: FC<GeneralSelectInputs> = (props) => {
  const contractTemplates = useGetContractTemplates({PageSize:-1})
 
  useEffect(() => {
    if (contractTemplates.isError) {
      showServiceErrorMessage(contractTemplates.error, {}, "GeneralContractTemplateInput", true);
    }
  }, [contractTemplates.isError]);



  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={contractTemplates.isLoading || contractTemplates.isFetching}
        disabled={
          contractTemplates.isLoading ||
          contractTemplates.isFetching ||
          props.disabled
        }
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input:any, option: any) =>
          (option.label ?? "").toLowerCase().includes(input.toLowerCase())
        }
        label={props.label}
        options={
          contractTemplates.data
            ? contractTemplates.data.Data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Title,
                  desc:item.Template
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralContractTemplateInput;
