import React, { FC, useMemo, useRef } from "react";
import BuildingMarker from "assets/Images/TokGoz/buildingCustomMarker.png";
import StationMarker from "assets/Images/TokGoz/stationCustomMarker.png";
import TransportMarker from "assets/Images/TokGoz/transport.png";
import { GoogleM<PERSON>, Marker, useJsApiLoader } from "@react-google-maps/api";
import { Col, Row, Typography } from "antd";
import { FormInstance } from "antd/lib";


interface CustomGoogleMapProps {
  onFinish?: any;
  form?: FormInstance;
  marker: any;
  setMarker: any;
  type:"station" |"building"|"vehicleTracking"
}

const containerStyle = {
  width: "100%",
  height: "400px",
};

const defaultCenter = {
  lat: 38.733509, // Kayseri coordinates
  lng: 35.485397,
};

const GOOGLE_MAP_LIBRARIES:any = ["places"] ;

const CustomGoogleMap: FC<CustomGoogleMapProps> = (props) => {
  const { isLoaded } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: process.env.REACT_APP_GOOGLE_MAP_API || "", 
    libraries: GOOGLE_MAP_LIBRARIES, 
  });
  const geocoder = useRef<google.maps.Geocoder | null>(null);

  if (!geocoder.current && isLoaded) {
    geocoder.current = new google.maps.Geocoder();
  }
  const onMapClick = async (event: google.maps.MapMouseEvent) => {
    const latLng:any = event.latLng;

  
    props.setMarker({
      lat: latLng.lat(),
      lng: latLng.lng(),
    });

    if (geocoder.current) {
      geocoder.current.geocode({ location: latLng }, (results:any, status) => {
        if (status === "OK" && results[0]) {
          const address = results[0].formatted_address;
          if(props.form)
          {

            props.form.setFieldValue("Address", address);
          }
        } else {
          console.error("Geocoder failed due to:", status);
        }
      });
    }
  };
  

  const { Text } = Typography;
  const mapCenter = useMemo(() => props.marker || defaultCenter, [props.marker]);

  return isLoaded ? (
    <Row gutter={[10, 20]}>
      {
        props.type !=="vehicleTracking"&&
      <Col span={24}>
        <Row gutter={[10, 10]} className="pr-2">
          {props.marker && (
            <>
              <Col xs={24} className="">
                <div>
                  <Text className="!text-bold">
                    Tıklanan yerin Konum adresi:
                  </Text>
                </div>
                <div>
                  <Text className="!text-primary">
                    {props.marker?.lat} + {props.marker?.lng}
                  </Text>
                </div>
              </Col>
            </>
          )}
        </Row>
      </Col>
      }
      <Col span={24}>
        <GoogleMap
          mapContainerStyle={containerStyle}
          center={mapCenter}
          zoom={15}
          onClick={ props.type ==="vehicleTracking"?undefined:onMapClick}
        >
          {props.marker && (
            <Marker
              position={props.marker}
              icon={{
                url: props.type==="station"?StationMarker:props.type==="vehicleTracking"?TransportMarker:BuildingMarker, 
                scaledSize: new google.maps.Size(30, 30), 
                anchor: new google.maps.Point(15, 15), 
              }}
            />
          )}
        </GoogleMap>
      </Col>
    </Row>
  ) : (
    <p>Harita yükleniyor...</p>
  );
};

export default React.memo(CustomGoogleMap);
