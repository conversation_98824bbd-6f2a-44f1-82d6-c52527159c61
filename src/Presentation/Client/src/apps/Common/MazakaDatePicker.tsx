import { Col, DatePicker, Form } from "antd";
import { FC } from "react";
import { MazakaPickerModel } from "models/Client/MazakaDatePickerProps";
import moment from "moment";

const dateFormat = "YYYY-MM-DD";

export const MazakaDatePicker: FC<MazakaPickerModel> = (props) => {
  return (
    <Col
      span={props.span ?? 24}
      xs={props.xs}
      sm={props.sm}
      md={props.md}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        tooltip={props.tooltip}
      >
        <DatePicker
          size={props.size || "middle"}
          style={{ width: "100%" }}
          format={dateFormat}
          disabledDate={props.disablePastDates}
          placeholder={props.DatePickerPlaceHolder}
          defaultValue={props.defaultValue}
          onChange={props.onChange}
          disabled={props.disabled}
       
          
          inputReadOnly={false}
        />
      </Form.Item>
    </Col>
  );
};
