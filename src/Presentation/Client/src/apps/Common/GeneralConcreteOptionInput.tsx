

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { useGetConcereteOptions } from "apps/Plan/ServerSideStates";
import { normalizeString } from "helpers/TRNormalizedName";


const GeneralConcreteOptions: FC<GeneralSelectInputs> = (props) => {
  const options:any= useGetConcereteOptions()
  useEffect(()=>{
    if(options.isError)
    {
      showServiceErrorMessage(options.error,{},"GeneralConcreteOptionsInput",true)
    }


},[options.isError])


  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={options.isLoading || options.isFetching}
        disabled={options.isLoading || options.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          options.data
            ? options.data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Name,
                 
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralConcreteOptions;
