

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { useGetBuildingUsers, } from "apps/User/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";

const GeneralBuildingUserInput: FC<GeneralSelectInputs> = (props) => {
  const users= useGetBuildingUsers(props.externalFilter?props.externalFilter:{PageSize:-1,IncludeProperties:["User","Building"]})
  useEffect(() => {
    if ( users.isError) {
      showServiceErrorMessage( users.error, {}, "GeneralBuildingUserInput", true);
    }
  }, [ users.isError]);

  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={users.isLoading || users.isFetching}
        disabled={users.isLoading || users.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          users.data
            ? users.data.Data.map((item: any) => {
                return {
                  key: item.UserId,
                  value: item.UserId,
                  label: (item?.User?.Name||"")+"-"+(item?.Building?.Name||""),
                 
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralBuildingUserInput;
