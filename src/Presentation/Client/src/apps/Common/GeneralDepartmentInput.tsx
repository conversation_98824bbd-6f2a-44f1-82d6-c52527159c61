

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { useGetDepartments } from "apps/Deparment/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";
const GeneralDepartmentInput: FC<GeneralSelectInputs> = (props) => {
  const departments = useGetDepartments({PageSize:-1})
  useEffect(() => {
    if (departments.isError) {
      showServiceErrorMessage(departments.error, {}, "GeneralDepartmentInput", true);
    }
  }, [departments.isError]);

  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={departments.isLoading || departments.isFetching}
        disabled={departments.isLoading || departments.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          departments.data
            ? departments.data.Data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Name,
                 
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralDepartmentInput;
