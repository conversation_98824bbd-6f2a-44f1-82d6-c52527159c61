import { FC, useEffect } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { useGetContractPaymentPlans } from "apps/Contract/ServerSideStates";
import { normalizeString } from "helpers/TRNormalizedName";

const GeneralContractPaymentPlanInput: FC<GeneralSelectInputs> = (props) => {
  const contractPaymentPlans: any = useGetContractPaymentPlans();
  useEffect(() => {
    if (contractPaymentPlans.isError) {
      showServiceErrorMessage(
        contractPaymentPlans.error,
        {},
        "GeneralContractPaymentPlanInput",
        true
      );
    }
  }, [contractPaymentPlans.isError]);

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={
          contractPaymentPlans.isLoading || contractPaymentPlans.isFetching
        }
        disabled={
          contractPaymentPlans.isLoading ||
          contractPaymentPlans.isFetching ||
          props.disabled
        }
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          contractPaymentPlans.data
            ? contractPaymentPlans.data
                .filter((item: any) => {
                  if (
                    props.externalFilteredData &&
                    props.externalFilteredData.length > 0
                  ) {
                    return !props.externalFilteredData.includes(item.Id);
                  }
                  return item;
                })
                .map((item: any) => {
                  return {
                    key: item.Id,
                    value: item.Id,
                    label: item?.Name || "",
                  };
                })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralContractPaymentPlanInput;
