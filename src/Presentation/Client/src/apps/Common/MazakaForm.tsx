import { Col, Form, Row, Typography } from "antd";
import Title from "antd/lib/typography/Title";
import { FC, useId } from "react";

import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaFormProps } from "models/Client/MazakaFormProps";

const { Text } = Typography;

const MazakaFormSubmitArea: FC<MazakaFormProps> = ({
  submitButtonXPosition = "right",
  submitButtonYPosition = "bottom",
  submitButtonVisible = true,
  isCancelButton = false,
  ...props
}) => {
 
  return (
    <div
      className={`w-full flex justify-end items-center gap-4  
      ${submitButtonYPosition === "bottom" ? "mt-5" : ""} 
      ${submitButtonXPosition === "left" ? "flex-row-reverse" : ""} ${
        props.submitButtonCustomClass
      } `}
    >
      <div className={`h-full`}>{props?.submitContent}</div>
      {submitButtonVisible && (
        <MazakaButton
          htmlType="submit"
          icon={props.submitIcon}
          processType={props.submitProcessType}
          childrenForce={!!props.submitText}
        >
          {props.submitText ||"Kaydet" }
        </MazakaButton>
      )}
      {isCancelButton && (
        <MazakaButton
          className="!bg-red-500"
          icon={props.submitIcon}
          onClick={props.cancelButtonOnClick}
        >
          {"İptal Et"}
        </MazakaButton>
      )}
    </div>
  );
};
export const MazakaForm: FC<MazakaFormProps> = (props) => {
  const formId = "form_" + useId();
  const formItemLayout = {
    labelCol: props.labelCol || {
      xs: { span: 24 },
      sm: { span: 24 },
    },
    wrapperCol: props.wrapperCol || {
      xs: { span: 24 },
      sm: { span: 24 },
    },
  };
  return (
    <Row gutter={[0, 10]} className={props.className}>
      <Col
        span={props.span ?? 24}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        xxl={props.xxl}
      >
        {(props.formText || props.formTitle) && (
          <>
            <Title
              underline={props.underline}
              type={props.type}
              code={props.code}
              mark={props.mark}
              level={props.level ?? 2}
              style={{ marginBottom: "0px" }}
            >
              {props.formTitle}
            </Title>
            <Text
              className="block text-gray-300 mb-5"
              copyable={props.copyable}
            >
              {props.formText}
            </Text>
          </>
        )}
        <Form
          id={formId}
          form={props.form}
          name={props.name}
          layout={props.layout ?? "vertical"}
          className={`w-full ${
            props.submitButtonYPosition === "top"
              ? "flex flex-col-reverse gap-4"
              : ""
          }`}
          initialValues={props.initialValues}
          onFinish={props.onFinish}
          onFinishFailed={props.onFinishFailed}
          autoComplete="off"
          onValuesChange={props.onValuesChange}
          onFieldsChange={props.onFieldsChange}
          {...formItemLayout}
        >
          {props.children}
          {props.isWraperSubmit ? (
            // <MazakaCard bodyStyle={{ padding: "0" }} className="!border-none">
            <MazakaFormSubmitArea {...props} />
          ) : (
            // </MazakaCard>
            <MazakaFormSubmitArea {...props} />
          )}
        </Form>
      </Col>
    </Row>
  );
};
