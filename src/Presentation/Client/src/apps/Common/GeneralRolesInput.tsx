import { FC, useEffect } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";
import { useGetUserRoles } from "apps/User/ServerSideStates";

const GeneralUserRolesInput: FC<GeneralSelectInputs> = (props) => {
  const roles: any = useGetUserRoles();
  useEffect(() => {
    if (roles.isError) {
      showServiceErrorMessage(
        roles.error,
        {},
        "GeneralRoleInput",
        true
      );
    }
  }, [roles.isError]);

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={
          roles.isLoading || roles.isFetching
        }
        disabled={
          roles.isLoading ||
          roles.isFetching ||
          props.disabled
        }
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          roles.data
            ? roles.data
                .filter((item: any) => {
                  if (
                    props.externalFilteredData &&
                    props.externalFilteredData.length > 0
                  ) {
                    return !props.externalFilteredData.includes(item.Id);
                  }
                  return item;
                })
                .map((item: any) => {
                  return {
                    key: item.Id,
                    value: item.Id,
                    label: item?.Name || "",
                  };
                })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralUserRolesInput;
