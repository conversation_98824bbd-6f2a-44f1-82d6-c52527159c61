

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { useGetStationStatus } from "apps/Station/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";
const GeneralStationStatusInput: FC<GeneralSelectInputs> = (props) => {
  const stationStatus:any = useGetStationStatus({PageSize:-1})
  useEffect(() => {
    if ( stationStatus.isError) {
      showServiceErrorMessage( stationStatus.error, {}, "GeneralstationsStatusInput", true);
    }
  }, [ stationStatus.isError]);

  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={stationStatus.isLoading || stationStatus.isFetching}
        disabled={stationStatus.isLoading || stationStatus.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          stationStatus.data
            ? stationStatus.data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.DisplayName,
                  name:item.Name
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralStationStatusInput;
