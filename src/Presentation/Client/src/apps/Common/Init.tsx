
import { Spin } from "antd";
import { handleSetAccountInfo } from "apps/Account/ClientSideStates";
import { getUserInfo } from "apps/Account/Services";
import { openNotificationWithIcon } from "helpers/Notifications";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { Navigate, Outlet, useNavigate } from "react-router-dom";

const Init = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const getUserInformations = async () => {
      setIsLoading(true)
      try {
        const token = localStorage.getItem("access_token");
        if (token) {
          const response:any = await getUserInfo();
          if (response?.Value) {
            dispatch(handleSetAccountInfo({ data: response.Value }));
          } 
        } else {
          localStorage.removeItem("access_token");
          navigate("/account/login")
         
        }
      } catch (error:any) {
        const errorMessage =
          error?.Message || error?.Value?.Message || "Kullanıcı Bilgileri çekerken bir hata oluştur";
        openNotificationWithIcon("error", errorMessage);

        localStorage.removeItem("access_token");
        navigate("/account/login")
        
      }
      finally{
        setIsLoading(false)
      }
    };

    getUserInformations();
  }, [dispatch]);

  if (isLoading) {
    return <Spin/>; 
  }

  if (!localStorage.getItem("access_token")) {
    return <Navigate to="/account/login" replace />;
  }

  return (
    <>
      <Outlet />
    </>
  );
};

export default Init;

