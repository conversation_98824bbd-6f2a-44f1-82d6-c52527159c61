import React, { useEffect, useState } from "react";
import { TruckOutlined } from "@ant-design/icons";

const CarAnimations = () => {
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setIsAnimating((prev) => !prev);
    }, 4000); // 4 saniyede bir animasyonu değiştirir

    return () => clearInterval(timer);
  }, []);

  return (
    <div className="truck-animation-container ">
      <div className={`truck-icon ${isAnimating ? "move-truck" : ""}`}>
        <TruckOutlined className="!text-primary"/>
      </div>
    </div>
  );
};

export default CarAnimations;
