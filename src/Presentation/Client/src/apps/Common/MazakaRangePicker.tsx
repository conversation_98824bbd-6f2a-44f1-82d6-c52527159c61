import { Col, DatePicker, Form } from "antd";
import { FC } from "react";
import { MazakaRangePickerProps } from "models/Client/MazakaRangePickerProps";

const { RangePicker } = DatePicker;
const dateFormat = "DD/MM/YYYY";

export const MazakaRangePicker: FC<MazakaRangePickerProps> = (props) => {
 
  return (
    <Col
      span={props.span ?? 24}
      xs={props.xs}
      sm={props.sm}
      md={props.md}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        tooltip={props.tooltip}
      >
        <RangePicker
          onChange={props.onChange}
          size={props.size || "middle"}
          placeholder={[
            props.LeftPlaceHolder || "Min",
            props.RightPlaceHolder || "Max",
          ]}
          style={{ width: "100%" }}
          format={dateFormat}
          value={props.value}
          showTime={props.showTime}
          disabled={props.disabled}
        />
      </Form.Item>
    </Col>
  );
};
