import { Col, Form, Switch } from "antd";
import { FC } from "react";
import { MazakaSwitchProps } from "models/Client/MazakaSwitchProps";

export const MazakaSwitch: FC<MazakaSwitchProps> = (props) => {
  return (
    <Col
      span={props.span ?? 24}
      xs={props.xs}
      sm={props.sm}
      md={props.md}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
      className={props.wrapperClassName}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        noStyle={props.noStyle}
        valuePropName="checked"
      >
        <Switch
          size={props.size}
          onChange={props.onChange}
          onClick={props.onClick}
          disabled={props.disabled}
          loading={props.loading}
          checked={props.checked}
          checkedChildren={"Aktif"}
          unCheckedChildren={"Pasif"}
          defaultChecked={props.defaultChecked}
          ref={props.ref}
        />
      </Form.Item>
    </Col>
  );
};
