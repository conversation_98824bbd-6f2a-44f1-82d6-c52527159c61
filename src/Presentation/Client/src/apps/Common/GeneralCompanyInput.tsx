import useCapitalize from "hooks/useCapitalize";
import { FC, useEffect } from "react";
import { MazakaSelect } from "./MazakaSelect";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { useGetCompanies } from "apps/Company/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";

const GeneralCompanies: FC<GeneralSelectInputs> = (props) => {
  const companies = useGetCompanies({ PageSize: -1 });

  let { capitalize } = useCapitalize();

  useEffect(() => {
    if (companies.isError) {
      showServiceErrorMessage(companies.error, {}, "GeneralCompanyInput", true);
    }
  }, [companies.isError]);

  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={companies.isLoading || companies.isFetching}
        disabled={companies.isLoading || companies.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
       
        label={props.label}
        options={
          companies.data
            ? companies.data.Data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: capitalize(item.Name),
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralCompanies;
