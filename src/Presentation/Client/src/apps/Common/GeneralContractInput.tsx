

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { useGetContracts } from "apps/Contract/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";


const GeneralContractInput: FC<GeneralSelectInputs> = (props) => {
  const contracts= useGetContracts({PageSize:-1})
  useEffect(() => {
    if (contracts.isError) {
      showServiceErrorMessage(contracts.error, {}, "GeneralContractInput", true);
    }
  }, [contracts.isError]);

  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={contracts.isLoading || contracts.isFetching}
        disabled={contracts.isLoading || contracts.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          contracts.data
            ? contracts.data.Data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Title,
                 
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralContractInput;
