

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { useGetConcereteLocations } from "apps/Plan/ServerSideStates";
import { normalizeString } from "helpers/TRNormalizedName";


const GeneralConcreteLocationsInput: FC<GeneralSelectInputs> = (props) => {
  const locations:any= useGetConcereteLocations()
  useEffect(()=>{
    if(locations.isError)
    {
      showServiceErrorMessage(locations.error,{},"GeneralConcreteLocationsInput",true)
    }


},[locations.isError])


  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={locations.isLoading || locations.isFetching}
        disabled={locations.isLoading || locations.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          locations.data
            ? locations.data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Name,
                  subItems:item.SubItem.split(",")
                 
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralConcreteLocationsInput;
