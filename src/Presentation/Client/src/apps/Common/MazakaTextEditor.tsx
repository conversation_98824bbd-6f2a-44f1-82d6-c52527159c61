import { Col, Form } from "antd";
import { MazakaInputProps } from "models/Client/MazakaInputModel";
import { FC,  useMemo, useRef,  } from "react";
import ReactQuill, { Quill } from "react-quill";
import "react-quill/dist/quill.snow.css";



const MazakaTextEditor: FC<MazakaInputProps> = (props) => {
  // const [deltaContent, setDeltaContent] = useState<any>(null);
  const quillRef = useRef<ReactQuill | null>(null);

  const modules = useMemo(() => ({
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ script: "sub" }, { script: "super" }],
      [{ indent: "-1" }, { indent: "+1" }],
      [{ direction: "rtl" }],
      [{ size: ["small", false, "large", "huge"] }],
      [{ color: [] }, { background: [] }],
      [{ font: [] }],
      [{ align: [] }],
      ["clean"],
    ],
  }), []);

  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "list",
    "bullet",
    "script",
    "indent",
    "direction",
    "size",
    "color",
    "background",
    "font",
    "align",
  ];


  return (
    <Col span={props.span ?? 24}>
      <Form.Item
        className={`${props.className}`}
        name={props.name}
        // initialValue={deltaContent}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        tooltip={props.tooltip}
        noStyle={props.noStyle}
      >
        <ReactQuill
          ref={quillRef}
          theme="snow"
          // value={deltaContent}
          // onChange={(_, __, ___, editor) => {
          //   const content = editor.getContents();
          //   // setDeltaContent(content);
          //   props.form.setFieldValue(props.name, content);
          // }}
          modules={modules}
          formats={formats}
          placeholder={props.placeholder}
          style={{  maxHeight: "500px", overflowY: "auto" }}
        />
      </Form.Item>
    </Col>
  );
};

export default MazakaTextEditor;
