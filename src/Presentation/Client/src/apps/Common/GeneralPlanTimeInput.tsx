

import { FC,  } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import dayjs from "dayjs";


const GeneralPlanTimePeriodes: FC<GeneralSelectInputs> = (props) => {
    const options = [
        { label: "Bugün", value: dayjs().format("YYYY-MM-DD") },
        { label: "Yarın", value: dayjs().add(1, "day").format("YYYY-MM-DD") },
        { label: "Gelecek 7 Gün", value: dayjs().add(7, "day").format("YYYY-MM-DD") },
        { label: "Gelecek 15 Gün", value: dayjs().add(15, "day").format("YYYY-MM-DD") },
        { label: "Tümü", value: "all" },
      ];

  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        disabled={ props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input:any, option: any) =>
          (option.label ?? "").toLowerCase().includes(input.toLowerCase())
        }
        label={props.label}
        options={options}
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralPlanTimePeriodes;
