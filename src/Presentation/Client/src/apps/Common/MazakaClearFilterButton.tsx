import { ClearOutlined,} from "@ant-design/icons";
import { MazakaButton } from "./MazakaButton";
import { FC } from "react";
import { useDispatch } from "react-redux";

interface MazakaDetailsFilterButtonProps{
   actionFunk:any
   type?:string;
   
}


const MazakaClearFilterButton:FC<MazakaDetailsFilterButtonProps >= (props) => {
    const dispatch = useDispatch()
    return ( <>
    <MazakaButton
              className="  !flex !items-center  hover:!bg-primary hover:!text-white"
              type="text"
              icon={<ClearOutlined />}
              onClick={() => {
                dispatch(props.type?props.actionFunk(props.type):props.actionFunk())
                
              }}
              
            >
              {"Filtreyi Sıfırla"}
            </MazakaButton>
    </> );
}
 
export default MazakaClearFilterButton;