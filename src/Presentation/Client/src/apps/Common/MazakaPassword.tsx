import { Col, Form, Input } from "antd";
import { FC } from "react";
import { MazakaInputProps } from "models/Client/MazakaInputModel";

export const MazakaPassword: FC<MazakaInputProps> = (props) => {
  return (
    <Col
      span={props.span ?? 24}
      xl={props.xl}
      lg={props.lg}
      xs={props.xs}
      md={props.md}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
      >
        <Input.Password
          className="rounded"
          onBlur={props.onBlur}
          onChange={props.onChange}
          prefix={props?.prefix}
          suffix={props?.suffix}
          disabled={props?.disabled}
          min={props.min}
          max={props.max}
          type={props?.type}
          
          placeholder={`${props.placeholder ?? "••••••••"}`}
        />
      </Form.Item>
    </Col>
  );
};
