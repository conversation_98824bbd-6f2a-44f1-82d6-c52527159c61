import { Col, Form, Input, InputNumber } from "antd";
import { MazakaInputProps } from "models/Client/MazakaInputModel";
import { FC } from "react";

const MazakaInputNumber: FC<MazakaInputProps> = (props) => {
  const valueType: any = props.value
  return (<>

    <Col
      className={props.colClassName}
      span={props.span}
      xs={props.xs}
      sm={props.sm}
      md={props.md}
      lg={props.lg}
      xl={props.xl}
      xxl={props.xxl}
    >
      <Form.Item
        className={props.className}
        name={props.name}
        initialValue={props.initialValue}
        rules={props.rules}
        label={props.label}
        colon={props.colon}
        labelAlign={props.labelAlign}
        labelCol={props.labelCol}
        wrapperCol={props.wrapperCol}
        hasFeedback={props.hasFeedback}
        tooltip={props.tooltip}
        noStyle={props.noStyle}
        help={props.help}
        validateStatus={props.validateStatus}

      >
        <InputNumber
          className={`rounded !w-full ${props.inputClassName}`}
          onBlur={props.onBlur}
          onChange={props.onInputNumberChange}
          formatter={(value) => {
            if (!value) return "";
            return value.toString();
          }}
          parser={(value) => {
            if (!value) return "";

            let cleanValue = value.replace(",", ".");

            const parts = cleanValue.split(".");
            if (parts.length > 2) {
              const lastPart = parts.pop();
              const firstParts = parts.join("");
              cleanValue = firstParts + "." + lastPart;
            }
            return cleanValue;
          }}
          prefix={props?.prefix}
          suffix={props?.suffix}
          pattern={props.pattern}
          disabled={props?.disabled}
          min={props.min}
          max={props.max}
          step={props.step}
          bordered={props.bordered}
          size={props.size}
          type={props?.type}
          placeholder={props.placeholder}
          addonAfter={props.addonAfter}
          addonBefore={props.addonBefore}
          value={valueType}
        />
      </Form.Item>
    </Col>
  </>);
}

export default MazakaInputNumber;