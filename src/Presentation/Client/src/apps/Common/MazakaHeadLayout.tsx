import { Layout, Typography } from "antd";
import Title from "antd/lib/typography/Title";
import { Content } from "antd/lib/layout/layout";
import { LeftOutlined } from "@ant-design/icons";
import { MazakaHeadLayoutProps } from "models/Client/MazakaHeadLayoutProps";
import { FC } from "react";

import { MazakaButton } from "apps/Common/MazakaButton";

const { Text } = Typography;

const LayoutStyle = {
  wide: "w-full rounded pt-10 px-8",
  wideBackgroundNone: "w-full rounded pt-10",
  strecth: "w-full lg:!w-[1000px] rounded pt-10",
  strecthBackgroundNone: "w-full md:!w-3/4 lg:!w-1/2 pt-10",
};

export const MazakaHeadLayout: FC<MazakaHeadLayoutProps> = (props) => {

  const handleOnClose = () => {
    props.toggleVisible();
  };

  return (
    <Layout hasSider={props.hasSider} className={"items-center"}>
      <Content className={LayoutStyle[props.layoutType || "wide"]}>
        <Title
          style={{ marginBottom: "0px" }}
          underline={props.underline}
          type={props.type}
          code={props.code}
          mark={props.mark}
          level={props.Tlevel ?? 1}
        >
          {props.title}
        </Title>
        <Text className="block text-gray-300" copyable={props.copyable}>
          {props.text || props.description}
        </Text>
        <MazakaButton
          onClick={handleOnClose}
          className="!pl-0 !border-0 !text-gray-700 hover:!text-gray-500"
          icon={<LeftOutlined />}
          type="link"
        >
          {"Geriye Dön "}
        </MazakaButton>
      </Content>
    </Layout>

  );
};
