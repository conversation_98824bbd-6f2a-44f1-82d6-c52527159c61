

import { FC, useEffect, } from "react";
import { GeneralSelectInputs } from "models/Client/GeneralSelectInputs";
import { MazakaSelect } from "./MazakaSelect";
import { useGetStations } from "apps/Station/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { normalizeString } from "helpers/TRNormalizedName";


const GeneralStationInput: FC<GeneralSelectInputs> = (props) => {
  const stations= useGetStations({PageSize:-1})
  useEffect(() => {
    if ( stations.isError) {
      showServiceErrorMessage( stations.error, {}, "GeneralstationsInput", true);
    }
  }, [ stations.isError]);

  
  return (
    <>
      <MazakaSelect
        name={props.name}
        xs={props.xs}
        sm={props.sm}
        md={props.md}
        lg={props.lg}
        xl={props.xl}
        loading={stations.isLoading || stations.isFetching}
        disabled={stations.isLoading || stations.isFetching || props.disabled}
        className={props.className}
        showSearch={true}
        allowClear={props.allowClear || false}
        filterOption={(input: any, option: any)=>{
          const normalizedInput = normalizeString(input.toLowerCase().trim());
          const normalizedLabel = normalizeString(option.label.toLowerCase().trim());
          const status =  (normalizedLabel ?? "").includes(normalizedInput.toLowerCase())
          return status
        }}
        label={props.label}
        options={
          stations.data
            ? stations.data.Data.map((item: any) => {
                return {
                  key: item.Id,
                  value: item.Id,
                  label: item.Name,
                 
                };
              })
            : []
        }
        placeholder={props.placeholder}
        mode={props.mode}
        onChange={props.onChange}
        value={props.value}
        rules={props.rules}
      />
    </>
  );
};

export default GeneralStationInput;
