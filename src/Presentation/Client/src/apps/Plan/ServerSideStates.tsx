import { useQuery } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { getConcreteConsistencyClasses, getConcreteLocations, getConcreteOptions, getContractProductTransaction, getDocumentNoListFilter, getLaboratoryResultListFilter, getTransactionRequestListFilter, getVehicleLocationLiveTracking } from "./Services";



export const useGetTransactionRequests = (filter: any) => {
  const query = useQuery(
    [endpoints.getTransactionRequestListFilter, filter],
    () => {
      return getTransactionRequestListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetLaboratoryResults = (filter: any) => {
  const query = useQuery(
    [endpoints.getLaboratoryResultListFilter, filter],
    () => {
      return getLaboratoryResultListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetDocumentNoes = (filter: any) => {
  const query = useQuery(
    [endpoints.getDocumentNoList, filter],
    () => {
      return getDocumentNoListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetContractProductTransactions = (filter: any) => {
  const query = useQuery(
    [endpoints.getContractProductTransaction, filter],
    () => {
      return getContractProductTransaction(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      // refetchInterval: 10000
    }
  );

  return query;
};


export const useGetConcereteLocations = () => {
  const query = useQuery(
    [endpoints.getConcreteLocationList],
    () => {
      return getConcreteLocations();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetVehicleLocationLiveTracking= (plate:string) => {
  const query = useQuery(
    [endpoints.getVehicleLocationTracking,plate],
    () => {
      return getVehicleLocationLiveTracking(plate);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchInterval:15000
    }
  );

  return query;
};

export const useGetConcereteOptions = () => {
  const query = useQuery(
    [endpoints.getConcreteOptionList],
    () => {
      return getConcreteOptions();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetConcereteConsistencyClasses= () => {
  const query = useQuery(
    [endpoints.getConcreteConsistencyClassList],
    () => {
      return getConcreteConsistencyClasses();
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
