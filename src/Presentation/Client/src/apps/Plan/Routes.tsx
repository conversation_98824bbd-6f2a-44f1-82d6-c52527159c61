import { Route } from "react-router-dom";
import DiscoveryIndex from "./Pages/Discovery/DiscoveryIndex";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";

const WaitApprovedIndex = lazy(() => import('./Pages/WaitApproved/WaitApproved'))
const ReturningIndex = lazy(() => import('./Pages/Return/ReturnIndex'))
const PlanIndex = lazy(() => import('./Pages/Operation/PlanIndex'))
const  AllIndex = lazy(() => import('./Pages/All/AllIndex'))




export const transactionRouteList = [
  <Route key={"transactionRouteList"}>
    <Route
      path={"/tranascion/without/approved/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <WaitApprovedIndex/>
        </Suspense>
      }
    />
     <Route
      path={"/tranascion/returning/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <ReturningIndex/>
        </Suspense>
      }
    />
     <Route
      path={"/plan/transaction/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <PlanIndex/>
        </Suspense>
      }
    />
     <Route
      path={"/plan/all/transaction/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <AllIndex/>
        </Suspense>
      }
    />
     <Route
      path={"/plan/discovery/list"}
      element={
        <Suspense fallback={<Spin/>}>
          <DiscoveryIndex/>
        </Suspense>
      }
    />
  </Route>,
];
