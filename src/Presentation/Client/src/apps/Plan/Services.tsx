import {
  deleteRequest,
  get,
  patch,
  post,
  put,
} from "services/BaseClient/Client";
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Plan/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";

export const getTransactionRequestListFilter = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getTransactionRequestListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const getDocumentNoListFilter = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getDocumentNoList}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const getLaboratoryResultListFilter = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getLaboratoryResultListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getContractProductTransactionLogListFilter = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getTransactionRequestLogListFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getConcreteLocations = async (): Promise<DataResponse<any[]>> => {
  const url = `${endpoints.getConcreteLocationList}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getContractProductTransaction = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getContractProductTransaction}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const getConcreteOptions = async (): Promise<DataResponse<any[]>> => {
  const url = `${endpoints.getConcreteOptionList}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getWeatherInfoes = async (
  filter: any
): Promise<DataResponse<any[]>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getWeatherStatus}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const getConcreteConsistencyClasses = async (): Promise<
  DataResponse<any[]>
> => {
  const url = `${endpoints.getConcreteConsistencyClassList}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};
export const getTransactionDetails = async (
  id: string
): Promise<DataResponse<any[]>> => {
  const url = `${endpoints.getTransactionRequestDetails}/${id}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getContractProductTransactionDetails = async (
  id: string
): Promise<DataResponse<any[]>> => {
  const url = `${endpoints.getContractProductTransactionDetails}/Find/${id}?IncludeProperties=TransactionRequest.Product&IncludeProperties=Status&IncludeProperties=Vehicle.Driver&IncludeProperties=TransactionRequest`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getVehicleLocationLiveTracking = async (
  plate: string
): Promise<DataResponse<any[]>> => {
  const url = `${endpoints.getVehicleLocationTracking}?VehiclePlate=${plate}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const addTransaction = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.addTransaction}`;
  const config = headers;
  return post<DataResponse<any>>(url, data, config);
};


export const transferERPTransaction = async (): Promise<DataResponse<any>> => {
  const url = `${endpoints.transferERPOrder}`;
  const config = headers;
  return post<DataResponse<any>>(url, {}, config);
};
export const addLaboratoryResult = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.addLaboratoryResult}`;
  const config = headers.content_type.multipart_form_data;
  return post<DataResponse<any>>(url, data, config);
};
export const approveTransactionWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.approveTransaction}/${data?.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const cancelTransactionWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.cancelTransaction}/${data?.Id}/${data?.CanceledNote}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const cancelContractProductTransactionWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.cancelContractProductTransaction}/${data?.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};

export const refundedContractProductTransactionWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.cancelContractProductTransaction}/${data?.Id}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const rejectTransactionWithPut = async (
  transactionId: string,
  note: string
): Promise<DataResponse<any>> => {
  const url = `${endpoints.rejectTransaction}/${transactionId}/${note}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, {}, config);
};

export const addContractProductTransaction = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.addContractProductTransaction}/InsertWithDocumentNo/${data["TransactionRequestId"]}/${data["DocumentNo"]}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};
export const addContractProductTransactionPost = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.addContractProductTransaction}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};
export const putTransaction = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.addTransaction}`;
  const config = headers;
  return put<DataResponse<any>>(url, data, config);
};
export const validateDocumentNumber = async (
  data: string | number
): Promise<DataResponse<any>> => {
  const url = `${endpoints.validateDocumentNo}/${data}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const updateTransactionWithPut = async (
  data: any,
  type = "json"
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateTransactionWithPut}`;
  const config =
    type === "json"
      ? headers.content_type.application_json
      : headers.content_type.multipart_form_data;
  return put<DataResponse<any>>(url, data, config);
};
export const addabResult = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.addLaboratoryResult}`;
  const config = headers.content_type.multipart_form_data;
  return post<DataResponse<any>>(url, data, config);
};
export const deleteCertificate = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updateTransactionWithPut}/${data.Id}/Certificate`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, config, data);
};
export const deleteLaboratoryResult = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteLaboratoryResult}/${data.Id}`;
  const config = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, config, data);
};
export const updateTransactionWithPatch = async (
  data: any
): Promise<DataResponse<PatchRequest>> => {
  const url = `${endpoints.updateTransactionWithPatch}`;
  const config = headers.content_type.application_json;
  return patch<DataResponse<any>>(url, data, config);
};
export const updateContractProductTransactionWithPatch = async (
  data: any
): Promise<DataResponse<PatchRequest>> => {
  const url = `${endpoints.updateContractProductTransactionWithPatch}`;
  const config = headers.content_type.application_json;
  return patch<DataResponse<any>>(url, data, config);
};
