import { createSlice } from "@reduxjs/toolkit";
import dayjs from "dayjs";

const InitialState: any = {
  waitApprovedfilter: {
    PageIndex: 1,
    PageSize: 20,
    TransactionrequestTypeId: 1,
    StatusIds: [1],
    IncludeProperties: [
      "Building.Company",
      "TransactionRequestPompType.PompType",
      "ConsistencyClass",
      "CarType",
      "Status",
      "Product",
      "RequestedPerson",
      "ConcreteLocation",
      "TransactionRequestConcreteOption.ConcreteOption",
      "ConsistencyClass",
      "Building.BuildingUser.User",
    ],
  },
  contractProductTransactionFilter: {
    PageIndex: 1,
    PageSize: 20,
    TypeId: 2,
    StatusId: 5,
    IncludeProperties: [
      "Contract.Company",
      "Vehicle",
      "Station",
      "Status",
      "Type",
      "Station",
    ],
  },
  operationTransactionFilter: {
    PageIndex: 1,
    PageSize: 7,
    StartDesiredTime: dayjs().format("YYYY-MM-DD"),
    EndDesiredTime: dayjs().format("YYYY-MM-DD"),
    StatusIds: [2],

    IncludeProperties: [
      "Building.Company",
      "TransactionRequestPompType.PompType",
      "ConsistencyClass",
      "CarType",
      "Status",
      "Product",
      "RequestedPerson",
      "ConcreteLocation",
      "TransactionRequestConcreteOption.ConcreteOption",
      "ConsistencyClass",
      "Building.BuildingUser.User",
    ],
  },
  operationViewMode: "calendar",
  calendarFilter: {
    PageSize: -1,
    StatusIds: [3, 4, 5, 6],
    ApprovedStartDate: dayjs().startOf("month").format("YYYY-MM-DD"),
    ApprovedEndDate: dayjs().endOf("month").format("YYYY-MM-DD"),
    IncludeProperties: [
      "Building.Company",
      "TransactionRequestPompType.PompType",
      "ConsistencyClass",
      "CarType",
      "Status",
      "Product",
      "Station",
      "ConcreteLocation",
      "TransactionRequestConcreteOption.ConcreteOption",
      "ConsistencyClass",
      // "Building.AuditPerson",
    ],
  },
  discoveryFilter: {
    PageIndex: 1,
    PageSize: 20,
    TransactionrequestTypeId: 2,
    StatusIds: [1, 6, 7],

    IncludeProperties: [
      "Building.Company",
      "TransactionRequestPompType.PompType",
      "ConsistencyClass",
      "CarType",
      "Status",
      "Product",
      "RequestedPerson",
      "ConcreteLocation",
      "TransactionRequestConcreteOption.ConcreteOption",
      "ConsistencyClass",
      "Building.BuildingUser.User",
    ],
  },

  allFilter: {
    PageIndex: 1,
    PageSize: 20,
    IncludeProperties: [
      "Building.Company",
      "TransactionRequestPompType.PompType",
      "ConsistencyClass",
      "CarType",
      "Status",
      "Product",
      "RequestedPerson",
      "ConcreteLocation",
      "TransactionRequestConcreteOption.ConcreteOption",
      "ConsistencyClass",
      "Building.BuildingUser.User",
    ],
  },
  isShowedCalanderHistories: false,
};

const transactionSlice = createSlice({
  name: "transactionSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetTranasactionFilter: (state, action) => {
      let data = action.payload;
      switch (data.type) {
        case "waitApprovedfilter":
          state.waitApprovedfilter = data.filter;
          break;
        case "contractProductTransactionFilter":
          state.contractProductTransactionFilter = data.filter;
          break;
        case "operationTransactionFilter":
          state.operationTransactionFilter = data.filter;
          break;
        case "calendarFilter":
          state.calendarFilter = data.filter;
          break;
        case "discoveryFilter":
          state.discoveryFilter = data.filter;
          break;
        case "allFilter":
          state.allFilter = data.filter;
          break;
      }
    },

    handleSetOperationView: (state, action) => {
      state.operationViewMode = action.payload.mode;
    },
    handleSetCalanderHistories: (state, action) => {
      state.isShowedCalanderHistories = action.payload.status;
    },

    handleResetAllFieldsTransaction: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetFilterTrasaction: (state, action) => {
      let type = action.payload;
      switch (type) {
        case "waitApprovedfilter":
          state.waitApprovedfilter = {
            PageIndex: 1,
            PageSize: 20,
            TransactionrequestTypeId: 1,
            StatusIds: [1],
            IncludeProperties: [
              "Building.Company",
              "TransactionRequestPompType.PompType",
              "ConsistencyClass",
              "CarType",
              "Status",
              "Product",
              "RequestedPerson",
              "ConcreteLocation",
              "TransactionRequestConcreteOption.ConcreteOption",
              "ConsistencyClass",
              "Building.BuildingUser.User",
            ],
          };
          break;
        case "contractProductTransactionFilter":
          state.contractProductTransactionFilter = {
            PageIndex: 1,
            PageSize: 20,
            TypeId: 2,
            StatusId: 5,
            IncludeProperties: [
              "Contract.Company",
              "Vehicle",
              "Status",
              "Station",
              "Type",
              "Station",
            ],
          };
          break;
        case "operationTransactionFilter":
          state.operationTransactionFilter = {
            PageIndex: 1,
            PageSize: 7,
            StartDesiredTime: dayjs().format("YYYY-MM-DD"),
            EndDesiredTime: dayjs().format("YYYY-MM-DD"),
            StatusIds: [2],

            IncludeProperties: [
              "Building.Company",
              "TransactionRequestPompType.PompType",
              "ConsistencyClass",
              "CarType",
              "Status",
              "Product",
              "RequestedPerson",
              "ConcreteLocation",
              "TransactionRequestConcreteOption.ConcreteOption",
              "ConsistencyClass",
              "Building.BuildingUser.User",
            ],
          };
          break;

        case "calendarFilter":
          state.calendarFilter = {
            PageSize: -1,
            StatusIds: [3, 4, 5, 6],
            ApprovedStartDate: dayjs().startOf("month").format("YYYY-MM-DD"),
            ApprovedEndDate: dayjs().endOf("month").format("YYYY-MM-DD"),
            IncludeProperties: [
              "Building.Company",
              "TransactionRequestPompType.PompType",
              "ConsistencyClass",
              "CarType",
              "Status",
              "Product",
              "Station",
              "ConcreteLocation",
              "TransactionRequestConcreteOption.ConcreteOption",
              "ConsistencyClass",
              // "Building.AuditPerson",
            ],
          };
          break;

        case "discoveryFilter":
          state.discoveryFilter = {
            PageIndex: 1,
            PageSize: 20,
            TransactionrequestTypeId: 2,
            StatusIds: [1, 6, 7],

            IncludeProperties: [
              "Building.Company",
              "TransactionRequestPompType.PompType",
              "ConsistencyClass",
              "CarType",
              "Status",
              "Product",
              "RequestedPerson",
              "ConcreteLocation",
              "TransactionRequestConcreteOption.ConcreteOption",
              "ConsistencyClass",
              "Building.BuildingUser.User",
            ],
          };
          break;

        case "allFilter":
          state.allFilter = {
            PageIndex: 1,
            PageSize: 20,
            IncludeProperties: [
              "Building.Company",
              "TransactionRequestPompType.PompType",
              "ConsistencyClass",
              "CarType",
              "Status",
              "Product",
              "RequestedPerson",
              "ConcreteLocation",
              "TransactionRequestConcreteOption.ConcreteOption",
              "ConsistencyClass",
              "Building.BuildingUser.User",
            ],
          };
          break;
      }
    },
  },
});

export const {
  handleResetAllFieldsTransaction,
  handleResetFilterTrasaction,
  hanldleSetTranasactionFilter,
  handleSetOperationView,
  handleSetCalanderHistories,
} = transactionSlice.actions;
export default transactionSlice;
