
const baseTransactionUrl = "TransactionRequest"
const baseContractProductTransactionUrl = "ContractProductTransaction"
const baseContractProductTransactionLogUrl = "ContractProductTransactionLog"
const baseWeatherUrl = "Weather"
const baseLaboratoryResult = "LabResult"
const baseAccountingUrl = "Accounting"
  const endpoints = {
    getTransactionRequestListFilter:`${baseTransactionUrl}/Filter`,
    getTransactionRequestDetails:`${baseTransactionUrl}/find`,
    getTransactionRequestLogListFilter:`${baseContractProductTransactionLogUrl}/Filter`,
    getWeatherStatus:`${baseWeatherUrl}`,
    getDocumentNoList:`${baseAccountingUrl}/DocumentFilter`,
    getVehicleLocationTracking:`LiveTracking`,
    getContractProductTransaction:`${baseContractProductTransactionUrl}/Filter`,
    getContractProductTransactionDetails:`${baseContractProductTransactionUrl}`,
    getConcreteLocationList:`${baseTransactionUrl}/ConcreteLocation`,
    getConcreteOptionList:`${baseTransactionUrl}/ConcreteOption`,
    getConcreteConsistencyClassList:`${baseTransactionUrl}/ConsistencyClass`,
    addTransaction:`${ baseTransactionUrl}`,
    transferERPOrder:`${ baseTransactionUrl}/TransferOrderByErp`,
    approveTransaction:`${ baseTransactionUrl}/Approve`,
    cancelTransaction:`${ baseTransactionUrl}/Cancel`,
    cancelContractProductTransaction:`${ baseTransactionUrl}/Cancel`,
    refundedContractProductTransaction:`${ baseTransactionUrl}/Refunded`,
    rejectTransaction:`${ baseTransactionUrl}/Reject`,
    updateTransactionWithPut:`${baseTransactionUrl}`,
    updateTransactionWithPatch:`${baseTransactionUrl}`,
    updateContractProductTransactionWithPatch:`${baseContractProductTransactionUrl}`,
    validateDocumentNo:`${baseAccountingUrl}/DocumentCheck`,
    addContractProductTransaction:`${baseContractProductTransactionUrl}`,
    getLaboratoryResultListFilter:`${baseLaboratoryResult}/Filter`,
    addLaboratoryResult:`${ baseLaboratoryResult}`,
    deleteLaboratoryResult:`${ baseLaboratoryResult}`,

  
  };
  
  export default endpoints;
  