import { Col, Row } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";

import TransactionRequestTableList from "./Components/ReturinginContractProductTransactioin";
import TopFilters from "./Components/TopFilters";

const ReturningIndex = () => {
  return (
    <>
      <MazakaLayout
        title={"İptal ve İade Listesi"}
        headDescription={
          "İptal ve İade sayfası, sistemde İade ve iptal talabınde bulunan  isteklerin listesini görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]} className="!mt-4">
          <Col xs={24}>
            <TopFilters />
          </Col>
          <Col span={24}>
            <TransactionRequestTableList />
          </Col>
        </Row>
      </MazakaLayout>
    </>
  );
};

export default ReturningIndex;
