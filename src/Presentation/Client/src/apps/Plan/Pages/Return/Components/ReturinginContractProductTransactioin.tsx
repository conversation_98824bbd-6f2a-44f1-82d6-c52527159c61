
  import {  Col, Table, Tag, } from "antd";
  import { useDispatch, useSelector } from "react-redux";
  import { RootState } from "store/Reducers";
  import { useGetContractProductTransactions,} from "apps/Plan/ServerSideStates";
  import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";

  
  const TransactionRequestTableList = () => {
    const {  contractProductTransactionFilter: filter } = useSelector(
      (state: RootState) => state.transactions
    );
    const dispatch = useDispatch();

  
  
    const handleChangePagination = (pageNum: number, pageSize: number) => {
      let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
      dispatch(hanldleSetTranasactionFilter({ filter: newFilter,type:"contractProductTransactionFilter" }));
    };
  
    const contractProductTranastions = useGetContractProductTransactions(filter);
  
    const columns = [
        {
            title:"İstasyon",
            dataIndex:["Station","Name"],
            key:"StationName",
            sorter: (a: any, b: any) =>
              a?.Station?.Name.localeCompare(b?.Station?.Name),
        },
        {
            title:"Araç",
            dataIndex:["Vehicle","Name"],
            key:"VehicleName",
            sorter: (a: any, b: any) =>
              a?.Vehicle?.Name.localeCompare(b?.Vehicle?.Name),
        },
        {
            title:"Firma",
            dataIndex:["Contract","Company","Name"],
            key:"CompanyName",
            sorter: (a: any, b: any) =>
              a?.Contract?.Company?.Name.localeCompare(b?.Contract?.Company?.Name),
        },
        {
            title:"Sözleşme",
            dataIndex:["Contract","Title"],
            key:"ContractName",
            sorter: (a: any, b: any) =>
              a?.Contract?.Title.localeCompare(b?.Contract?.Title),
        },
       
        {
            title:"İrsaliyeNo",
            dataIndex:"DocumentNo",
            key:"DocumentNo"
        },
        {
          title: "İptal Eden",
          dataIndex: "CanceledUser",
          key: "CanceledUserName",
          sorter: (a: any, b: any) => {
            const nameA = a?.CanceledUser?.Name || "";
            const nameB = b?.CanceledUser?.Name || "";
            return nameA.localeCompare(nameB);
          },
          render: (user: any) => {
            if (user && (user.Name || user.Surname)) {
              return `${user.Name || ""} ${user.Surname || ""}`.trim();
            }
          }
        },
        {
          title:"İptal Notu",
          dataIndex:"CanceledNote",
          key:"CanceledNote",
          sorter: (a: any, b: any) =>
              a?.CanceledNote?.localeCompare(b?.CanceledNote),
        },
        {
            title:"Miktar(m³)",
            dataIndex:"SendingAmount",
            key:"SendingAmount",
            sorter: (a: any, b: any) =>
              a?.SendingAmount - b?.SendingAmount,
        },
        {
            title:"Tip",
            dataIndex:["Type","Name"],
            render:(value:string,record:any)=>{
                return(
                    <>
                    <Tag color="error" >{record?.TypeId===2?record?.Type?.Name:record?.Status?.Name}</Tag>
                    
                    </>
                )
            }
           
        },
        


     
    ];
  
    return (
      <Col span={24}>
        <Table
          columns={columns}
          loading={
            contractProductTranastions.isLoading ||
            contractProductTranastions.isFetching
          }
          dataSource={
            contractProductTranastions.data
              ? contractProductTranastions.data.Data
              : []
          }
          rowKey={"Id"}
          scroll={{ x: 700 }}
          pagination={{
            position: ["bottomRight"],
            className: "!px-0",
            onChange: handleChangePagination,
            total: contractProductTranastions.data?.FilteredCount || 0,
            current: contractProductTranastions.data?.PageIndex,
            pageSize: contractProductTranastions.data?.PageSize,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
        />
      
      </Col>
    );
  };
  
  export default TransactionRequestTableList;
  