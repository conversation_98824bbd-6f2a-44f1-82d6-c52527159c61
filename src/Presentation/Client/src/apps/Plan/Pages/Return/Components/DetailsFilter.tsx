import { Col, Form, Row } from "antd";
import GeneralProductInput from "apps/Common/GeneralProductInput";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaRangePicker } from "apps/Common/MazakaRangePicker";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import dayjs from "dayjs";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form, {
    submitText: "Filtrele",
  });
  const { contractProductTransactionFilter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    let currentFilter = { ...contractProductTransactionFilter };

    for (let key in formValues) {
      if (
        !formValues[key] ||
        (Array.isArray(formValues[key]) && formValues[key].length === 0)
      ) {
        delete formValues[key];
        delete currentFilter[key];
      }
    }
    if (formValues?.Date && formValues["Date"][0] && formValues["Date"][1]) {
      formValues["StartDate"] = dayjs(formValues["Date"][0]).format(
        "YYYY-MM-DD"
      );
      formValues["EndDate"] = dayjs(formValues["Date"][1]).format("YYYY-MM-DD");
      delete formValues["Date"];
    }
    const newFilter = { ...currentFilter, ...formValues };

    await dispatch(
      hanldleSetTranasactionFilter({
        filter: newFilter,
        type: "contractProductTransactionFilter",
      })
    );
    mazakaForm.setSuccess(1000, () => {}, "Başarılı");
    onFinish();
  };

  useEffect(() => {
    const startDate = contractProductTransactionFilter["StartDate"]
      ? dayjs(contractProductTransactionFilter["StartDate"])
      : null;
    const endDate = contractProductTransactionFilter["EndDate"]
      ? dayjs(contractProductTransactionFilter["EndDate"])
      : null;
    const dateRange = startDate && endDate ? [startDate, endDate] : [];
    form.setFieldsValue({
      ProductId: contractProductTransactionFilter?.ProductId || undefined,
      Date: dateRange.length > 0 ? dateRange : undefined,
    });
  }, [contractProductTransactionFilter]);
  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} {...formActions}>
          <Row gutter={[0, 10]}>
            <MazakaInput
             
              xs={24}
              label="İrsaliye Numarası"
              placeholder="İrsaliye Numarası"
              name={"DocumentNo"}
              className="!m-0"
            />

            <GeneralProductInput
              name={"ProductId"}
              label={"Ürün"}
              placeholder={"Ürün"}
              className="!m-0"
              xs={24}
              allowClear={true}
            />
            <MazakaRangePicker
              name={"Date"}
              label="Tarih Aralığı"
              xs={24}
              className="!m-0"
            />

          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
