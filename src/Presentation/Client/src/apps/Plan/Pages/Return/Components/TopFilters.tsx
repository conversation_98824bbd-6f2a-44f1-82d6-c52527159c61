import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import {
  handleResetFilterTrasaction,
  hanldleSetTranasactionFilter,
} from "apps/Plan/ClientSideStates";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { Col, Drawer, Form, Row } from "antd";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import GeneralStationInput from "apps/Common/GeneralStationInput";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { useEffect, useState } from "react";
import DetailsFilter from "./DetailsFilter";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";

const TopFilters = () => {
  const { contractProductTransactionFilter: filter } = useSelector(
    (state: RootState) => state.transactions
  );
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  const handleOnChangeType = (value: number) => {
    let currentFilter = { ...filter };
    if (value === 1) {
      currentFilter["StatusId"] = 5;
      delete currentFilter["TypeId"];
    } else if (value === 2) {
      currentFilter["TypeId"] = 2;
      delete currentFilter["StatusId"];
    } else {
      currentFilter["TypeId"] = 2;
      currentFilter["StatusId"] = 5;
    }

    dispatch(
      hanldleSetTranasactionFilter({
        filter: currentFilter,
        type: "contractProductTransactionFilter",
      })
    );
  };

  const handleOnChangeCompany = (value: string[]) => {
    let currentFilter = { ...filter };
    if (value) {
      currentFilter["CompanyId"] = value;
    } else {
      delete currentFilter["CompanyId"];
    }
    dispatch(
      hanldleSetTranasactionFilter({
        filter: currentFilter,
        type: "contractProductTransactionFilter",
      })
    );
  };

  const handleOnChangeBuilding = (value: string[]) => {
    let currentFilter = { ...filter };
    if (value) {
      currentFilter["BuildingId"] = value;
    } else {
      delete currentFilter["BuildingId"];
    }
    dispatch(
      hanldleSetTranasactionFilter({
        filter: currentFilter,
        type: "contractProductTransactionFilter",
      })
    );
  };

  const handleOnChangeStation = (value: string[]) => {
    let currentFilter = { ...filter };
    if (value) {
      currentFilter["StationId"] = value;
    } else {
      delete currentFilter["StationId"];
    }
    dispatch(
      hanldleSetTranasactionFilter({
        filter: currentFilter,
        type: "contractProductTransactionFilter",
      })
    );
  };

  useEffect(() => {
    form.setFieldsValue({
      StationId: filter?.StationId,
      CompanyId: filter?.CompanyId,
      BuildingId: filter?.BuildingId,
      Type:filter?.TypeId&&filter?.StatusId?undefined:filter?.StatusId&&!filter.TypeId?1:2
    });
  }, [filter]);

  

  return (
    <Row>
      <Col xs={24} xl={18} className="">
        <Form form={form}>
          <Row gutter={[10, 10]}>
            <MazakaSelect
              placeholder={"Tip"}
              name="Type"
              xs={24}
              md={12}
              xl={5}
              options={[
                { label: "İptal", value: 1 },
                { label: "İade", value: 2 },
              ]}
              onChange={handleOnChangeType}
              allowClear
            />
            <GeneralStationInput
              name="StationId"
              placeholder="İstasyon"
              className="!m-0"
              xs={24}
              md={12}
              xl={5}
              allowClear={true}
              onChange={handleOnChangeStation}
            />
            <GeneralCompanies
              name="CompanyId"
              placeholder="Firma"
              className="!m-0"
              xs={24}
              md={12}
              xl={5}
              allowClear={true}
              onChange={handleOnChangeCompany}
            />
            <GeneralBuildingInput
              name="BuildingId"
              placeholder="Şantiye"
              className="!m-0"
              xs={24}
              md={12}
              xl={5}
              allowClear={true}
              onChange={handleOnChangeBuilding}
            />
          </Row>
        </Form>
      </Col>
      <Col xs={24} xl={6} className="">
        <div className="!flex items-center gap-1 justify-end">
          <MazakaDetailsFilterButton
            setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
          />
          {(Object.entries(filter).length > 5 ||
            ((Object.entries(filter).length === 4 ||Object.entries(filter).length === 5 )&&
              (!filter?.StatusId || !filter?.TypeId))) && (
            <MazakaClearFilterButton
              type="contractProductTransactionFilter"
              actionFunk={handleResetFilterTrasaction}
            />
          )}
        </div>
      </Col>

      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
        <>
          <DetailsFilter
            onFinish={() => {
              setIsShowFilterDetailsDrawer(false);
            }}
          />
        </>
      </Drawer>
    </Row>
  );
};

export default TopFilters;
