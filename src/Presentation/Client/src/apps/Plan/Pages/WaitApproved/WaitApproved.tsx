import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";
import { useQueryClient } from "react-query";
import TopFilterOptions from "./Components/TopFilterOptions";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { handleResetFilterTrasaction } from "apps/Plan/ClientSideStates";
import TransactionRequestTableList from "./Components/TransactionRequestTableList";
import endpoints from "apps/Plan/EndPoints";
import ModeTabs from "./Components/Tabs";
const DetailsFilter = lazy(
  () => import("./Components/DetailsFilter")
);
const AddOrUpdateRequest = lazy(
  () => import("./Components/AddOrUpdateRequest")
);

const WaitApprovedIndex = () => {
  const { Text } = Typography;
  const queryClient = useQueryClient();
  const [isShowAddTransactionDrawer, setisShowAddTransactionDrawer] = useState(false);
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
  const { waitApprovedfilter } = useSelector(
    (state: RootState) => state.transactions
  );

  return (
    <>
      <MazakaLayout
        title={"Onay Bekleyen İstekler(Satış)"}
        headDescription={
          "Onay bekleyen istek sayfası, sistemde kayıtlı tüm onaylanmamış ,sözleşmesiz ve reddeilmiş istekleri görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]} className="!mt-4">
          <Col span={24} >
            <ModeTabs />
          </Col>
          <Col span={24}>
            <Row gutter={[10, 10]}>

              <Col xs={24} lg={18}>
                <TopFilterOptions />
              </Col>
              <Col xs={24} lg={6} className="!flex justify-end gap-2">
                <Button
                  onClick={() => {
                    setisShowAddTransactionDrawer(true);
                  }}
                  className="!flex items-center"
                  type="primary"
                >
                  <div className="!flex items-center gap-1">
                    <PlusOutlined />
                    <Text className="!text-white">Talep Oluştur</Text>
                  </div>
                </Button>
                <MazakaDetailsFilterButton
                  setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
                />
                {Object.entries(waitApprovedfilter).length > 6 && (
                  <>
                    <MazakaClearFilterButton
                      type="waitApprovedfilter"
                      actionFunk={handleResetFilterTrasaction}
                    />
                  </>
                )}
              </Col>
            </Row>
          </Col>

          <Col span={24}>
            <TransactionRequestTableList />
          </Col>
        </Row>
      </MazakaLayout>

      <Drawer
        title="Yeni talep oluştur"
        open={isShowAddTransactionDrawer}
        onClose={() => {
          setisShowAddTransactionDrawer(false);
        }}
      >
        <>
          {
            isShowAddTransactionDrawer &&
            <Suspense fallback={<Spin />} >
              <AddOrUpdateRequest
                onFinish={() => {
                  setisShowAddTransactionDrawer(false)
                  queryClient.resetQueries({
                    queryKey: endpoints.getTransactionRequestListFilter,
                    exact: false,
                  });

                }} />

            </Suspense>
          }
        </>
      </Drawer>
      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
        <>
          {
            isShowFilterDetailsDrawer &&
            <Suspense fallback={<Spin />}>

              <DetailsFilter
                onFinish={() => {
                  setIsShowFilterDetailsDrawer(false);
                }}
              />
            </Suspense>
          }
        </>
      </Drawer>
    </>
  );
};

export default WaitApprovedIndex;
