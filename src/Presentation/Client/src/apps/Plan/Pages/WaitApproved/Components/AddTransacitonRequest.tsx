import { Col, Form, Row } from "antd";
import GeneralCarInput from "apps/Common/GeneralCarInput";
import GeneralUserInput from "apps/Common/GeneralUserInput";
import GeneralPompTypeInput from "apps/Common/GenralPompTypeInput";
import { MazakaForm } from "apps/Common/MazakaForm";
import { addContractProductTransactionPost, addTransaction, putTransaction } from "apps/Plan/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";
import { FC } from "react";

interface AddTransactionRequestProps extends GeneralAddOrUpdateFormProps {
  selectedTransactionRequest?: any;
  onTransactionUpdated?: () => void;
}

const AddTransactionRequest: FC<AddTransactionRequestProps> = ({
  onFinish,
  selectedTransactionRequest,
  onTransactionUpdated
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    console.log("selectedTransactionRequest:", selectedTransactionRequest);
    console.log("formValues:", formValues);

    try {
      // İlk API çağrısı: ContractProductTransaction
      const contractProductData = {
        VehicleId: formValues["VehicleId"],
        DriverId: formValues["DriverId"],
        TypeId: 1,
        StatusId: 1,
        TransactionRequestId: selectedTransactionRequest?.Id,
        ContractId: selectedTransactionRequest?.ContractId,
        ProductId: selectedTransactionRequest?.ProductId,
        BuildingId: selectedTransactionRequest?.BuildingId,
        StationId: selectedTransactionRequest?.StationId,
        PompTypeId: formValues["PompTypeId"],
      };


      // İkinci API çağrısı: TransactionRequestPompType (sadece pompa tipi varsa)
      if (formValues["PompTypeId"]) {
        const newPompType = {
          PompTypeId: formValues["PompTypeId"],
          TransactionRequestId: selectedTransactionRequest?.Id,
          Id: crypto.randomUUID()
        };
        const pompData = {
          Id: selectedTransactionRequest.Id,
          BuildingId: selectedTransactionRequest.BuildingId,
          ContractId: selectedTransactionRequest.ContractId,
          ProductId: selectedTransactionRequest.ProductId,
          ConcreteLocationId: selectedTransactionRequest.ConcreteLocationId,
          ConsistencyClassId: selectedTransactionRequest.ConsistencyClassId,
          RequestedPersonId: selectedTransactionRequest.RequestedPersonId,
          ApprovedDateTime: selectedTransactionRequest.ApprovedDateTime,
          ApprovedNote: selectedTransactionRequest.ApprovedNote,
          CarTypeId: selectedTransactionRequest.CarTypeId,
          Note: selectedTransactionRequest.Note,
          DistanceInDestination: selectedTransactionRequest.DistanceInDestination,
          DesiredTotalConcrete: selectedTransactionRequest.DesiredTotalConcrete,
          TransactionrequestTypeId: selectedTransactionRequest.TransactionrequestTypeId,
          StationId: selectedTransactionRequest.StationId,
          TransactionRequestConcreteOption: selectedTransactionRequest.TransactionRequestConcreteOption,
          StatusId: selectedTransactionRequest.StatusId,
          TransactionRequestPompType: [
            ...selectedTransactionRequest.TransactionRequestPompType || [],
            newPompType
          ]
        };

        // await putTransaction(pompData);
      }

      await addContractProductTransactionPost(contractProductData);

      mazakaForm.setSuccess(2000, () => {
        return "İşlem Başarılı";
      });

      onTransactionUpdated && onTransactionUpdated();

      form.resetFields();
      onFinish && onFinish();

    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "Create Transaction");
    }
  };

  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} {...formActions}>
          <Row gutter={[0, 10]}>
            <GeneralPompTypeInput
              name="PompTypeId"
              label={"Pompa Tipi"}
              placeholder="Pompa Tipi"
              className="!m-0"
              rules={[{ required: true, message: "Pompa tipi seçimi zorunludur" }]}
              xs={24}
            />

            <GeneralCarInput
              name="VehicleId"
              label={"Araç"}
              placeholder="Araç"
              className="!m-0"
              rules={[{ required: true, message: "Araç seçimi zorunludur" }]}
              span={24}
              allowClear={true}
            />

            <GeneralUserInput
              name="DriverId"
              label={"Şoför"}
              placeholder="Şoför"
              className="!m-0"
              allowClear
              rules={[{ required: true, message: "Şoför seçimi zorunludur" }]}
              xs={24}
              externalFilter={{
                PageSize: -1,
                RoleId: "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469",
              }}
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddTransactionRequest;