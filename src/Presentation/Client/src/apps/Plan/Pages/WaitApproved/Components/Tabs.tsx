import { Tabs, TabsProps } from "antd";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const ModeTabs = () => {
    const {waitApprovedfilter:filter} = useSelector((state:RootState)=>state.transactions)
    const dispatch = useDispatch()
    const items: TabsProps['items'] = [
        {
          key: 'pending',
          label: '<PERSON><PERSON>',
          children:<></>
          
        },
        {
            key: 'rejected',
            label: 'Reddedilenler',
            children:<></>
            
          },
       
      ];
      const [activeKey,setActiveKey] = useState("pending")


     const handleOnChange = (value:string)=>{
        let currentFilter = {...filter}
        if(value ==="pending")
        {
            currentFilter["StatusIds"] = [1]
        }
        else{
            currentFilter["StatusIds"] = [8]
        }
        setActiveKey(value)
        dispatch(hanldleSetTranasactionFilter({filter:currentFilter,type:"waitApprovedfilter"}))
     }
    return (<>
     <Tabs activeKey={activeKey}  items={items} onChange={handleOnChange} />
    
    </>  );
}
 
export default ModeTabs;