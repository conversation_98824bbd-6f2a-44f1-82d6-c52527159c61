import { Col, Form, Row } from "antd";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import GeneralConcreteLocationsInput from "apps/Common/GeneralConcreteLocations";
import GeneralConcreteOptions from "apps/Common/GeneralConcreteOptionInput";
import GeneralConsistencyInput from "apps/Common/GeneralConsistencyClassInput";
import GeneralPriceTypeInput from "apps/Common/GeneralPriceTypeInput";
import GeneralProductInput from "apps/Common/GeneralProductInput";
import GeneralUserInput from "apps/Common/GeneralUserInput";
import GeneralPompTypeInput from "apps/Common/GenralPompTypeInput";
import { MazakaDatePicker } from "apps/Common/MazakaDatePicker";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import MazakaInputNumber from "apps/Common/MazakaInputNumber";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { addTransaction } from "apps/Plan/Services";
import dayjs from "dayjs";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";
import { FC, useState } from "react";

const AddOrUpdateRequest: FC<GeneralAddOrUpdateFormProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    let time = formValues["Time"];
    let date = dayjs(formValues["Date"]).format("YYYY-MM-DD");
    let dateTime = `${date}T${time}`;
    formValues["TransactionRequestPompType"] = formValues["PompTypeIds"]?.map(
      (pompId: string) => {
        return {
          PompTypeId: pompId,
        };
      }
    );

    formValues["DesiredDateTime"] = dateTime;
    delete formValues["Date"];
    delete formValues["Time"];
    delete formValues["ConsistencyClassIds"]
    delete formValues["PompTypeIds"]

    formValues["StatusId"] = 1;

    const concreteData = formValues["ConcreteOptionIds"].map((id: string) => {
      return {
        // TransactionRequestId:response?.Data.Id,
        ConcreteOptionId: id,
      };
    });
    formValues["TransactionRequestConcreteOption"] = concreteData;
    delete formValues["ConcreteOptionIds"];

    try {
      await addTransaction(formValues);
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "Create Transaction");
    }
  };
  const [concreteTypeSubItems, setConcreteTypeSubItems] = useState([]);
  const [selectedBuildingId, setSelectedBuildingId] = useState<null | string>(
    null
  );

  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} {...formActions}>
          <Row gutter={[0, 10]}>
            <GeneralProductInput
              name={"ProductId"}
              label={"Beton Cinsi"}
              placeholder={"Beton Cinsi"}
              className="!m-0"
              xs={24}
              rules={[{ required: true, message: "" }]}
            />
            <GeneralConcreteOptions
              name={"ConcreteOptionIds"}
              label={"Beton Özelliği"}
              placeholder={"Beton Özelliği"}
              className="!m-0"
              xs={24}
              mode="multiple"
              rules={[{ required: true, message: "" }]}
            />

            <GeneralConcreteLocationsInput
              name="ConcreteLocationId"
              label={"Beton Döküm Yeri"}
              placeholder="Beton Döküm Yeri"
              className="!m-0"
              xs={24}
              rules={[{ required: true, message: "" }]}
              onChange={(valey: string, obj: any) => {
                setConcreteTypeSubItems(obj["subItems"]);
              }}
            />

            <GeneralConsistencyInput
              name={"ConsistencyClassId"}
              label={"Beton Kıvam Sınıfı"}
              placeholder={"Beton Kıvam Sınıfı"}
              className="!m-0"
              xs={24}

              rules={[{ required: true, message: "" }]}
              externalConcreteTypeSubItems={concreteTypeSubItems}
            />
            <GeneralPompTypeInput
              name="PompTypeIds"
              label={"Pompa Tipi"}
              placeholder="Pompa Tipi"
              className="!m-0"
              xs={24}
              mode="multiple"
            />

            <GeneralBuildingInput
              name="BuildingId"
              label={"Şantiye"}
              placeholder="Şantiye"
              className="!m-0"
              xs={24}
              rules={[{ required: true, message: "" }]}
              onChange={(value: string) => {
                setSelectedBuildingId(value);
              }}
            />

            {selectedBuildingId && (
              <>
                <GeneralUserInput
                  name="RequestedPersonId"
                  label="Sorumlu Kişi"
                  placeholder="Sorumlu Kişi"
                  className="!m-0"
                  externalFilter={{
                    PageSize: -1,
                    BuildingId: selectedBuildingId,
                    IncludeProperties: ["BuildingUser"],
                  }}
                  rules={[{ required: true, message: "" }]}
                />
              </>
            )}

            <MazakaDatePicker
              name="Date"
              label={"İstenilen Tarih"}
              className="!m-0"
              xs={24}
              rules={[{ required: true, message: "" }]}
            />
            <MazakaInput
              name={"Time"}
              label={"Zaman"}
              placeholder="Saat:Dakika"
              xs={24}
              className="!m-0"
              rules={[
                { required: true, message: "" },
                {
                  pattern: /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/,
                  message:
                    "Lütfen geçerli bir saat:dakika formatında (01:00 - 23:59 arası) giriniz",
                },
              ]}
            />
            <MazakaInputNumber
              name="DesiredTotalConcrete"
              label={"Miktar"}
              placeholder="Miktar"
              className="!m-0"
              xs={24}
              suffix={"m³"}
              rules={[{ required: true, message: "" }]}
            />
            <MazakaTextArea
              name="Note"
              label={"Açıklama"}
              placeholder="Açıklama"
              xs={24}
              className="!m-0"
            />

            <GeneralPriceTypeInput
              name="PriceTypeId"
              label={"Fiyat Tipi"}
              placeholder="Fiyat Tipi"
              className="!m-0"
              xs={24}
              rules={[{ required: true, message: "" }]}
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateRequest;
