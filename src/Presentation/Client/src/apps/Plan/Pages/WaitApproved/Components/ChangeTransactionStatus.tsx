import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import {
  approveTransactionWithPut,
  rejectTransactionWithPut,
} from "apps/Plan/Services";
import { MazakaButton } from "apps/Common/MazakaButton";
import { openNotificationWithIcon } from "helpers/Notifications";

interface ChangeTransactionStatusProps {
  onFinish?: any;
  selectedRecord?: any;
}

const ChangeTransactionStatus: FC<ChangeTransactionStatusProps> = ({
  onFinish,
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    try {
      if (selectedRecord["SelectedStatus"] === 8) {
        await rejectTransactionWithPut(selectedRecord.Id, formValues["Note"]);
      } else {
        const response: any = await approveTransactionWithPut({
          Id: selectedRecord.Id,
          Note: formValues["Note"] || "",
        });
        if (response?.Value) {
          openNotificationWithIcon("info", response.Message);
        }
      }

      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getTransactionRequestListFilter,
        exact: false,
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ChangeTransactionStatus");
    }
  };

  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 10]}>
            <MazakaTextArea
              name="Note"
              label={"Açıklama"}
              placeholder="Açıklama"
              xs={24}
              className="!m-0"
              rules={[
                {
                  required:
                    selectedRecord["SelectedStatus"] === 8 ? true : false,
                  message: "",
                },
              ]}
            />
            <Col xs={24} className="!flex justify-end">
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                {selectedRecord["SelectedStatus"] === 8
                  ? "Reddet"
                  : "Sözleşmeyi Kontrol Et"}
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ChangeTransactionStatus;
