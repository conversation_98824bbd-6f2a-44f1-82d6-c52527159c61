import { Col, Form, Row } from "antd";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import GeneralContractInput from "apps/Common/GeneralContractInput";
import GeneralProductInput from "apps/Common/GeneralProductInput";
import GeneralStationInput from "apps/Common/GeneralStationInput";
import { MazakaForm } from "apps/Common/MazakaForm";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form,{submitText:"Filtrele"});
  const { waitApprovedfilter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    let currentFilter = { ...waitApprovedfilter };
    
    for (let key in formValues) {
      if (!formValues[key] || (Array.isArray(formValues[key]) && formValues[key].length === 0)) {
        delete formValues[key];   
        delete currentFilter[key];
      }
    }
    const newFilter = { ...currentFilter, ...formValues };
 
    await dispatch(
      hanldleSetTranasactionFilter({
        filter: newFilter,
        type: "waitApprovedfilter",
      })
    );
    mazakaForm.setSuccess(1000, () => {}, "Başarılı");
    onFinish();
  };

 
  useEffect(() => {
    form.setFieldsValue({
      CompanyIds: waitApprovedfilter?.CompanyIds || undefined,
      ContractIds: waitApprovedfilter?.ContractIds || undefined,
      StationIds: waitApprovedfilter?.StationIds || undefined,
      ProductIds: waitApprovedfilter?.ProductIds || undefined,
    });
  }, [waitApprovedfilter]);
  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} {...formActions}>
          <Row gutter={[0, 10]}>
            <GeneralCompanies
              name="CompanyIds"
              label={"Firma"}
              placeholder="Firma"
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />

            <GeneralStationInput
              name="StationIds"
              label={"Santral"}
              placeholder="Santral"
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />
           
            <GeneralProductInput
              name={"ProductIds"}
              label={"Ürün"}
              placeholder={"Ürün"}
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />
            <GeneralContractInput
              name={"ContractIds"}
              label={"Sözleşme"}
              placeholder={"Sözleşme"}
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
