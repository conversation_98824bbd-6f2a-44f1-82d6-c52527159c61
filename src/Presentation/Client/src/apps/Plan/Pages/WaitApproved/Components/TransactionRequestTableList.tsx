import {
  <PERSON><PERSON>,
  <PERSON>,
  Mo<PERSON>,
  Spin,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetTransactionRequests } from "apps/Plan/ServerSideStates";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import dayjs from "dayjs";
import { SecretText } from "apps/Common/SecretString";
import { lazy, Suspense, useState } from "react";
import { determineTransactionStatus } from "helpers/DetermineTransactionStatusColor";
import ExpandableText from "apps/Common/TruncatedDesc";
const ChangeTransactionStatus = lazy(() => import("./ChangeTransactionStatus"));

const TransactionRequestTableList = () => {
  const { waitApprovedfilter: filter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();
  const [selectedRecord, setSelectedRecord] = useState<null | any>(null);
  const [isShowChangeTransactionStatusModal, setIsShowTransactionStatusModal] =
    useState(false);

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(
      hanldleSetTranasactionFilter({
        filter: newFilter,
        type: "waitApprovedfilter",
      })
    );
  };

  const waitApprovedTransactions = useGetTransactionRequests(filter);
  const { Text } = Typography;

  const columns = [
    {
      title: "Firma",
      dataIndex: ["Building", "Company", "Name"],
      key: "CompanyName",
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{value}</Text>
            </div>
            {record?.Building?.Company?.Phone && (
              <>
                <div className="!text-primary">
                  <SecretText
                    text={record?.Building?.Company?.Phone}
                    textType="phone"
                  />
                </div>
              </>
            )}

            <div className="!mt-2">
              {record?.ContractId ? (
                <Tag color="green">Sözleşmeli</Tag>
              ) : (
                <Tag color="error">Sözleşmesiz</Tag>
              )}
            </div>
          </>
        );
      },
      width: "10%",
      sorter: (a: any, b: any) =>
        a?.Building?.Company?.Name.localeCompare(b?.Building?.Company?.Name),
    },
    {
      title: "Oluşturan Kişi",
      render: (_: string, record: any) => {
        return (
          <>
            <Text>{`${record?.RequestedPerson?.Name || ""} ${
              record?.RequestedPerson?.Surname || ""
            }`}</Text>
          </>
        );
      },
      width: "10%",
      sorter: (a: any, b: any) =>
        a?.RequestedPerson?.Name.localeCompare(b?.RequestedPerson?.Name),
    },
    {
      title: "Şantiye",
      dataIndex: ["Building", "Name"],
      key: "BuildingName",
      width: "8%",
      render: (value: string, record: any) => {
        return (
          <div className="!flex gap-1 flex-col">
            <div>
              <Text>{value}</Text>
            </div>
           
          
          </div>
        );
      },
      sorter: (a: any, b: any) =>
        a?.Building?.Name.localeCompare(b?.Building?.Name),
    },
    {
      title: "Araç Tipi",
      dataIndex: ["CarType", "Name"],
      key: "CarTypeName",
      width: "8%",
      sorter: (a: any, b: any) =>
        a?.CarType?.Name.localeCompare(b?.CarType?.Name),
    },
    {
      title: "Pompa Tipi",
      dataIndex: "TransactionRequestPompType",
      key: "CarTypeName",
      width: "8%",
      render: (pomps: any[]) => {
        return (
          <div className="!flex gap-1 !flex-wrap">
            {pomps?.map((item: any) => {
              return (
                <span>
                  <Tag color="blue">{item?.PompType?.Name || ""}</Tag>
                </span>
              );
            })}
          </div>
        );
      },
    },

    {
      title: "Ürün",
      dataIndex: ["Product", "Name"],
      key: "ProductInfoes",
      render: (value: string, record: any) => {
        return (
          <div className="!flex flex-col gap-1">
            <div>
              <Text>{value}</Text>
            </div>
            <div>
              <Text>{record?.ConcreteLocation?.Name}</Text>
            </div>
            <div>
              <Tag color="blue">{record?.ConsistencyClass?.Name || ""}</Tag>
            </div>

            <div></div>

            <div className="!flex !flex-wrap gap-1">
              {record?.TransactionRequestConcreteOption?.map(
                (item: any, index: number) => {
                  return (
                    <>
                      <Tag color="green">{item.ConcreteOption?.Name}</Tag>
                    </>
                  );
                }
              )}
            </div>
          </div>
        );
      },
      width: "8%",
      sorter: (a: any, b: any) =>
        a?.Product?.Name.localeCompare(b?.Product?.Name),
    },
    {
      title: "Miktar(m³)",
      dataIndex: "DesiredTotalConcrete",
      key: "ProductInfoes",
      width: "8%",
      render: (value: string) => {
        return <Text className="!font-bold">{value}</Text>;
      },
      sorter: (a: any, b: any) =>
        Number(a?.DesiredTotalConcrete) - Number(b?.DesiredTotalConcrete),
    },

    {
      title: "İstenilen Tarih",
      dataIndex: "DesiredDateTime",
      key: "DesiredDateTime",
      render: (text: any) => {
        if (text) {
          return (
            <>
              <div>
                <Text>{new Date(text).toLocaleDateString("tr-TR")}</Text>
              </div>
              <div>
                <Text>{dayjs(new Date(text)).format("HH:mm")}</Text>
              </div>
            </>
          );
        }
        return <></>;
      },
      width: "8%",
      sorter: (a: any, b: any) => {
        const aTime = a?.DesiredDateTime
          ? dayjs(a.DesiredDateTime).valueOf()
          : 0;
        const bTime = b?.DesiredDateTime
          ? dayjs(b.DesiredDateTime).valueOf()
          : 0;
        return aTime - bTime;
      },
    },

    {
      title: "Durum",
      dataIndex: ["Status", "Name"],
      render: (value: string, record: any) => {
        return (
          <Tag color={determineTransactionStatus(record.StatusId)}>{value}</Tag>
        );
      },
      width: "8%",
      sorter: (a: any, b: any) =>
        a?.Status?.Name.localeCompare(b?.Status?.Name),
    },
    {
      title: "Şantiye Açıklaması",
      dataIndex: "Note",
      key: "Note",
      width: "10%",
      render: (value: string) => {
        return (
          <>
            <ExpandableText
              title={"Şantiye Açıklaması"}
              limit={20}
              text={value || ""}
            />
          </>
        );
      },
    },

    {
      title: "Rapor",
      dataIndex: "RejectNote",
      key: "Note",
      width: "10%",
      render: (value: string) => {
        return (
          <>
            <ExpandableText title={"Rapor"} limit={20} text={value || ""} />
          </>
        );
      },
    },

    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "4%",
      render: (key: any, record: any) => (
        <div className="!flex items-center justify-center gap-2">
          {filter.StatusIds.includes(1) ? (
            <>
              <Button
                onClick={async () => {
                  const data = { ...record, SelectedStatus: 2 };
                  await setSelectedRecord(data);
                  setIsShowTransactionStatusModal(true);
                }}
                type="text"
                className="!bg-green-500 !text-white"
              >
                Onayla
              </Button>
              <Button
                onClick={async () => {
                  const data = { ...record, SelectedStatus: 8 };
                  await setSelectedRecord(data);
                  setIsShowTransactionStatusModal(true);
                }}
                type="text"
                className="!bg-red-500 !text-white"
              >
                Reddet
              </Button>
            </>
          ) : (
            <></>
          )}
        </div>
      ),
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={
          waitApprovedTransactions.isLoading ||
          waitApprovedTransactions.isFetching
        }
        dataSource={
          waitApprovedTransactions.data
            ? waitApprovedTransactions.data.Data
            : []
        }
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: waitApprovedTransactions.data?.FilteredCount || 0,
          current: waitApprovedTransactions.data?.PageIndex,
          pageSize: waitApprovedTransactions.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />

      <Modal
        title={""}
        open={isShowChangeTransactionStatusModal}
        onCancel={() => {
          setIsShowTransactionStatusModal(false);
        }}
        footer={false}
      >
        {isShowChangeTransactionStatusModal && (
          <Suspense fallback={<Spin />}>
            <ChangeTransactionStatus
              selectedRecord={selectedRecord}
              onFinish={() => {
                setIsShowTransactionStatusModal(false);
              }}
            />
          </Suspense>
        )}
      </Modal>
    </Col>
  );
};

export default TransactionRequestTableList;
