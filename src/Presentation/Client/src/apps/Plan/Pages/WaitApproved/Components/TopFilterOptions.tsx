import { Form, Row } from "antd";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import GeneralPlanTimePeriodes from "apps/Common/GeneralPlanTimeInput";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import dayjs from "dayjs";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const TopFilterOptions = () => {
  const [form] = Form.useForm();
  const {waitApprovedfilter:filter} = useSelector((state:RootState)=>state.transactions)
  const dispatch = useDispatch()

  const handleOnChangeBuildings = (valueIds:string[])=>{
    let currentFilter = {...filter}
    if(valueIds)
    {
      currentFilter["BuildingIds"] = valueIds
    }
    else{
      delete currentFilter["BuildingIds"]

    }
    dispatch(hanldleSetTranasactionFilter({filter:currentFilter,type:"waitApprovedfilter"}))

  }



  const handleOnChnageTimePeriode = (value:string)=>{

    let currentFilter = {...filter}
    if(value ==="all")
    {
      delete currentFilter["StartDesiredTime"] 
      delete currentFilter["EndDesiredTime"] 
    }
    else{
      let startDate =  ""
      let endDate =""
      if(value===dayjs().format("YYYY-MM-DD")||value===dayjs().add(1, "day").format("YYYY-MM-DD"))
      {
        startDate = value
        endDate = value
      }
      else{
        startDate =  dayjs().format("YYYY-MM-DD")
        endDate =value
      }

     
      currentFilter["StartDesiredTime"] = startDate
      currentFilter["EndDesiredTime"] = endDate
    }
    dispatch(hanldleSetTranasactionFilter({filter:currentFilter,type:"waitApprovedfilter"}))

  }




  useEffect(()=>{

  
    
    form.setFieldValue("BuildingIds",filter?.BuildingIds)
    form.setFieldValue("Times",filter?.EndDesiredTime?dayjs(filter?.EndDesiredTime).format("YYYY-MM-DD"):"all")
 

 
  
},[filter])

  return (
    <>
      <Form form={form}>
        <Row gutter={[10, 10]}>
          <GeneralBuildingInput
            name="BuildingIds"
            placeholder="Şantiye"
            className="!m-0"
            xs={24}
            md={12}
            lg={6}
           
            allowClear={true}
            mode="multiple"
            onChange={handleOnChangeBuildings}
          />
          <GeneralPlanTimePeriodes
            name="Times"
            placeholder="Zamanlama"
            className="!m-0"
            xs={24}
            md={12}
            lg={6}
          
            allowClear={true}
            onChange={handleOnChnageTimePeriode}
          

          />
        </Row>
      </Form>
    </>
  );
};

export default TopFilterOptions;
