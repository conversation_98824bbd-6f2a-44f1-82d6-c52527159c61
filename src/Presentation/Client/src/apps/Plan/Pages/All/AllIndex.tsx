
import {  Col, Drawer, Row, Spin, } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";

import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { handleResetFilterTrasaction } from "apps/Plan/ClientSideStates";
import TopFilterOptions from "./Components/TopFiterOptions";
import TransactionRequestTableList from "./Components/TransactionRequestList";
import Search from "./Components/Search";
const DetailsFilter= lazy(
  () => import("./Components/DetailsFilter")
);

const AllIndex = () => {

 
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
  const { allFilter } = useSelector(
    (state: RootState) => state.transactions
  );

  return (
    <>
      <MazakaLayout
        title={"Tüm Talepler Listesi"}
        headDescription={
          "Tüm talepler sayfası, sistemde kayıtlı tüm isteklerin listesini görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]} className="!mt-4">
         
          <Col span={24}>
            <Row gutter={[10, 10]}>

              <Col xs={24} lg={18}>
                <TopFilterOptions />
              </Col>
              <Col xs={24} lg={6} className="!flex justify-end gap-2">
               
                <MazakaDetailsFilterButton
                  setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
                />
                {Object.entries(allFilter).length > 3 && (
                  <>
                    <MazakaClearFilterButton
                      type="allFilter"
                      actionFunk={ handleResetFilterTrasaction}
                    />
                  </>
                )}
              </Col>
            </Row>
          </Col>

          <Col span={24}>
            <Row>
              <Col span={24} style={{ background: "#f0f0f0" }}>
                <Row gutter={[0, 10]} className="p-2">
                  <Col xs={24} xl={12} className="">
                    <Row gutter={[10, 10]}>
                      <Col xs={24} md={12} lg={10}>
                        <Search />
                      </Col>
                    </Row>
                  </Col>
                  <Col xs={24} xl={12} className="text-right"></Col>
                </Row>
              </Col>
              <Col span={24}>
                <TransactionRequestTableList />
              </Col>
            </Row>
          </Col>
        </Row>
      </MazakaLayout>

      
      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
        <>
        {
          isShowFilterDetailsDrawer&&
          <Suspense fallback={<Spin/>} >
            <DetailsFilter
              onFinish={() => {
                setIsShowFilterDetailsDrawer(false);
              }}
            />

          </Suspense>
        }
        </>
      </Drawer>
    </>
  );
};

export default AllIndex;
