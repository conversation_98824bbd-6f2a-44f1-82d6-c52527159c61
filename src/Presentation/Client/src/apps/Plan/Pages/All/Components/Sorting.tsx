import { SortAscendingOutlined } from "@ant-design/icons";
import { Dropdown, MenuProps, Space } from "antd";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";

import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const Sorting = () => {
  const dispatch = useDispatch();
  const {allFilter:filter} = useSelector((state:RootState)=>state.transactions)
  const applySorterFilter = ({ key }: { key: string }) => {
    let newFilter: any = {};

    const parsedKey = key.split("_");
    if (!!parsedKey[1]) {
      newFilter.SortProperty = parsedKey[0];
      newFilter.SortType = parsedKey[1] === "asc" ? "asc" : "desc";
      let allFilter = { ...filter, ...newFilter };

      dispatch(
        hanldleSetTranasactionFilter({ filter: allFilter, type: "allFilter" })
      );
    }
  };

  const sortingDropdownMenu: MenuProps["items"] = [
    {
      key: "DesiredDateTime_asc",
      label: "İstenilen tarih eskiden yeniye göre",
    },
    {
      key: "DesiredDateTime_desc",
      label: "İstenilen tarih yeniden eskiye göre",
    },

    {
      key: "ApprovedDateTime_asc",
      label: "Onay tarihi eskiden yeniye göre",
    },
    {
      key: "ApprovedDateTime_desc",
      label: "Onay tarihi yeniden eskiye göre",
    },
    {
      key: "Building.Name_asc",
      label: "Şantiye adına göre A-Z",
    },
    {
      key: "Building.Name_desc",
      label: "Şantiye adına göre Z-A",
    },
    {
      key: "Building.Company.Name_asc",
      label: "Firma adına göre A-Z",
    },
    {
      key: "Building.Company.Name_desc",
      label: "Firma adına göre Z-A",
    },
  ];
  return (
    <>
      <Dropdown
        menu={{
          items: sortingDropdownMenu,
          onClick: applySorterFilter,
        }}
        className="!mr-4"
      >
        <span onClick={(e) => e.preventDefault()}>
          <Space>
            <SortAscendingOutlined />
          </Space>
        </span>
      </Dropdown>
    </>
  );
};

export default Sorting;
