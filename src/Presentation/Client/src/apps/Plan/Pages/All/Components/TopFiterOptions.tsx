import { Form, Row } from "antd";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const TopFilterOptions = () => {
  const [form] = Form.useForm();
  const {allFilter:filter} = useSelector((state:RootState)=>state.transactions)
  const dispatch = useDispatch()

  const handleOnChangeBuildings = (valueIds:string[])=>{
    let currentFilter = {...filter}
    if(valueIds)
    {
      currentFilter["BuildingIds"] = valueIds
    }
    else{
      delete currentFilter["BuildingIds"]

    }
    dispatch(hanldleSetTranasactionFilter({filter:currentFilter,type:"allFilter"}))

  }


  const handleOnChangeStatus =(valueIds:string[])=>{
    let currentFilter = {...filter}
    if(valueIds)
    {
      currentFilter["StatusIds"] = valueIds
    }
    else{
      delete currentFilter["StatusIds"]

    }
    dispatch(hanldleSetTranasactionFilter({filter:currentFilter,type:"allFilter"}))

  }

  const options = [
    {
      value: 1,
      label: "Onay Bekliyor",
     
    },
    {
      value: 2,
      label: "Onaylandı",
   
    },
    {
      value: 3,
      label: "Planlamada",
    
    },
    {
      value: 4,
      label: "Planlandı",
     
    },
    {
      value: 5,
      label: "Döküm Devam Ediyor",
    
    },
    {
      value: 6,
      label: "Tamamlandı",
     
    },
    {
      value: 7,
      label: "İptal Talebi",
      
    },
    {
      value: 8,
      label: "Reddedildi",
     
    }
  ]

  return (
    <>
      <Form form={form}>
        <Row gutter={[10, 10]}>
          <GeneralBuildingInput
            name="BuildingIds"
            placeholder="Şantiye"
            className="!m-0"
            xs={24}
            md={12}
            lg={6}
           
            allowClear={true}
            mode="multiple"
            onChange={handleOnChangeBuildings}
          />
      
          <MazakaSelect
            name="StatusIds"
            placeholder="Durum"
            className="!m-0"
            xs={24}
            md={12}
            lg={6}
            options={options}
            mode="multiple"
          
            allowClear={true}
            onChange={handleOnChangeStatus}
          

          />
        </Row>
      </Form>
    </>
  );
};

export default TopFilterOptions;
