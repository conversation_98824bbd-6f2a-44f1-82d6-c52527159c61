import { <PERSON><PERSON>, <PERSON>, Modal, Table, Tag, Typography } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetTransactionRequests } from "apps/Plan/ServerSideStates";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import dayjs from "dayjs";
import { SecretText } from "apps/Common/SecretString";
import { lazy, useState } from "react";
import { RequestTransactionAndDetails } from "apps/Plan/Models";
import { determineTransactionStatus } from "helpers/DetermineTransactionStatusColor";
import ExpandableText from "apps/Common/TruncatedDesc";

const AddOrUpdateCertificate = lazy(() => import("./AddOrUpdateCertificate"));

const TransactionRequestTableList = () => {
  const [isShowAddCertificateModal, setIsShowAddCertificateModal] =
    useState(false);

  const { allFilter: filter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(
      hanldleSetTranasactionFilter({ filter: newFilter, type: "allFilter" })
    );
  };

  const transactions = useGetTransactionRequests(filter);
  const { Text } = Typography;
  const [selectedRecord, setSelectedRecord] =
    useState<null | RequestTransactionAndDetails>(null);

  const columns = [
    {
      title: "Firma",
      dataIndex: ["Building", "Company", "Name"],
      key: "CompanyName",
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{value}</Text>
            </div>
            {record?.Building?.Company?.Phone && (
              <>
                <div className="!text-primary">
                  <SecretText
                    text={record?.Building?.Company?.Phone}
                    textType="phone"
                  />
                </div>
              </>
            )}
            <div className="!mt-2">
              {record?.ContractId ? (
                <Tag color="green">Sözleşmeli</Tag>
              ) : (
                <Tag color="error">Sözleşmesiz</Tag>
              )}
            </div>
          </>
        );
      },
      width: "10%",
      sorter: (a: any, b: any) =>
        a?.Building?.Company?.Name.localeCompare(b?.Building?.Company?.Name),
    },
    {
      title: "Oluşturan Kişi",
      render: (_: string, record: any) => {
        return (
          <>
            <Text>{`${record?.RequestedPerson?.Name || ""} ${
              record?.RequestedPerson?.Surname || ""
            }`}</Text>
          </>
        );
      },
      width: "10%",
      sorter: (a: any, b: any) =>
        a?.RequestedPerson?.Name.localeCompare(b?.RequestedPerson?.Name),
    },
    {
      title: "Şantiye",
      dataIndex: ["Building", "Name"],
      key: "BuildingName",
      width: "10%",
      render: (value: string, record: any) => {
        return (
          <div className="!flex gap-1 flex-col">
            <div>
              <Text>{value}</Text>
            </div>
          </div>
        );
      },
      sorter: (a: any, b: any) =>
        a?.Building?.Name.localeCompare(b?.Building?.Name),
    },
    {
      title: "Araç Tipi",
      dataIndex: ["CarType", "Name"],
      key: "CarTypeName",
      width: "8%",
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{value}</Text>
            </div>
            <div>
              <Text>{record?.PompType?.Name || ""}</Text>
            </div>
          </>
        );
      },
      sorter: (a: any, b: any) =>
        a?.CarType?.Name.localeCompare(b?.CarType?.Name),
    },

    {
      title: "Ürün",
      dataIndex: ["Product", "Name"],
      key: "ProductInfoes",
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{value}</Text>
            </div>
            <div>
              <Text>{record?.ConcreteLocation?.Name}</Text>
            </div>
            <div>
              <Text>{record?.CosistencyClass?.Name}</Text>
            </div>
            <div className="!flex !flex-wrap gap-1">
              {record?.TransactionRequestConcreteOption?.map(
                (item: any, index: number) => {
                  return (
                    <>
                      <Tag color="green">{item.ConcreteOption?.Name}</Tag>
                    </>
                  );
                }
              )}
            </div>
          </>
        );
      },
      width: "8%",
      sorter: (a: any, b: any) =>
        a?.Product?.Name.localeCompare(b?.Product?.Name),
    },
    {
      title: "Miktar(m³)",
      dataIndex: "DesiredTotalConcrete",
      key: "ProductInfoes",
      width: "8%",
      render: (value: string) => {
        return <Text className="!font-bold">{value}</Text>;
      },
      sorter: (a: any, b: any) =>
        Number(a?.DesiredTotalConcrete) - Number(b?.DesiredTotalConcrete),
    },

    {
      title: "İstenilen Tarih",
      dataIndex: "DesiredDateTime",
      key: "DesiredDateTime",
      render: (text: any) => {
        if (text) {
          return (
            <>
              <div>
                <Text>{new Date(text).toLocaleDateString("tr-TR")}</Text>
              </div>
              <div>
                <Text>{dayjs(new Date(text)).format("HH:mm")}</Text>
              </div>
            </>
          );
        }
        return <></>;
      },
      width: "8%",
      sorter: (a: any, b: any) => {
        const aTime = a?.DesiredDateTime
          ? dayjs(a.DesiredDateTime).valueOf()
          : 0;
        const bTime = b?.DesiredDateTime
          ? dayjs(b.DesiredDateTime).valueOf()
          : 0;
        return aTime - bTime;
      },
    },
    {
      title: "Onaylanan Tarih",
      dataIndex: "ApprovedDateTime",
      key: "DesiredDateTime",
      render: (text: any) => {
        if (text) {
          return (
            <>
              <div>
                <Text>{new Date(text).toLocaleDateString("tr-TR")}</Text>
              </div>
              <div>
                <Text>{dayjs(new Date(text)).format("HH:mm")}</Text>
              </div>
            </>
          );
        }
        return <></>;
      },
      width: "8%",
      sorter: (a: any, b: any) => {
        const aTime = a?.ApprovedDateTime
          ? dayjs(a.ApprovedDateTime).valueOf()
          : 0;
        const bTime = b?.ApprovedDateTime
          ? dayjs(b.ApprovedDateTime).valueOf()
          : 0;
        return aTime - bTime;
      },
    },

    {
      title: "Durum",
      dataIndex: ["Status", "Name"],
      render: (value: string, record: any) => {
        return (
          <Tag color={determineTransactionStatus(record?.StatusId)}>
            {value}
          </Tag>
        );
      },
      width: "8%",
      sorter: (a: any, b: any) =>
        a?.Status?.Name.localeCompare(b?.Status?.Name),
    },
    {
      title: "Açıklama",
      dataIndex: "Note",
      key: "Note",
      width: "10%",
      render: (value: string, record: any) => {
        // Duruma göre hangi note ve user bilgisini göstereceğimizi belirle
        let noteText = "";
        let userInfo = "";
        let titleText = "Şantiye Açıklaması";

        // Status kontrolü - İptal durumu
        if (record?.Status?.Name === "İptal" || record?.StatusId === 7) {
          noteText = record?.CanceledNote || "";
          userInfo = record?.CanceledUser
            ? `${record.CanceledUser?.Name} ${record.CanceledUser?.Surname}`
            : "";
          titleText = "İptal Açıklaması";
        }
        // Status kontrolü - Onaylı durumu
        else if (
          record?.Status?.Name === "Onaylandı" ||
          record?.StatusId === 2
        ) {
          noteText = record?.ApprovedNote || "";
          userInfo = record?.ApprovedUser
            ? `${record.ApprovedUser?.Name} ${record.ApprovedUser?.Surname}`
            : "";
          titleText = "Onay Açıklaması";
        } else if (
          record?.Status?.Name === "Reddedildi" ||
          record?.StatusId === 8
        ) {
          noteText = record?.RejectNote || "";
          titleText = "Reddilme Açıklaması";
        }
        // Diğer durumlar
        else {
          noteText = record?.Note || "";
          titleText = "Şantiye Açıklaması";
        }

        if (noteText || userInfo) {
          return (
            <div>
              {userInfo && (
                <Tag color="blue" style={{ marginBottom: 4 }}>
                  {userInfo}
                </Tag>
              )}
              {noteText && (
                <div>
                  <ExpandableText
                    title={titleText}
                    limit={20}
                    text={noteText}
                  />
                </div>
              )}
            </div>
          );
        }
        return null;
      },
    },
    {
      title: "Rapor",
      dataIndex: "ApprovedNote",
      key: "ApprovedNote",
      width: "10%",
      render: (value: string) => {
        if (value) {
          return (
            <>
              <ExpandableText title={"Rapor"} limit={20} text={value || ""} />
            </>
          );
        }
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <div className="!flex items-center justify-center gap-2">
          <Button
            onClick={async () => {
              await setSelectedRecord(record);
              setIsShowAddCertificateModal(true);
            }}
            type="text"
            className="!bg-green-500 !text-white"
          >
            Lab Sonuçu Ekle
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={transactions.isLoading || transactions.isFetching}
        dataSource={transactions.data ? transactions.data.Data : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: transactions.data?.FilteredCount || 0,
          current: transactions.data?.PageIndex,
          pageSize: transactions.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <Modal
        title={""}
        open={isShowAddCertificateModal}
        onCancel={() => {
          setIsShowAddCertificateModal(false);
        }}
        footer={false}
      >
        <AddOrUpdateCertificate
          selectedRecord={selectedRecord}
          onFinish={() => {
            setIsShowAddCertificateModal(false);
            setSelectedRecord(null);
          }}
        />
      </Modal>
    </Col>
  );
};

export default TransactionRequestTableList;
