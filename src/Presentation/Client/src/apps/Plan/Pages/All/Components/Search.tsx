import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useEffect, useState } from "react";
import { Input } from "antd";
import { useDebouncedCallback } from "use-debounce";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import Sorting from "./Sorting";


const Search = () => {
    const {allFilter:filter} = useSelector((state:RootState)=>state.transactions)
    let dispatch = useDispatch();
    const debounce = useDebouncedCallback((inputValue) => {
      let value = inputValue?.trim();
      if (value) {
        let newFilter = { ...filter };
        newFilter["SearchKey"] = value;
        dispatch(
        hanldleSetTranasactionFilter({filter:newFilter,type:"allFilter"})
        );
      } else {
        let newFilter = { ...filter };
        delete newFilter["SearchKey"];
        dispatch(
            hanldleSetTranasactionFilter({filter:newFilter,type:"allFilter"})
            );
      }
    }, 1000);
    let [searchValue, setSearchValue] = useState<string | undefined>(undefined);
  
    useEffect(() => {
      if(filter)
      {

        setSearchValue(filter[`SearchKey`]);
      }
    }, [filter]);
    
    return ( <>

    
    
    <div className="!flex  items-center">
      <Input
        allowClear
        className="!py-3 !border-0 max-w-sm !w-full"
        placeholder={"Ara..."}
        onChange={(e) => {
          setSearchValue(e.target.value);
          debounce(e.target.value);
        }}
        value={searchValue}
      />
      <Sorting/>
      
    </div>
    
    </> );
}
 
export default Search;