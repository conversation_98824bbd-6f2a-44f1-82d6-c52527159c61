import { Col, Form, Row } from "antd";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import GeneralContractInput from "apps/Common/GeneralContractInput";
import GeneralProductInput from "apps/Common/GeneralProductInput";
import GeneralStationInput from "apps/Common/GeneralStationInput";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaRangePicker } from "apps/Common/MazakaRangePicker";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import dayjs from "dayjs";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form,{submitText:"Filtrele"});
  const { allFilter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
    let currentFilter = { ...allFilter };
    
    for (let key in formValues) {
      if (!formValues[key] || (Array.isArray(formValues[key]) && formValues[key].length === 0)) {
        delete formValues[key];   
        delete currentFilter[key];
      }
    }
    if (formValues?.Date && formValues["Date"][0] && formValues["Date"][1]) {
      formValues["ApprovedStartDate"] = dayjs(formValues["Date"][0]).format(
        "YYYY-MM-DD"
      );
      formValues["ApprovedEndDate"] = dayjs(formValues["Date"][1]).format(
        "YYYY-MM-DD"
      );
      delete formValues["Date"]
    } else {
      mazakaForm.setFailed(
        2000,
        "Tarih aralığında başlangıç ve bitiş tarihi zorunlu "
      );
    }
    const newFilter = { ...currentFilter, ...formValues };
 
    await dispatch(
      hanldleSetTranasactionFilter({
        filter: newFilter,
        type: "allFilter",
      })
    );
    mazakaForm.setSuccess(1000, () => {}, "Başarılı");
    onFinish();
  };

 
  useEffect(() => {
    const startDate = allFilter["ApprovedStartDate"]
    ? dayjs(allFilter["ApprovedStartDate"])
    : null;
  const endDate = allFilter["ApprovedEndDate"]
    ? dayjs(allFilter["ApprovedEndDate"])
    : null;
    const dateRange = startDate && endDate ? [startDate, endDate] : [];
    form.setFieldsValue({
      CompanyIds: allFilter?.CompanyIds || undefined,
      ContractIds: allFilter?.ContractIds || undefined,
      StationIds: allFilter?.StationIds || undefined,
      ProductIds: allFilter?.ProductIds || undefined,
      Date:dateRange
    });
  }, [allFilter]);
  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} {...formActions}>
          <Row gutter={[0, 10]}>
            <GeneralCompanies
              name="CompanyIds"
              label={"Firma"}
              placeholder="Firma"
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />

            <GeneralStationInput
              name="StationIds"
              label={"Santral"}
              placeholder="Santral"
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />
           
            <GeneralProductInput
              name={"ProductIds"}
              label={"Ürün"}
              placeholder={"Ürün"}
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />
            <GeneralContractInput
              name={"ContractIds"}
              label={"Sözleşme"}
              placeholder={"Sözleşme"}
              className="!m-0"
              xs={24}
              allowClear={true}
              mode="multiple"
            />
               <MazakaRangePicker
              name={"Date"}
              label="Onaylama Tarihi"
              xs={24}
              className="!m-0"
              
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
