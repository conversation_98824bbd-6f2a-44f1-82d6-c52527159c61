import { Col, Form, message, Row, Typography, Upload, Modal, Skeleton } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { FC, useEffect, useState } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { DeleteOutlined, EyeOutlined, FileOutlined, InboxOutlined } from "@ant-design/icons";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import {    addLaboratoryResult, deleteLaboratoryResult } from "apps/Plan/Services";
import useMazakaForm from "hooks/useMazakaForm";
import { openNotificationWithIcon } from "helpers/Notifications";
import { useGetLaboratoryResults } from "apps/Plan/ServerSideStates";

interface AddOrUpdateCertificateProps {
  selectedRecord: any;
  onFinish: any;
}

const AddOrUpdateCertificate: FC<AddOrUpdateCertificateProps> = ({ selectedRecord, onFinish }) => {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const { formActions, mazakaForm } = useMazakaForm(form);
  const [fileList, setFileList] = useState<any[]>([]);
  const { Text } = Typography;

  const laboratoryResult = useGetLaboratoryResults({TransactionRequestId:selectedRecord["Id"]})


  const handleOnFinish = async () => {
    if (fileList.length <= 0) {
      message.error("Dosya Seçimi Zorunlu");
      return false;
    }

    mazakaForm.setLoading();

    try {
      let data = fileList.map((item: any) => ({
        TransactionRequestId: selectedRecord["Id"],
        File: item,
      }));
    
      // Use Promise.all to handle all `addLaboratoryResult` calls concurrently
      await Promise.all(
        data.map(async (item) => {
          await addLaboratoryResult(item);
          message.success("İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getLaboratoryResultListFilter,
            exact: false,
          });
          form.resetFields();
        })
      );
    
      mazakaForm.setSuccess(2000, () => "İşlem Başarılı");
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "AddCertificate");
    }
    
  };

 

  useEffect(() => {
    if (laboratoryResult.data) {
     
     
     let files = laboratoryResult.data?laboratoryResult.data.Data:[]
      let currentFiles = files.map((item: any) => ({
        ...item,
        name: item.FileName,
        isSaved: true,
      }));
     
      setFileList((prev:any)=>currentFiles);
    }
  }, [laboratoryResult.data]);

  const validateFile = (file: any) => {
    const isValidSize = file.size <= 2000000;
    if (!isValidSize) {
      message.error("Dosya Boyutu en fazla 2MB olmalı");
      return false;
    }
    return true;
  };

  const beforeUpload = (file: any) => {
    const isValid = validateFile(file);
    if (isValid) {
      setFileList((prevFileList) => [...prevFileList, file]);
    }
    return false; 
  };

  const handleDeleteFile = ( item: any) => {
    Modal.confirm({
      title: "Bu dosyayı silmek istediğinizden emin misiniz?",
      okText: "Evet",
      cancelText: "Hayır",
      onOk: async () => {
        try {
          if (item.Id) {
            await deleteLaboratoryResult(item); 
          openNotificationWithIcon("success","Dosya başarıyla silindi.");
          }
          setFileList((prevFileList) => prevFileList.filter((file) => file.name !== item.name));
        } catch (error: any) {
          showServiceErrorMessage(error,undefined,"DeleteCertificate",true)
         
        }
      },
    });
  };

  const { Dragger } = Upload;

  return (
    <Col span={24} className="!p-4">
      <MazakaForm form={form} onFinish={handleOnFinish} initialValues={{ Active: true }}>
        <Row gutter={[10, 10]}>
          <Col xs={24}>
            <Row gutter={[10, 10]}>
              <Col xs={24}>
                <Dragger
                  accept=".pdf"
                  beforeUpload={beforeUpload}
                  customRequest={() => {}}
                  multiple={true}
                  fileList={fileList}
                  showUploadList={false}
                >
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">Dosya Yükle</p>
                  <p className="ant-upload-hint">
                    Sertifikanizi buradan yükleyebilirsiniz
                  </p>
                </Dragger>
              </Col>
              <Col xs={24}>
              <Skeleton loading={laboratoryResult.isLoading ||laboratoryResult.isFetching} >

                {fileList.map((item) => (
                  <div key={item.name} className="!flex justify-between">
                    <div className="!flex items-center gap-1">
                      <FileOutlined className="!text-indigo-500" />
                      <Text>{item.name}</Text>
                    </div>
                    <div className="!flex gap-2">
                      <DeleteOutlined
                        className="!text-red-600"
                        onClick={() => handleDeleteFile( item)}
                      />
                      {
                        item?.Id&&
                        <>
                      <EyeOutlined
                        className="!text-indigo-600"
                        
                        onClick={() => {
                         
                          window.open(`${ process.env.NODE_ENV === "development"?process.env.REACT_APP_DEVELOPMENT_API_BASE_MEDIA_DOWNLOAD:window.location.origin }/uploads/LabResult/${item?.FileName}`)
                        }}
                      />
                        </>
                      }
                    </div>
                  </div>
                ))}
              </Skeleton>
              </Col>
            </Row>
          </Col>
        </Row>
      </MazakaForm>
    </Col>
  );
};

export default AddOrUpdateCertificate;
