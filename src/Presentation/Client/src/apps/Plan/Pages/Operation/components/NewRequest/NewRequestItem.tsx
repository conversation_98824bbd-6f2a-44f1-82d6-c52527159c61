import { <PERSON>, <PERSON>, <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Divider } from "antd";
import { FC, useState } from "react";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import DetermineDateTime from "./DetermineDateTime";
import { determineTransactionStatus } from "helpers/DetermineTransactionStatusColor";
import ApproveCancelRequest from "./ApproveCancelRequest";

const NewRequestItem: FC<any> = (props) => {
  const { Text } = Typography;

  const { operationTransactionFilter: filter } = useSelector(
    (state: RootState) => state.transactions
  );
  const [isShowDetermineDateTime, setIsShowDetermineDateTime] = useState(false);
  const [isShowCancelRequestModal, setIsShowCancelRequestModal] =
    useState(false);

  const convertToTRDay = (day: string) => {
    switch (day.toLocaleLowerCase()) {
      case "monday":
        return "Paz";
      case "tuesday":
        return "Sal";
      case "wednesday":
        return "Çar";
      case "thursday":
        return "Per";
      case "friday":
        return "Cum";
      case "saturday":
        return "Cum"; // Cumartesi ve Cuma'nın ilk 3 harfi aynı
      case "sunday":
        return "Paz";
      default:
        return "Geç";
    }
  };

  return (
    <>
      <Col
        span={24}
        style={{ position: "relative" }}
        className={filter?.StatusIds.includes(4) ? "" : "request-dragable"}
        draggable={filter?.StatusIds.includes(4) ? false : true}
      >
        <Card
          className={
            filter?.StatusIds.includes(4)
              ? "hover:cursor-not-allowed !border-none"
              : "!border-none"
          }
          bodyStyle={{
            padding: "0px",
            background: filter?.StatusIds.includes(4)
              ? "ghostwhite"
              : "#fffae4",
            borderRadius: "0",
          }}
        >
          <Row gutter={[10, 10]} className="!p-4">
            <Col xs={24}>
              <Row gutter={[0, 0]}>
                {props.StatusId !== 5 && props.StatusId !== 6 && (
                  <>
                    <Col xs={24} className="!flex gap-2 items-center">
                      <Button
                        onClick={() => {
                          setIsShowDetermineDateTime(true);
                        }}
                        size="small"
                        className="!bg-red-500  !text-white"
                      >
                        Zaman Belirle
                      </Button>
                      {filter?.StatusIds.includes(2) && (
                        <>
                          <Button
                            onClick={async () => {
                              setIsShowCancelRequestModal(true);
                            }}
                            className="!bg-indigo-500 !text-xs !text-white "
                            size="small"
                          >
                            İptal Et
                          </Button>

                          <div className="!flex items-center gap-1">
                            <Text className="!text-red-500">
                              {new Date(
                                filter?.StatusIds.includes(4)
                                  ? props.ApprovedDateTime
                                  : props.DesiredDateTime
                              ).toLocaleDateString("tr-TR")}
                            </Text>

                            <Text className="!text-red-500">
                              {dayjs(
                                new Date(
                                  filter?.StatusIds.includes(4)
                                    ? props.ApprovedDateTime
                                    : props.DesiredDateTime
                                )
                              ).format("HH:mm")}
                            </Text>
                            <Text className="!text-red-500">
                              {convertToTRDay(
                                dayjs(
                                  new Date(
                                    filter?.StatusIds.includes(4)
                                      ? props.ApprovedDateTime
                                      : props.DesiredDateTime
                                  )
                                ).format("dddd")
                              )}
                            </Text>
                          </div>
                        </>
                      )}
                    </Col>
                  </>
                )}
                <Col xs={24} className="!flex gap-1 items-center !mt-2">
                  <Text className="!text-xs">
                    {props?.Building?.Company?.Name || ""}
                  </Text>
                </Col>
                <Col xs={24}>
                  <Text className="!text-black !text-xs !font-bold !flex">
                    {props?.Building?.Name}
                  </Text>
                  <Text id={"item-id"} className=" hidden">
                    {props.Id}
                  </Text>
                  <Text id={"item-desiredDateTime"} className=" hidden">
                    {props?.DesiredDateTime}
                  </Text>
                </Col>

                <Col xs={24} className="!flex">
                  <Text className="!text-xs">{`${props?.RequestedPerson?.Name || ""
                    } ${props?.RequestedPerson?.Surname || ""} ${dayjs(
                      props.InsertDate
                    ).format("DD.MM.YYYY HH:mm")}`}</Text>
                </Col>

                {props?.Station?.Name && (
                  <Col xs={24} >
                    <Text className="!font-bold !text-black !text-xs">
                      {props?.Station?.Name || ""}
                    </Text>
                  </Col>
                )}
                <Col className="!flex" xs={24} >
                  <Divider className="!my-2 !mx-0" />
                </Col>
                {filter?.StatusIds.includes(4) && (
                  <>
                    <Col span={24}>
                      <Tag color={determineTransactionStatus(props.StatusId)}>
                        {props?.Status?.Name}
                      </Tag>
                    </Col>
                  </>
                )}

                <Col xs={24} className="!flex">
                  <Text className="!font-bold ">
                    {`${props.DesiredTotalConcrete + "m³"}-${props?.Product?.Name || ""
                      }`}
                  </Text>
                </Col>
                <Col xs={24} className="!flex gap-1 !flex-wrap">
                  <Text className=" ">
                    {`${props?.ConcreteLocation?.Name || ""}-${props?.ConsistencyClass?.Name || ""
                      }`}
                  </Text>
                  {props?.TransactionRequestConcreteOption?.map(
                    (item: any, index: number) => {
                      return (
                        <span>
                          <Tag color="green">{item.ConcreteOption?.Name}</Tag>
                        </span>
                      );
                    }
                  )}
                </Col>
                <Col xs={24} className="!flex">
                  <Text className=" ">{`${props?.CarType?.Name || ""}`}</Text>
                </Col>
                {props?.TransactionRequestPompType?.length > 0 && (
                  <Col xs={24} className="!flex gap-1 flex-wrap">
                    {props?.TransactionRequestPompType?.map((item: any) => {
                      return (
                        <span>
                          <Tag color="blue">{item?.PompType?.Name || ""}</Tag>
                        </span>
                      );
                    })}
                  </Col>
                )}
              </Row>
            </Col>
          </Row>
        </Card>
      </Col>
      <Modal
        title={"Zaman ve İstasyonu Belirle"}
        footer={false}
        open={isShowDetermineDateTime}
        onCancel={() => {
          setIsShowDetermineDateTime(false);
        }}
      >
        <DetermineDateTime
          selectedRecord={props}
          type="request"
          onFinish={() => {
            setIsShowDetermineDateTime(false);
          }}
        />
      </Modal>
      <Modal
        title={"İsteği İptal Et"}
        footer={false}
        open={isShowCancelRequestModal}
        onCancel={() => {
          setIsShowCancelRequestModal(false);
        }}
      >
        <ApproveCancelRequest
          selectedRecord={props}
          onFinish={() => {
            setIsShowCancelRequestModal(false);
          }}
          type="transactionRequet"
        />
      </Modal>
    </>
  );
};

export default NewRequestItem;
