import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";

import { MazakaButton } from "apps/Common/MazakaButton";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { cancelTransactionWithPut, updateContractProductTransactionWithPatch } from "../../../../Services";
import { openNotificationWithIcon } from "helpers/Notifications";

interface ChangeTransactionStatusProps {
  onFinish?: any;
  selectedRecord?: any;
  type: "transactionRequet" | "contractProductTransaction"
}

const ApproveCancelRequest: FC<ChangeTransactionStatusProps> = ({
  onFinish,
  selectedRecord,
  type
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { userInfo } = useSelector((state: RootState) => state.account);
  const queryClient = useQueryClient();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    try {


      if (type === "contractProductTransaction") {
        const data = {
          Id: selectedRecord?.Id,
          Patch: [
            { Path: "StatusId", Value: "5" },
            { Path: "CanceledUserId", Value: userInfo?.Id },
            { Path: "CanceledNote", Value: formValues["CanceledNote"] || "" },
            { Path: "ContractId", Value: selectedRecord?.ContractId || "********-0000-0000-0000-************" },
            { Path: "StationId", Value: selectedRecord?.StationId || "" },
            { Path: "DocumentNo", Value: selectedRecord?.DocumentNo || "" },
          ],
        }
        await updateContractProductTransactionWithPatch(data);
        queryClient.resetQueries({
          queryKey: endpoints.getContractProductTransaction,
          exact: false,
        });
      }
      else {
        formValues["CanceledUserId"] = userInfo.Id;
        formValues["Id"] = selectedRecord?.Id;
        await cancelTransactionWithPut(formValues);
        queryClient.resetQueries({
          queryKey: endpoints.getTransactionRequestListFilter,
          exact: false,
        });
      }

      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      openNotificationWithIcon("success", "İşlem Başarılı");

      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ChangeTransactionStatus");
    }
  };

  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 10]}>
            <MazakaTextArea
              name="CanceledNote"
              label={"Açıklama"}
              placeholder="Açıklama"
              xs={24}
              className="!m-0"
              rules={[
                {
                  required: true,

                  message: "",
                },
              ]}
            />
            <Col xs={24} className="!flex justify-end">
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                İptal Et
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ApproveCancelRequest;
