import { Col, Drawer, Row, Space, Spin, Switch, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import MazakaCard from "apps/Common/MazakaCart";
import CalendarIndex from "./components/Calendar/CalendarIndex";
import RequestIndex from "./components/NewRequest/RequestIndex";
import { lazy, Suspense, useState } from "react";

import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import {
  handleResetFilterTrasaction,
  handleSetCalanderHistories,
} from "apps/Plan/ClientSideStates";
import { RootState } from "store/Reducers";
import { useDispatch, useSelector } from "react-redux";

import TopFilterOptions from "./components/TopFilterOptions";
import ViewTabModes from "./components/ViewTabModes";
import TransferErpButton from "./components/Calendar/Components/TransferErpButton";
const DetailsFilter = lazy(() => import("./components/DetailsFilter"));
const EventTableDetails = lazy(
  () => import("./components/Calendar/Components/EventTableDetails")
);

const PlanIndex = () => {
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
  const {
    operationTransactionFilter,
    operationViewMode,
    isShowedCalanderHistories,
  } = useSelector((state: RootState) => state.transactions);
  const { Text } = Typography;
  const dispatch = useDispatch();

  return (
    <>
      <MazakaLayout
        title={"Santral(Operasyonlar)"}
        headDescription={
          "Bu sayfada onaylanmış tüm istekleri görüp plan aşamalarını yönetebilirsiniz"
        }
      >
        <Row gutter={[10, 10]} className="!mt-4">
          <Col span={24} className="!flex justify-between">
            <ViewTabModes />
            <Space>
              <div>
                <Text>Geçmişi Göster</Text>
              </div>
              <div>
                <Switch
                  value={isShowedCalanderHistories}
                  onChange={(status) => {
                    dispatch(handleSetCalanderHistories({ status }));
                  }}
                />
              </div>
              <TransferErpButton/>
            </Space>
          </Col>

          <>
            <Col span={24}>
              <Row gutter={[20, 10]}>
                <Col xs={24} lg={6}>
                  <TopFilterOptions />
                </Col>
                {/* <Col xs={24} lg={15}>
                  <StationOccupancyRateChart />
                </Col> */}
                <Col
                  xs={24}
                  lg={18}
                  className="!flex justify-end gap-2 items-start"
                >
                  <MazakaDetailsFilterButton
                    setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
                  />
                  {Object.entries(operationTransactionFilter).length > 6 && (
                    <>
                      <MazakaClearFilterButton
                        type="operationTransactionFilter"
                        actionFunk={handleResetFilterTrasaction}
                      />
                    </>
                  )}
                </Col>
              </Row>
            </Col>

            <Col span={24}>
              <Row gutter={[0, 0]}>
                <MazakaCard
                  xs={24}
                  md={12}
                  lg={6}
                  gutter={[0, 10]}
                  bodyStyle={{ padding: 0 }}
                >
                  <RequestIndex />
                </MazakaCard>
                <MazakaCard bodyStyle={{ padding: 10 }} xs={24} md={12} lg={18}>
                  {operationViewMode === "calendar" ? (
                    <>
                      <CalendarIndex />
                    </>
                  ) : (
                    <Suspense fallback={<Spin />}>
                      <EventTableDetails/>
                    </Suspense>
                  )}
                </MazakaCard>
              </Row>
            </Col>

            <Drawer
              title="Detaylı Filtre"
              open={isShowFilterDetailsDrawer}
              onClose={() => {
                setIsShowFilterDetailsDrawer(false);
              }}
            >
              <>
                {isShowFilterDetailsDrawer && (
                  <Suspense fallback={<Spin />}>
                    <DetailsFilter
                      onFinish={() => {
                        setIsShowFilterDetailsDrawer(false);
                      }}
                    />
                  </Suspense>
                )}
              </>
            </Drawer>
          </>
        </Row>
      </MazakaLayout>
    </>
  );
};

export default PlanIndex;
