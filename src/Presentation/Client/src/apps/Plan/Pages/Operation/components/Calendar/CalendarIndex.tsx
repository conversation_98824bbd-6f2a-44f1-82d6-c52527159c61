import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import interactionPlugin, { Draggable } from "@fullcalendar/interaction";
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import { lazy, Suspense, useEffect, useRef, useState } from "react";
import { Col, Spin, Typography } from "antd";
import trLocale from "@fullcalendar/core/locales/tr";
import { updateTransactionWithPatch } from "apps/Plan/Services";
import { openNotificationWithIcon } from "helpers/Notifications";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { useGetTransactionRequests } from "apps/Plan/ServerSideStates";
import dayjs from "dayjs";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useLocation } from "react-router-dom";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
const EventContent = lazy(
  () => import("./Components/EventContent")
);
const EventTableDetails= lazy(
  () => import("./Components/EventTableDetails")
);

const CalendarIndex = () => {
  const calendarRef = useRef<FullCalendar>(null);
  const queryClient = useQueryClient();
  const [viewType, setViewType] = useState("");
  const [eventList, setEventList] = useState<any[]>([]);
  const { calendarFilter, operationTransactionFilter,isShowedCalanderHistories } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();
  
  const transactions = useGetTransactionRequests(calendarFilter);
  const [isShowTableDetails, setIsShowTableDetails] = useState(false);
  useEffect(() => {
    let transactionList = transactions.data ? transactions.data.Data : [];
    
    // Check if we need to filter for today's date
    const filteredTransactionList = isShowedCalanderHistories
      ? transactionList
      : transactionList.filter((item: any) => {
          const dateTime = item.ApprovedDateTime;
          const approvedDate = dayjs(dateTime).format("YYYY-MM-DD");
          const todayDate = dayjs().format("YYYY-MM-DD");
          if(viewType !=="timeGridDay")
          {

            return approvedDate >= todayDate;
          }
          return item
          
        });
  
    setEventList(
      filteredTransactionList.map((item: any, index: number) => {
        const dateTime = item.ApprovedDateTime;
        const approvedDate = dayjs(dateTime).format("YYYY-MM-DD");
        const approvedStartTime = dayjs(dateTime).format("HH:mm");
        const approvedEndTime = dayjs(dateTime).add(2, "hour").add(30, "minute").format("HH:mm");
  
        const startTime = new Date(`${approvedDate}T${approvedStartTime}`);
        const endTime = new Date(`${approvedDate}T${approvedEndTime}`);
        
        return {
          id: item.Id || Math.random() * 1000,
          title: `${item?.Building?.Name}, ${item.DesiredTotalConcrete} / ${item?.Product?.Name}`,
          start: startTime,
          end: endTime,
          extendedProps: { originalItem: item },
          index: index,
        };
      })
    );
  }, [transactions.data, transactions.isFetching, isShowedCalanderHistories]);
  

  const handleEventReceive = (info: any) => {
    const { event } = info;
    let dateTime = dayjs(event.start);
    
    const transactionId = event.id;
    if (viewType === "dayGridMonth") {
      let time = event.title;
      let date = dateTime.format("YYYY-MM-DD");
      let finalDateTime = `${date}T${time}`;
      dateTime = dayjs(finalDateTime);
    }

    if (transactionId && dateTime) {
      updateTransaction(transactionId, dateTime);
    }
  };

  const handleEventDrop = (info: any) => {
    const { event } = info;
    let dateTime = dayjs(event.start);
    let transactionId = event.extendedProps.originalItem?.Id;
    const currentDateTime = event.extendedProps.originalItem?.ApprovedDateTime;

    if (viewType === "dayGridMonth" || viewType === "timeGridWeek") {
      let time = dayjs(currentDateTime).format("HH:mm");
      let date = dateTime.format("YYYY-MM-DD");
      let finalDateTime = date + "T" + time;
      dateTime = dayjs(finalDateTime);
    }

    if (transactionId && dateTime) {
      updateTransaction(transactionId, dateTime);
    }
  };

  const updateTransaction = async (transactionId: string, dateTime: any) => {
    try {
      const finalDate = dayjs(dateTime).format("YYYY-MM-DDTHH:mm");
      const data = {
        Id: transactionId,
        patch: [
          { path: "StatusId", value: "4" },
          { path: "ApprovedDateTime", value: finalDate },
        ],
      };

      await updateTransactionWithPatch(data);
      openNotificationWithIcon("success", "İşlem Başarılı");
      queryClient.resetQueries({
        queryKey: endpoints.getTransactionRequestListFilter,
      });
    } catch (error: any) {
      showServiceErrorMessage(error, {}, "Update Transaction request", true);
    }
  };

  useEffect(() => {
    const externalEventsEl = document.getElementById("external-events");

    if (externalEventsEl) {
      new Draggable(externalEventsEl, {
        itemSelector: ".request-dragable",
        eventData(eventEl: any) {
          const id = eventEl.querySelector("#item-id")?.innerText || "";
          const desiredDateTime =
            eventEl.querySelector("#item-desiredDateTime")?.innerText || "";
          const desiredTime = dayjs(desiredDateTime).format("HH:mm");

          return { title: desiredTime, id };
        },
      });
    } else {
      transactions.refetch();
    }
  }, [transactions.data, transactions.isFetching,operationTransactionFilter]);

  const style = `
 ${
   viewType === "timeGridWeek"
     ? ".fc-event { width: 154px !important; height: 100% !important}"
     : viewType === "dayGridMonth"
     ? ".fc-event { width: 163px !important; height: 100% !important}"
     : ".fc-event { width: 174px !important; height: 100% !important}"
 }
 ${isShowTableDetails ? ".fc-more-popover{display:none !important} " : ""}
 `;

  const [selectedRecord, setSelectedRecord] = useState<null | any>(null);
  const { Text } = Typography;
  const location = useLocation();

  useEffect(() => {
    queryClient.resetQueries({
      queryKey: endpoints.getTransactionRequestListFilter,
    });
  }, [location]);



  return (
    <Col span={24} >
      <FullCalendar
        ref={calendarRef}
        plugins={[dayGridPlugin, interactionPlugin, timeGridPlugin, listPlugin]}
        initialView="dayGridMonth"
        events={eventList}
        editable={true}
        slotMinTime="06:00:00"
        height="auto"
        droppable={true}
        locale={trLocale}
        
        eventDrop={handleEventDrop}
        eventReceive={handleEventReceive}
        datesSet={(data) => {
          setViewType(data.view.type);
          let startDate = dayjs(data.start).format("YYYY-MM-DD");
          let endDate = dayjs(data.end).format("YYYY-MM-DD");
          let currentFilter = { ...calendarFilter }; 
          currentFilter["ApprovedStartDate"] = startDate;
          currentFilter["ApprovedEndDate"] = endDate;

          dispatch(
            hanldleSetTranasactionFilter({
              filter: currentFilter,
              type: "calendarFilter",
            })
          );
        }}
        eventDidMount={(info) => {
          if (
            info.event.extendedProps?.originalItem?.StatusId === 5 ||
            info.event.extendedProps?.originalItem?.StatusId === 6
          ) {
            info.el.style.opacity = "0.5";
          }
        }}
        eventAllow={(dropInfo, draggedEvent: any) => {
        let data = draggedEvent.extendedProps?.originalItem
        if (dayjs(dropInfo.start).isBefore(dayjs(), "day")) {
          return false;
        }
     
          if (
            data?.StatusId === 5 ||
            data?.StatusId === 6
          ) {
            return false;
          }
         
         
          return true;
        }}
        eventMaxStack={viewType === "timeGridWeek" ? 1 : 6}
        eventContent={({ event }) => {
          const { originalItem } = event.extendedProps;
          return (
            <>
            {
              eventList?.length>0&&
              <Suspense fallback={<Spin/>}>
              <EventContent
                event={event}
                itemInfos={originalItem}
                viewType={viewType}
                setSelectedRecord={setSelectedRecord}
                setIsShowTableDetails={setIsShowTableDetails}
              />
              </Suspense>
            }
            </>
          );
        }}
        dayMaxEvents={true}
        dayMaxEventRows={3}
        headerToolbar={{
          left: "prev,next today",
          center: "title",
          right: "dayGridMonth,timeGridWeek,timeGridDay,listWeek",
        }}
      />

      <style>{style}</style>
      <MazakaDrawer
        title={
          <>
            <div className="!flex gap-2">
              <Text className="!font-bold !text-xl">
                {selectedRecord?.Building?.Name || ""}
              </Text>
              <Text className="!font-bold !text-xl">
                {selectedRecord?.ApprovedDateTime
                  ? `${dayjs(selectedRecord?.ApprovedDateTime).format(
                      "DD.MM.YYYY"
                    )}`
                  : ""}
              </Text>
              <Text className="!font-bold !text-xl">
                {selectedRecord?.ApprovedDateTime
                  ? `${dayjs(selectedRecord?.ApprovedDateTime).format("HH:mm")}`
                  : ""}
              </Text>
            </div>
          </>
        }
        placement="right"
        open={isShowTableDetails}
        onClose={() => {}}
        toggleVisible={async () => {
          setIsShowTableDetails(false);
          setSelectedRecord(null);
        }}
        layoutType="wide"
      >
        <>
        {
          isShowTableDetails&&
          <Suspense fallback={<Spin/>}>
            <EventTableDetails transactionId={selectedRecord?.Id || ""} />

          </Suspense>
        }
        </>
      </MazakaDrawer>
    </Col>
  );
};

export default CalendarIndex;
