import {
  DeleteOutlined,
  EnvironmentOutlined,
  RedoOutlined,
  RetweetOutlined,
  SyncOutlined,
} from "@ant-design/icons";
import { Card, Modal, Spin, Steps, Tag, Tooltip, Typography } from "antd";
import { FC, lazy, Suspense, useEffect, useState } from "react";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";

import {
  getContractProductTransactionDetails,
  getContractProductTransactionLogListFilter,
} from "apps/Plan/Services";
import dayjs from "dayjs";
import CustomNoData from "apps/Common/CustomNoData";
import { determineTransactionStatus } from "helpers/DetermineTransactionStatusColor";
import ApproveCancelRequest from "../../NewRequest/ApproveCancelRequest";
import RefundedContractProduct from "./RefundedContractProduct";
import ChangeContractProductStatus from "./ChangeContractProductStatus";
import VehicleLiveLocation from "./VehicleLiveLocation";
import { openNotificationWithIcon } from "helpers/Notifications";
import ChangeContractProductDriver from "./ChangeDriver";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints"

const RedirectAnotherStation = lazy(() => import("./RedirectToBuilding"));

interface PlanCarDetailsItemProps {
  record: any;
  selectedTransactionRequest: any;
  contractProductDataList: any[];
  setContractProductDataList: any;
}

const PlanCarDetailsItem: FC<PlanCarDetailsItemProps> = (props) => {
  const queryClient = useQueryClient()
  const [isShowRedirectToStation, setIsShowRedirectToStation] = useState(false);
  const [isShowLiveTrackingModal, setIsShowLiveTracking] = useState(false);
  const [isShowChangeDriverModal, setIsShowChangeDriverModal] = useState(false);
  const [isShowCancelRequestModal, setIsShowCancelRequestModal] =
    useState(false);
  const [isShowRefundedRequestModal, setIsShowRefundedRequestModal] =
    useState(false);

  const [
    isShowChangeContractProductStatusModal,
    setIsShowChangeContractProductStatusModal,
  ] = useState(false);

  const { Text } = Typography;
  const [actions, setActions] = useState<React.ReactNode[]>([]);
  useEffect(() => {
    const newActions: React.ReactNode[] = [];

    if (
      !props.record.PompTypeId &&
      props.record.StatusId !== 4 &&
      props.record.StatusId !== 5
    ) {
      newActions.push(
        <div
          key="cancel"
          className="!flex items-center justify-center gap-2 !h-full"
          onClick={() => {
            setIsShowCancelRequestModal(true);
          }}
        >
          <DeleteOutlined className="!text-red-500" />
          <Text>İptal Et</Text>
        </div>
      );

      newActions.push(
        <div
          key="redirect"
          className="!flex items-center justify-center gap-2 !h-full"
          onClick={() => {
            setIsShowRedirectToStation(true);
          }}
        >
          <RetweetOutlined className="!text-blue-500" />
          <Text>Başka şantiyeye yönlendir</Text>
        </div>
      );
    }

    if (
      !props.record.PompTypeId &&
      props.record.StatusId === 4 &&
      props.selectedTransactionRequest?.Product?.Refundable
    ) {
      newActions.push(
        <div
          key="refund"
          className="!flex items-center justify-center gap-2 !h-full"
          onClick={() => {
            setIsShowRefundedRequestModal(true);
          }}
        >
          <SyncOutlined className="!text-red-500" />
          <Text>İade Al</Text>
        </div>
      );
    }
    if (
      props.record.PompTypeId && (props.record.StatusId !== 4 && props.record.StatusId !== 5)
    ) {
      props.record.ContractId = props.selectedTransactionRequest?.ContractId;
      newActions.push(
        <div
          key="refund"
          className="!flex items-center justify-center gap-2 !h-full"
          onClick={() => {
            setIsShowCancelRequestModal(true);
          }}
        >
          <SyncOutlined className="!text-red-500" />
          <Text>İptal Et</Text>
        </div>
      );
    }

    newActions.push(
      <div
        key="tracking"
        className="!flex items-center justify-center gap-2 !h-full"
        onClick={() => {
          queryClient.resetQueries({
            queryKey: endpoints.getVehicleLocationTracking,
            exact: false,
          });
          setIsShowLiveTracking(true);
        }}
      >
        <EnvironmentOutlined className="!text-blue-500" />
        <Text>Konumunu İzle</Text>
      </div>
    );

    setActions(newActions);
  }, [props.record.StatusId]);

  const [logs, setLogs] = useState<any[]>([]);

  const [isLoding, setIsLoading] = useState(false);
  const getContractDetails = async () => {
    try {
      const response = await getContractProductTransactionDetails(
        props.record?.Id
      );

      const existingData: any[] = props.contractProductDataList;
      const updatedData = [...existingData];
      const findItemIndex = updatedData.findIndex(
        (item: any) => item.Id === props.record?.Id
      );

      if (findItemIndex !== -1) {
        updatedData[findItemIndex] = response?.Value;

        props.setContractProductDataList(updatedData);
      }

      openNotificationWithIcon("success", "İşlem Başarılı");
    } catch (error) {
      showServiceErrorMessage(error, null, "", true);
    }
  };
  const getContractProductTransactionLogs = async () => {
    setIsLoading(true);
    try {
      const response: any = await getContractProductTransactionLogListFilter({
        ContractProductTransactionId: props.record.Id,
        TransactionRequestId: props.record.TransactionRequestId,
        SortProperty: "InsertDate",
        SortType: "asc",
      });
      if (response?.Data) {
        setLogs(response.Data);
      }
    } catch (error: any) {
      showServiceErrorMessage(
        error,
        {},
        "ContractProductTranasactionLogs",
        true
      );
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (props.record.Id) {
      getContractProductTransactionLogs();
    }
  }, [props.record.Id]);

  return (
    <>
      <Card
        className={props.record?.RefundAmount ? "!border-red-500" : ""}
        bodyStyle={{ height: "350px", overflow: "auto" }}
        title={
          <>
            <div className="!flex justify-between px-1 !h-[70px]">
              {props.record?.PompTypeId ? (
                <>
                  <div className="!flex items-center">
                    <Text>{props.record?.PompType?.Name || ""}</Text>
                  </div>
                </>
              ) : (
                <>
                  <div>
                    <div className="!flex items-center gap-1">
                      <Text className="!font-bol ">
                        {props.record.SendingAmount}m³
                      </Text>
                      <Text className="!font-bold !text-xs">
                        {props.selectedTransactionRequest?.Product?.Name || ""}
                      </Text>
                    </div>
                    {props.record?.RefundAmount && (
                      <div>
                        <Text className="!font-bol !text-red-500 ">
                          {props.record?.RefundAmount}m³
                        </Text>
                      </div>
                    )}
                    <div className="!flex gap-1 items-center">
                      <Text className="!text-xs">
                        {props?.record?.DocumentNo}
                      </Text>
                      <span
                        className="!cursor-pointer"
                        onClick={getContractDetails}
                      >
                        <Tooltip title={"İrsaliye bilgileri yenile"}>
                          <RedoOutlined className="!text-indigo-500" />
                        </Tooltip>
                      </span>
                    </div>
                  </div>
                </>
              )}

              <div>
                <div className="!flex gap-1">
                  <Text>{props?.record?.Vehicle?.Plate}</Text>
                  <Tag
                    color={determineTransactionStatus(props?.record?.StatusId)}
                    className={`${props?.record?.StatusId !== 4 &&
                      props?.record?.StatusId !== 5
                      ? "!cursor-pointer"
                      : "!cursor-text"
                      }`}
                    onClick={() => {
                      if (
                        props?.record?.StatusId !== 4 &&
                        props?.record?.StatusId !== 5
                      ) {
                        setIsShowChangeContractProductStatusModal(true);
                      }
                    }}
                  >
                    {props.record.Status.Name}
                  </Tag>
                </div>
                <div
                  className={`${props?.record?.StatusId === 1 ? "cursor-pointer" : ""
                    }`}
                  onClick={() => {
                    if (props?.record?.StatusId === 1) {
                      setIsShowChangeDriverModal(true);
                    }
                  }}
                >
                  <Text>{`${props?.record?.Driver?.Name || ""} ${props?.record?.Driver?.Surname || ""}`}</Text>
                </div>
              </div>
            </div>
          </>
        }
        actions={actions}
      >
        <div>
          <Text className="!font-bold ">Araç Hareketleri</Text>
        </div>
        {isLoding ? (
          <Spin />
        ) : (
          <>
            {logs.length > 0 ? (
              <>
                <Steps
                  progressDot
                  current={logs?.length - 1 || 0}
                  direction="vertical"
                  items={
                    logs.map((item: any) => {
                      return {
                        title: item.Description,
                        description: (
                          <div className="!flex gap-2">
                            <Text className="!text-gray-400">
                              {dayjs(item.InsertDate).format("DD.MM.YYYY")}
                            </Text>
                            <Text className="!text-gray-400">
                              {dayjs(item.InsertDate).format("HH:mm")}
                            </Text>
                          </div>
                        ),
                      };
                    }) || []
                  }
                />
              </>
            ) : (
              <>
                <CustomNoData />
              </>
            )}
          </>
        )}
      </Card>
      <Modal
        title={"Araçı Yönlendir"}
        footer={false}
        open={isShowRedirectToStation}
        onCancel={() => {
          setIsShowRedirectToStation(false);
        }}
      >
        <>
          {isShowRedirectToStation && (
            <Suspense fallback={<Spin />}>
              <RedirectAnotherStation
                selectedRecord={props.record}
                onFinish={() => {
                  setIsShowRedirectToStation(false);
                }}
              />
            </Suspense>
          )}
        </>
      </Modal>

      <Modal
        title={"İsteği İptal Et"}
        footer={false}
        open={isShowCancelRequestModal}
        onCancel={() => {
          setIsShowCancelRequestModal(false);
        }}
      >
        <ApproveCancelRequest
          selectedRecord={props.record}
          type="contractProductTransaction"
          onFinish={() => {
            setIsShowCancelRequestModal(false);
          }}
        />
      </Modal>
      <Modal
        title={"İade Alma"}
        footer={false}
        open={isShowRefundedRequestModal}
        onCancel={() => {
          setIsShowRefundedRequestModal(false);
        }}
      >
        <RefundedContractProduct
          selectedRecord={props.record}
          onFinish={() => {
            setIsShowRefundedRequestModal(false);
          }}
        />
      </Modal>
      <Modal
        title={"Durumu Güncelle"}
        footer={false}
        open={isShowChangeContractProductStatusModal}
        onCancel={() => {
          setIsShowChangeContractProductStatusModal(false);
        }}
      >
        <ChangeContractProductStatus
          selectedRecord={props.record}
          onFinish={() => {
            setIsShowChangeContractProductStatusModal(false);
          }}
        />
      </Modal>
      <Modal
        title=""
        width={"70%"}
        open={isShowLiveTrackingModal}
        onCancel={() => {
          setIsShowLiveTracking(false);
        }}
        footer={false}
      >
        <VehicleLiveLocation plate={props.record?.Vehicle?.Plate} />
      </Modal>

      <Modal
        title={"Şoför Değiştirme"}
        footer={false}
        open={isShowChangeDriverModal}
        onCancel={() => {
          setIsShowChangeDriverModal(false);
        }}
      >
        <ChangeContractProductDriver
          selectedRecord={props.record}
          onFinish={() => {
            setIsShowChangeDriverModal(false);
          }}
        />
      </Modal>

    </>
  );
};

export default PlanCarDetailsItem;
