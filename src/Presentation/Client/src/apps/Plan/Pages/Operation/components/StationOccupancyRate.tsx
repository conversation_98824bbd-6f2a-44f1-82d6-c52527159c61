import { Col,  Progress, Row, Skeleton, Typography } from "antd";
import { useEffect, useState } from "react";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { getStationOccupancyRate } from "apps/Report/Services";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const StationOccupancyRateChart = () => {
  const { Text } = Typography;
  const [isLoading, setIsLoading] = useState(false);
  const [dataList, setDataList] = useState<any[]>([]);
  const { calendarFilter,isShowedCalanderHistories} = useSelector((state:RootState)=>state.transactions)

  const getCompanyRequests = async () => {
    setIsLoading(true);
    try {
      const filter = {
        StartDate:isShowedCalanderHistories?calendarFilter?.ApprovedStartDate:dayjs().format("YYYY-MM-DD"),
        EndDate:isShowedCalanderHistories?calendarFilter?.ApprovedEndDate:dayjs().format("YYYY-MM-DD") ,
      };

      const response: any = await getStationOccupancyRate(filter);
      if (response?.Value) {
        setDataList(response.Value);
      }
    } catch (err: any) {
      showServiceErrorMessage(err, {}, "StationOccupancyRate", true);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    getCompanyRequests();
  }, [calendarFilter,isShowedCalanderHistories]);

  return (
    <Skeleton loading={isLoading}>
      {
        !isLoading&& dataList.length<=0?<>
       
        </>:<>
      <Col span={24}>
        <Row>
          <Col xs={24}>
            <Text className="!text-xs">
              Santralların Doluluk Oranı 
            </Text>
          </Col>
          <Col xs={24} className="!flex gap-2">
            {dataList.map((item) => {
              return (
                <Row>
                  <div>
                    <Text className="!text-[10px] !font-bold">
                      {item.Station}
                    </Text>
                  </div>
                  <div className="!w-full">
                    <Progress
                      format={(percent) => `${percent} İstek`}
                      percent={item?.TotalRequest}
                      size="small"
                    />
                  </div>
                </Row>
              );
            })}
          </Col>
        </Row>
      </Col>
        </>
      }
    </Skeleton>
  );
};

export default StationOccupancyRateChart;
