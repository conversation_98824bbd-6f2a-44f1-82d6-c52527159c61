import { Tabs, TabsProps } from "antd";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const StatusTabModes = () => {
  const { operationTransactionFilter: filter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();
  const items: TabsProps["items"] = [
    {
      key: "approved",
      label: "Planlanacak",
      children: <></>,
    },
    {
      key: "planend",
      label: "Planlanmış",
      children: <></>,
    },
    
  ];
  const [activeKey, setActiveKey] = useState("approved");
  const handleOnChange = (value: string) => {
    let currentFilter = { ...filter };
    if (value === "approved") {
      currentFilter["StatusIds"] = [2];
   
    } 
   
    
    else if (value === "planend") {
      currentFilter["StatusIds"] = [4,5,6];
    } 
    setActiveKey(value);
    dispatch(
      hanldleSetTranasactionFilter({
        filter: currentFilter,
        type: "operationTransactionFilter",
      })
    );
   
  };
  return (
    <>
      <Tabs activeKey={activeKey} items={items} onChange={handleOnChange} />
    </>
  );
};

export default StatusTabModes;
