import { Col, Skeleton } from "antd";
import { useGetVehicleLocationLiveTracking } from "apps/Plan/ServerSideStates";
import { FC, useEffect, useState } from "react";
import CustomGoogleMap from "apps/Common/GoogleMap";
import { openNotificationWithIcon } from "helpers/Notifications";

interface VehicleLiveLocationProps {
  plate: string;
}
const VehicleLiveLocation: FC<VehicleLiveLocationProps> = ({ plate }) => {
  const vehicleLocations = useGetVehicleLocationLiveTracking(plate);
  const [marker, setMarker] = useState({
    lat: null,
    lng:null
  });

  useEffect(() => {
    if(vehicleLocations.isError)
      {
        openNotificationWithIcon("error","Araç Konum Bilgileri alınamadı")
      }
      else{
      let data = vehicleLocations?.data?.Value;
      setMarker({
        lat: data?.Location?.Lat,
        lng: data?.Location?.Lon,
      });

    }
   
  }, [plate,vehicleLocations.data,vehicleLocations.isError]);
  return (
    <Col xs={24} className="!mt-6">
      <Skeleton
        loading={vehicleLocations.isLoading }
      >
        {
          marker.lat&&marker.lng&&
        <CustomGoogleMap
          type="vehicleTracking"
          marker={marker}
          setMarker={setMarker}
        />
        }

      </Skeleton>
    </Col>
  );
};

export default VehicleLiveLocation;
