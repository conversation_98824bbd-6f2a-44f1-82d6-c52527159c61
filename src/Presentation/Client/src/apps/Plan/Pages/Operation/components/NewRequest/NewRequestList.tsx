import { Col, Pagination, Row, Skeleton } from "antd";
import NewRequestItem from "./NewRequestItem";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetTransactionRequests } from "apps/Plan/ServerSideStates";
import { RequestTransactionAndDetails } from "apps/Plan/Models";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";

const NewRequestList = () => {
  const { operationTransactionFilter: filter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();
  const approvedTransactions = useGetTransactionRequests(filter);
  const handleChangePagination = (pageNum: number, pageSize: number) => {
   
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(
      hanldleSetTranasactionFilter({
        filter: newFilter,
        type: "operationTransactionFilter",
      })
    );
  };
  return (
    <>
      <Col span={24} className="">
        <Skeleton
          loading={
            approvedTransactions.isLoading || approvedTransactions.isFetching
          }
        >
          <Row id="external-events" gutter={[10, 5]}>
            {approvedTransactions.data
              ? approvedTransactions.data.Data.map(
                  (item: RequestTransactionAndDetails) => {
                    return (
                      <>
                        <NewRequestItem {...item} />
                      </>
                    );
                  }
                )
              : []}
            {approvedTransactions.data?.Data?.length > 0 && (
              <>
                <Col span={24} className="!flex justify-end !my-4">
                  <Pagination
                    className={"!px-0"}
                    total={approvedTransactions.data?.FilteredCount || 0}
                    onChange={handleChangePagination}
                    current={approvedTransactions.data?.PageIndex || 0}
                    pageSize={approvedTransactions.data?.PageSize || 0}
                    showLessItems={true}
                    size={"small"}
                    showSizeChanger={true}
                    locale={{ items_per_page: "" }}
                    showTotal={(e) => `${e}`}
                  />
                </Col>
              </>
            )}
          </Row>
        </Skeleton>
      </Col>
    </>
  );
};

export default NewRequestList;
