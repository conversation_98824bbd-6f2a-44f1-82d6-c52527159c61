import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import { updateTransactionWithPatch } from "apps/Plan/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";

interface RedirectAnotherStationProps {
  onFinish?: any;
  selectedRecord?: any;
}

const ChangeStatusTransaction: FC<RedirectAnotherStationProps> = ({
  onFinish,
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { userInfo } = useSelector((state: RootState) => state.account);
  const queryClient = useQueryClient();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    let data = {
      Id: selectedRecord.Id,
      patch: [{ path: "StatusId", value: String(formValues["StatusId"]) }],
    };

    try {
      console.log(data);
      await updateTransactionWithPatch(data);
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getTransactionRequestListFilter,
        exact: false,
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ChangeTransactionStatus");
    }
  };

  let options = [];

  // userInfo boş değilse ve belirtilen RoleId'ye sahipse
  if (userInfo && (userInfo.RoleId === "439aeada-02d0-4962-9dfd-bc41363461a3" || (userInfo.RoleId === "5351cc0b-20d7-4dcc-8fba-90c797b3f5b8"))) {
    options = [
      { label: "Onay Bekliyor", value: "1" },
      { label: "Planlanacak", value: "2" },
      { label: "Planlamada", value: "3" },
      { label: "Planlandı", value: "4" },
      { label: "Döküm Devam Ediyor", value: "5" },
      { label: "Tamamlandı", value: "6" },
      { label: "İptal Talebi", value: "7" },
      { label: "Reddedildi", value: "8" },
      { label: "İptal Edildi", value: "9" },
    ];
  } else {
    options = [
      { label: "İptal Et", value: "7" },
      { label: "Planlanma", value: "2" },
    ];
  }

  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          initialValues={{ Active: true }}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
            <MazakaSelect
              name={"StatusId"}
              placeholder={"Durum"}
              xs={24}
              className="!m-0"
              options={options}
              rules={[{ required: true, message: "" }]}
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ChangeStatusTransaction;
