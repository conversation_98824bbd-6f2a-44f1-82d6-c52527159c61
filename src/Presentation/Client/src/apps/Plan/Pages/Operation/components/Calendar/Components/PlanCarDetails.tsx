import { Col, Modal, Row, Spin } from "antd";

import MazakaCard from "apps/Common/MazakaCart";
import { FC, Fragment, lazy, Suspense, useEffect, useState } from "react";
import { useGetContractProductTransactions } from "apps/Plan/ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { MazakaButton } from "apps/Common/MazakaButton";
import CustomNoData from "apps/Common/CustomNoData";
import { openNotificationWithIcon } from "helpers/Notifications";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { CloseOutlined } from "@ant-design/icons";
import AddOrUpdateRequest from "apps/Plan/Pages/WaitApproved/Components/AddTransacitonRequest";
import AddTransacitonRequest from "apps/Plan/Pages/WaitApproved/Components/AddTransacitonRequest";
const DocumentNoTableList = lazy(() => import("./DocumentTableList"));
const PlanCarDetailsItem = lazy(() => import("./PlanCarDetailsItem"));

interface ContractProductTranasactiionProps {
  transactionRequestId: string;
  selectedTransactionRequest: any;
  accountingCode: string;
  onRefreshComponents?: () => void;

}

const ContractProductTranasactiion: FC<ContractProductTranasactiionProps> = ({
  transactionRequestId,
  selectedTransactionRequest,
  accountingCode,
  onRefreshComponents
}) => {
  const contractProductTransactions = useGetContractProductTransactions({
    PageSize: -1,
    TransactionRequestId: transactionRequestId,
    sortProperty: "InsertDate",
    sortType: "desc",
    IncludeProperties: [
      "TransactionRequest.Product",
      "PompType",
      "Status",
      "Driver",
      "Vehicle.Driver",
      "TransactionRequest",
    ],
  });
  const [contractProductDataList, setContractProductDataList] = useState([])

  useEffect(() => {
    setContractProductDataList(contractProductTransactions.data?.Data || [])
  }, [contractProductTransactions.data])


  const [isValidateLoading, setIsValidateLoading] = useState(false);

  const [isShowDocumentList, setIsShowDocumentList] = useState(false);

  const [isShowAddPompList, handleAddPompList] = useState(false);

  const handleTransactionUpdated = async () => {
    try {
      // Kendi state'ini güncelle
      queryClient.invalidateQueries(['contractProductTransactions']);
      await contractProductTransactions.refetch();

      // Parent component'teki tüm component'leri refresh et
      onRefreshComponents && onRefreshComponents();

    } catch (error) {
      console.error('State güncellenirken hata:', error);
    }
  };


  const handleShowDocumentList = async (status: boolean) => {
    if (!accountingCode) {
      openNotificationWithIcon("error", "Bu firmada cari kod alanı boş");
      return false;
    } else {
      setIsValidateLoading(true);
      try {
        setIsShowDocumentList(true);
        setIsUsedHours(status);
        queryClient.resetQueries({
          queryKey: endpoints.getDocumentNoList,
          exact: false,
        });
      } catch (error) {
        showServiceErrorMessage(error, {}, "ValidateDocumentNumber", true);
      } finally {
        setIsValidateLoading(false);
      }
    }
  };
  const queryClient = useQueryClient();
  const [isUsedHours, setIsUsedHours] = useState(false);



  return (
    <>
      <Col span={24}>
        <Row gutter={[10, 10]}>
          <Col span={24} className="  bg-white" id="add">
            <MazakaCard gutter={[20, 20]} title="Sefer / Dolum Ekle (İrsaliye)">
              <Col span={24} className="!mt-4">
                <Row gutter={[10, 10]}>
                  <Col xs={24}>
                    <Row>
                      <Col xs={24} lg={24} className="!flex gap-2">
                        <MazakaButton
                          loading={isValidateLoading}
                          htmlType="button"
                          onClick={() => {
                            handleShowDocumentList(true);
                          }}
                        >
                          {isValidateLoading
                            ? ""
                            : "Yakındaki İrsaliyeleri Kontrol Et"}
                        </MazakaButton>
                        <MazakaButton
                          loading={isValidateLoading}
                          className="!bg-indigo-500"
                          htmlType="button"
                          onClick={() => {
                            handleShowDocumentList(false);
                          }}
                        >
                          Bugünki İrsaliyeleri Kontrol Et
                        </MazakaButton>
                        <MazakaButton
                          loading={isValidateLoading}
                          className="!bg-black !text-white "
                          htmlType="button"
                          onClick={() => {
                            handleAddPompList(true);
                          }}
                        >
                          Pompa Yönlendir
                        </MazakaButton>



                      </Col>
                      <Col
                        xs={24}
                        lg={12}
                        className="!flex items-end justify-end"
                      >
                        {isShowDocumentList && (
                          <>
                            <MazakaButton
                              htmlType="button"
                              icon={<CloseOutlined />}
                              className="!bg-red-400 !text-white"
                              onClick={() => {
                                setIsShowDocumentList(false);
                              }}
                            >
                              {"İrsaliyeleri Kapat"}
                            </MazakaButton>
                          </>
                        )}
                      </Col>
                    </Row>
                  </Col>
                  {isShowDocumentList && (
                    <>
                      <Col span={24}>
                        <Suspense fallback={<Spin />}>
                          <DocumentNoTableList
                            accountingCode={accountingCode}
                            transactionRequestId={transactionRequestId}
                            setIsShowDocumentList={setIsShowDocumentList}
                            isUsedHours={isUsedHours}
                          />
                        </Suspense>
                      </Col>
                    </>
                  )}
                </Row>
              </Col>
            </MazakaCard>
          </Col>
          <Col span={24}>
            <Row gutter={[16, 16]}>
              {contractProductTransactions.isLoading ? (
                <>
                  <Col xs={24} className="!flex justify-center">
                    <Spin />
                  </Col>
                </>
              ) : (
                <>
                  {contractProductTransactions?.data?.Data?.length <= 0 ? (
                    <MazakaCard className="!z-30">
                      <CustomNoData description="Araç Hareketi Bulunmadı" />
                    </MazakaCard>
                  ) : (
                    <>
                      {contractProductDataList.map(
                        (item: any, index: number) => {
                          return (
                            <Col key={index} xs={24} md={12} lg={8}>
                              <Suspense fallback={<Spin />}>
                                <PlanCarDetailsItem
                                  record={item}
                                  contractProductDataList={contractProductDataList}
                                  setContractProductDataList={setContractProductDataList}
                                  selectedTransactionRequest={
                                    selectedTransactionRequest
                                  }
                                />
                              </Suspense>
                            </Col>
                          );
                        }
                      )
                      }
                    </>
                  )}
                </>
              )}
            </Row>
          </Col>
        </Row>
      </Col>

      <Modal
        title={"Pompa Yönlendirme"}
        footer={false}
        open={isShowAddPompList}
        onCancel={() => {
          handleAddPompList(false);
        }}
      >
        <AddTransacitonRequest
          selectedTransactionRequest={selectedTransactionRequest}
          onTransactionUpdated={handleTransactionUpdated}

          onFinish={() => {
            handleAddPompList(false);
          }}
        />
      </Modal>


    </>
  );
};

export default ContractProductTranasactiion;
