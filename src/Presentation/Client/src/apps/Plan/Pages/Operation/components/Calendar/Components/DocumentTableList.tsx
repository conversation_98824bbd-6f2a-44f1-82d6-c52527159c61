import { ExclamationCircleOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Modal, Table } from "antd";
import {
  useGetContractProductTransactions,
  useGetDocumentNoes,
} from "apps/Plan/ServerSideStates";
import dayjs from "dayjs";
import { openNotificationWithIcon } from "helpers/Notifications";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { FC } from "react";
import endpoints from "apps/Plan/EndPoints";
import { useQueryClient } from "react-query";
import { addContractProductTransaction } from "apps/Plan/Services";
interface DocumentNoItemProps {
  accountingCode: string;
  transactionRequestId: string;
  setIsShowDocumentList: any;
  isUsedHours: boolean;
}

const DocumentNoTableList: FC<DocumentNoItemProps> = ({
  accountingCode,
  transactionRequestId,
  setIsShowDocumentList,
  isUsedHours,
}) => {
  const queryClient = useQueryClient();
  const documents: any = useGetDocumentNoes({
    AccountingCode: accountingCode,
    StartDate: isUsedHours
      ? dayjs().subtract(3, "hour").format("YYYY-MM-DDTHH:mm")
      : dayjs().format("YYYY-MM-DD"),
    EndDate: isUsedHours
      ? dayjs().add(1, "hour").format("YYYY-MM-DDTHH:mm")
      : dayjs().format("YYYY-MM-DD"),
  });
  const contractProducts = useGetContractProductTransactions({
    TransactionRequestId: transactionRequestId,
    PageSize: -1,
  });
  let contractProductsData = contractProducts.data
    ? contractProducts.data.Data
    : [];
  const columns = [
    {
      title: "No",
      render: (text: string, record: any, index: number) => {
        return <span>{index + 1}</span>;
      },
    },
    {
      title: "Şantiye Adı",
      dataIndex: "SantiyeAdi",
    },
    {
      title: "Şube Adı",
      dataIndex: "SubeAdi",
    },
    {
      title: "Şube No",
      dataIndex: "SubeNo",
    },
    {
      title: "İrsaliye No",
      dataIndex: "IrsaliyeNo",
    },
    {
      title: "İrsaliye Miktarı",
      dataIndex: "IrsaliyeMiktar",
    },
    {
      title: "Cari Kodu",
      dataIndex: "CariKodu",
    },
    {
      title: "Şoför",
      dataIndex: "Surucu",
    },
    {
      title: "Plaka",
      dataIndex: "Plaka",
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <div className="!flex items-center justify-center gap-2">
          <Button
            onClick={async () => {
              confirm(record);
            }}
            type="text"
            className="!bg-green-500 !text-white"
          >
            Seç
          </Button>
        </div>
      ),
    },
  ];

  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu irsaliye seçilecektir.Onaylıyor muzunuz ?`,
      okText: "Tamam",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await addContractProductTransaction({
            TransactionRequestId: transactionRequestId,
            DocumentNo: record.IrsaliyeNo,
          });
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getContractProductTransaction,
            exact: false,
          });
          setIsShowDocumentList(false);
        } catch (error: any) {
          showServiceErrorMessage(error, {}, "Start ContractProductLogs", true);
        }
      },
    });
  };

  return (
    <>
      <Table
        columns={columns}
        loading={documents.isLoading || documents.isFetching}
        dataSource={
          documents.data
            ? documents.data.Value.filter((doc: any) => {
                if (contractProductsData?.length > 0) {
                  const existsInContract = contractProductsData.some(
                    (contractProductItem: any) =>
                      contractProductItem.DocumentNo === doc.CariRecNo
                  );

                  return !existsInContract;
                }

                return true;
              })
            : []
        }
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",

          total: documents.data?.FilteredCount || 0,

          pageSize: 20,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
    </>
  );
};

export default DocumentNoTableList;
