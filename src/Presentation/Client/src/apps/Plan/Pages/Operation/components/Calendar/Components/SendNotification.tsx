import { Col, Form, Row, } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC,} from "react";
import endpoints from "apps/Notification/EndPoints";
import { RequestTransactionAndDetails } from "apps/Plan/Models";
import { sendBulkNotification, } from "apps/Notification/Services";
import { useQueryClient } from "react-query";
import { useGetBuildingUsers } from "apps/User/ServerSideStates";
import { openNotificationWithIcon } from "helpers/Notifications";

interface SendNotificationProps {
  onFinish?: any;
  selectedRecord: RequestTransactionAndDetails
}

const SendNotification: FC<SendNotificationProps> = ({
  onFinish,
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form,{submitText:"Gönder"});
  const buildingUser = useGetBuildingUsers({BuildingIds:[selectedRecord["BuildingId"]],PageSize:-1,})
 
  const queryClient = useQueryClient();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    const allData = buildingUser.data ? buildingUser.data.Data : [];
    const users = allData.map((item: any) => item.UserId);
    if(users?.length<=0)
    {
      mazakaForm.setFailed(2000,"")
      openNotificationWithIcon("error","Bu şantiye bağlı kullanıcı bulunamadı")
      return false
    }
  
    
   const data = {
    Title:formValues["Title"],
    Note:formValues["Note"],
    UserIds:users,
   }
  
    try {
     
      const response:any = await sendBulkNotification(data);
      if(response?.Value?.TotalSend)
      {

        openNotificationWithIcon("success",`Toplam gönderilen bildrim sayıs:${response?.Value?.TotalSend}  `)
      }
      if(response?.Value?.TotalTry)
      {

        openNotificationWithIcon("info",`Toplam gönderilecek bildrim sayıs:${response?.Value?.TotalTry}  `)
      }
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      form.resetFields();
      queryClient.resetQueries({
        queryKey: endpoints.getNotificationListFilter,
        exact: false,
      });
      onFinish();
    } catch (error: any) {
      // Handle any error from the notification promises
      showServiceErrorMessage(error, mazakaForm, "notification");
    }
  };
  


  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          initialValues={{ Active: true }}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
           
            <MazakaInput
              xs={24}
              label="Başlık"
              className="!m-0"
              name={"Title"}
              placeholder="Başlık"
              rules={[{ required: true, message: "" }]}
         
            />
          

            <MazakaTextArea
              xs={24}
              label="Açıklama"
              placeholder="Açıklama"
              name="Note"
              className="!m-0"
              rules={[{ required: true, message: "" }]}
             
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default SendNotification;
