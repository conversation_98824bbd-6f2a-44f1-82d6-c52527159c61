import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import {
  updateContractProductTransactionWithPatch,
} from "apps/Plan/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";

interface RedirectAnotherStationProps {
  onFinish?: any;
  selectedRecord?: any;
}

const ChangeContractProductStatus: FC<RedirectAnotherStationProps> = ({
  onFinish,
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  console.log(selectedRecord);
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    let data = {
      Id: selectedRecord.Id,
      Patch: [{ Path: "StatusId", Value: formValues["StatusId"]?.toString() }],
    };

    try {
      await updateContractProductTransactionWithPatch(data);
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getContractProductTransaction,
        exact: false,
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ChangeTransactionStatus");
    }
  };

  let options = [
    { label: "Beklemede", value: 1 },
    { label: "Yolda", value: 2 },
    { label: "Dökülüyor", value: 3 },
    { label: "Tamamlandı", value: 4 },
  ];

  if (selectedRecord.PompTypeId && selectedRecord.PompTypeId > 0) {
    options = [
      { label: "Beklemede", value: 1 },
      { label: "Yolda", value: 2 },
      { label: "Tamamlandı", value: 4 },
    ];
  }

  useEffect(() => {
    form.setFieldValue("StatusId", selectedRecord?.StatusId)
  }, [selectedRecord])

  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          initialValues={{ Active: true }}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
            <MazakaSelect
              name={"StatusId"}
              placeholder={"Durum"}
              xs={24}
              className="!m-0"
              options={options}
              rules={[{ required: true, message: "" }]}
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ChangeContractProductStatus;
