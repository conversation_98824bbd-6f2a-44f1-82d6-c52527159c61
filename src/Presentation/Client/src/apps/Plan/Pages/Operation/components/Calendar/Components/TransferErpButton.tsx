import { MazakaButton } from "apps/Common/MazakaButton";
import { transferERPTransaction } from "apps/Plan/Services";
import { openNotificationWithIcon } from "helpers/Notifications";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { useState } from "react";

const TransferErpButton = () => {
  const [isLoading, setIsLoading] = useState(false);
  const handleTransferERPOrder = async () => {
    setIsLoading(true);
    try {
      const response = await transferERPTransaction();
      openNotificationWithIcon("success", response?.Value);
    } catch (error) {
      showServiceErrorMessage(error, null, "", true);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <MazakaButton
        loading={isLoading}
        disabled={isLoading}
        className="!bg-black !text-white"
        htmlType="button"
        onClick={handleTransferERPOrder}
      >
        <PERSON><PERSON>t ve İş Emirlerini Kontrol Et
      </MazakaButton>
    </>
  );
};

export default TransferErpButton;
