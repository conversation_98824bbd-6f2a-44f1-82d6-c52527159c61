import React, { FC, lazy, Suspense, useEffect, useState } from "react";
import {
  Col,
  Modal,
  Progress,
  Row,
  Spin,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "antd";
import {
  BellOutlined,
  EditOutlined,
  FieldTimeOutlined,
} from "@ant-design/icons";
import PlanCarDetails from "./PlanCarDetails";
import { useGetTransactionRequests } from "apps/Plan/ServerSideStates";
import { SecretText } from "apps/Common/SecretString";
import dayjs from "dayjs";
import { RequestTransactionAndDetails } from "apps/Plan/Models";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { determineTransactionStatus } from "helpers/DetermineTransactionStatusColor";
const DetermineDateTime = lazy(
  () => import("../../NewRequest/DetermineDateTime")
);
const ChangeStatusTransaction = lazy(
  () => import("./ChangeStatusTransactions")
);
const SendNotification = lazy(() => import("./SendNotification"));

interface EventTableDetailsProps {
  transactionId?: string;
}

const EventTableDetails: FC<EventTableDetailsProps> = ({ transactionId }) => {
  const { Text } = Typography;
  const [isShowDetermineDateTime, setIsShowDetermineDateTime] = useState(false);
  const [viewType, setViewType] = useState("");
  const [isShowNotificationModal, setIsShowNoticationModal] = useState(false);
  const { calendarFilter, isShowedCalanderHistories } = useSelector(
    (state: RootState) => state.transactions
  );
  const { userInfo } = useSelector((state: RootState) => state.account);
  const [selectedRecord, setSelectedRecord] =
    useState<null | RequestTransactionAndDetails>(null);

  const columns = [
    {
      title: "Firma",
      dataIndex: ["Building", "Name"],
      key: "CompanyName",
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text className="!font-bold">{value}</Text>
            </div>

            <>
              <div className="!text-primary">
                <SecretText
                  text={record?.Building?.AuditPerson?.PhoneNumber}
                  textType="phone"
                />
              </div>
            </>

            <div>
              <Text>{record?.Building?.Company?.Name}</Text>
            </div>

            <>
              <div className="!text-primary">
                <SecretText
                  text={record?.Building?.Company?.Phone}
                  textType="phone"
                />
              </div>
            </>
          </>
        );
      },
      width: "16%",
    },
    {
      title: "Oluşturan Kişi",
      render: (_: string, record: any) => {
        return (
          <>
            <Text>{`${record?.RequestedPerson?.Name || ""} ${record?.RequestedPerson?.Surname || ""
              }`}</Text>
          </>
        );
      },
      width: "8%",
    },
    {
      title: "İstasyon / Ürün",
      dataIndex: ["Station", "Name"],
      key: "StationName",
      width: "10%",
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{value}</Text>
            </div>
            {/* {record?.Building?.AuditPerson?.PhoneNumber && (
              <>
                <div className="!text-primary">
                  <SecretText
                    text={record?.Building?.AuditPerson?.PhoneNumber}
                    textType="phone"
                  />
                </div>
              </>
            )} */}
            <div>
              <Text>{record?.Product?.Name || ""}</Text>
            </div>
            <div>
              <Text>{record?.ConcreteLocation?.Name || ""}</Text>
            </div>
            <div>
              <Text>{record?.CosistencyClass?.Name || ""}</Text>
            </div>
            <div className="!flex !flex-wrap gap-1">
              {record?.TransactionRequestConcreteOption?.map(
                (item: any, index: number) => {
                  return (
                    <>
                      <Tag color="green">{item.ConcreteOption?.Name}</Tag>
                    </>
                  );
                }
              )}
            </div>
          </>
        );
      },
    },

    {
      title: "Toplam(m³)",
      dataIndex: "DesiredTotalConcrete",
      key: "DesiredTotalConcrete",
      render: (value: string) => {
        return <Text className="!font-bold">{value}</Text>;
      },
      width: "8%",
    },

    {
      title: "Kalan(m³)",
      dataIndex: "TotalConcreteRemaining",
      key: "TotalConcreteRemaining",
      render: (value: string) => {
        return <Text className="!text-red-500">{value || 0}</Text>;
      },
      width: "8%",
    },
    {
      title: "Gerçekleşen(m³)",

      render: (value: string, record: any) => {
        return (
          <Text className="text-green-500">
            {
              <>
                {Math.abs(record?.TotalConcreteSent)}
              </>
            }
          </Text>
        );
      },
      width: "8%",
    },
    {
      title: "Tamamlanan",
      dataIndex: "TotalConcreteSent",
      key: "TotalConcreteSent",
      render: (value: any, record: any) => {
        const totalConcreteRequested = record.DesiredTotalConcrete || 0;
        const totalConcreteSent =
          totalConcreteRequested - (record?.TotalConcreteRemaining || 0);
        const percent =
          totalConcreteRequested > 0 && record?.TotalConcreteRemaining !== null
            ? Math.round(
              (totalConcreteSent / totalConcreteRequested) * 100 * 100
            ) / 100
            : 0;

        return (
          <>
            <Progress
              type="circle"
              percent={Math.round(percent)}
              size="small"
            />
          </>
        );
      },
      width: "8%",
    },
    {
      title: "Pompa Tipi",
      dataIndex: "TransactionRequestPompType",
      key: "CarTypeName",
      width: "12%",
      render: (pomps: any[]) => {
        return (
          <div className="!flex gap-1 !flex-wrap">
            {pomps?.map((item: any) => {
              return (
                <span>
                  <Tag color="blue">{item?.PompType?.Name || ""}</Tag>
                </span>
              );
            })}
          </div>
        );
      },
    },

    {
      title: "Döküm Zamanı",
      dataIndex: "ApprovedDateTime",
      render: (text: any, record: any) => {
        return (
          <div className="">
            <div>
              <Text>{new Date(text).toLocaleDateString("tr-TR")}</Text>
            </div>
            <div>
              <Text>{dayjs(text).format("HH:mm")}</Text>
            </div>
          </div>
        );
      },
      width: "15%",
      key: "pouringTime",
    },

    {
      title: "Durum",
      dataIndex: ["Status", "Name"],
      key: "durum",
      render: (value: string, record: any) => {
        return (
          <Tag color={determineTransactionStatus(record?.StatusId)}>
            {value}
          </Tag>
        );
      },
      width: "4%",
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "4%",
      render: (key: any, record: any) => {
        return (
          <div className="!flex gap-2">
            <Tooltip title="Bildrim Gönder">
              <BellOutlined
                className="!text-red-500 !text-lg hover:cursor-pointer"
                onClick={async () => {
                  await setSelectedRecord(record);
                  setIsShowNoticationModal(true);
                }}
              />
            </Tooltip>
            {((userInfo.RoleId == "439aeada-02d0-4962-9dfd-bc41363461a3" || userInfo.RoleId == "5351cc0b-20d7-4dcc-8fba-90c797b3f5b8" || userInfo.RoleId == "439aeada-02d0-4962-9dfd-bc41363461a3") || (record?.StatusId !== 5 && record?.StatusId !== 6)) && (
              <>
                <Tooltip title="Durumu Güncelle">
                  <EditOutlined
                    onClick={async () => {
                      await setSelectedRecord(record);
                      setIsShowChangeStatusModal(true);
                    }}
                    className="!text-blue-500 !text-lg hover:cursor-pointer"
                  />
                </Tooltip>
                <Tooltip title="Zaman ve İstasyonu Güncelle ">
                  <FieldTimeOutlined
                    onClick={async () => {
                      await setSelectedRecord(record);
                      setIsShowDetermineDateTime(true);
                    }}
                    className="!text-green-500 !text-lg hover:cursor-pointer"
                  />
                </Tooltip>
              </>
            )}
          </div>
        );
      },
    },
  ];
  const initialFilter = {
    PageIndex: 1,
    PageSize: 20,

    IncludeProperties: [
      "Building.Company",
      "TransactionRequestPompType.PompType",
      "ConsistencyClass",
      "CarType",
      "Status",
      "Station",
      "Product",
      "ConcreteLocation",
      "RequestedPerson",
      "TransactionRequestConcreteOption.ConcreteOption",

      // "Building.AuditPerson",
    ],
  };
  const [filter, setFilter] = useState<any>(
    transactionId ? { ...initialFilter, Ids: [transactionId] } : calendarFilter
  );

  useEffect(() => {
    if (calendarFilter?.StationIds) {
      let currentFilter = { ...filter };
      currentFilter["StationIds"] = calendarFilter?.StationIds;
      setFilter(currentFilter);
    } else {
      let currentFilter = { ...filter };
      delete currentFilter["StationIds"];
      setFilter(currentFilter);
    }
  }, [calendarFilter]);

  const transactionRequestFilter = useGetTransactionRequests(filter);
  let transactionList = transactionRequestFilter.data ? transactionRequestFilter.data.Data : [];

  const transactionRequests = isShowedCalanderHistories
    ? transactionList
    : transactionList.filter((item: any) => {
      const dateTime = item.ApprovedDateTime;
      const approvedDate = dayjs(dateTime).format("YYYY-MM-DD");
      const todayDate = dayjs().format("YYYY-MM-DD");
      if (viewType !== "timeGridDay") {
        return approvedDate >= todayDate;
      }
      return item

    });

  const [isShowChangeStatusModal, setIsShowChangeStatusModal] = useState(false);

  return (
    <>
      <Row>
        <Col span={24}>
          <Table
            scroll={{ x: 1394 }}
            dataSource={
              transactionRequests
            }
            columns={columns}
            loading={
              transactionRequests.isLoading || transactionRequests.isFetching
            }
            rowKey="Id"
            expandable={{
              expandedRowKeys: transactionId ? [transactionId] : undefined,

              expandedRowRender: (record: any) => (
                <>
                  <PlanCarDetails
                    transactionRequestId={record.Id}
                    selectedTransactionRequest={record}
                    accountingCode={record?.Building?.Company?.AccountingCode}
                  />
                </>
              ),
            }}
            pagination={false}
          />
        </Col>
      </Row>
      {selectedRecord && (
        <>
          <Modal
            open={isShowNotificationModal}
            onCancel={() => {
              setIsShowNoticationModal(false);
            }}
            footer={false}
            title={"Şantiye üyelerine bildrim at"}
          >
            <>
              {isShowNotificationModal && (
                <Suspense fallback={<Spin />}>
                  <SendNotification
                    selectedRecord={selectedRecord}
                    onFinish={() => {
                      setIsShowNoticationModal(false);
                    }}
                  />
                </Suspense>
              )}
            </>
          </Modal>

          <Modal
            title={"Durumu Değiştır"}
            footer={false}
            open={isShowChangeStatusModal}
            onCancel={() => {
              setIsShowChangeStatusModal(false);
            }}
          >
            {isShowChangeStatusModal && (
              <Suspense fallback={<Spin />}>
                <ChangeStatusTransaction
                  selectedRecord={selectedRecord}
                  onFinish={() => {
                    setIsShowChangeStatusModal(false);
                  }}
                />
              </Suspense>
            )}
          </Modal>
          <Modal
            title={"Zaman ve İstasyonu Belirle"}
            footer={false}
            open={isShowDetermineDateTime}
            onCancel={() => {
              setIsShowDetermineDateTime(false);
            }}
          >
            <>
              {isShowDetermineDateTime && (
                <Suspense fallback={<Spin />}>
                  <DetermineDateTime
                    selectedRecord={selectedRecord}
                    type="calander"
                    onFinish={() => {
                      setIsShowDetermineDateTime(false);
                    }}
                  />
                </Suspense>
              )}
            </>
          </Modal>
        </>
      )}
    </>
  );
};

export default EventTableDetails;
