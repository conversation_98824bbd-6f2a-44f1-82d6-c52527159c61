import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { updateContractProductTransactionWithPatch, } from "apps/Plan/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import GeneralUserInput from "apps/Common/GeneralUserInput";

interface RedirectAnotherStationProps {
  onFinish?: any;
  selectedRecord?: any;
}

const ChangeContractProductDriver: FC<RedirectAnotherStationProps> = ({
  onFinish,
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    let data = {
      Id: selectedRecord.Id,
      patch: [{ path: "DriverId", value: formValues["DriverId"] }],
    };

    try {
      await updateContractProductTransactionWithPatch(data);
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getContractProductTransaction,
        exact: false,
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ChangeVehicle");
    }
  };

  useEffect(()=>{
    if(selectedRecord)
    {
      form.setFieldValue("DriverId",selectedRecord?.DriverId)
    }
  },[selectedRecord])


  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          initialValues={{ Active: true }}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
          <GeneralUserInput
              name="DriverId"
              label={"Şoför"}
              placeholder="Şoför"
              className="!m-0"
              allowClear
              xs={24}
              externalFilter={{
                PageSize: -1,
                RoleId: "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469",
              }}
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ChangeContractProductDriver