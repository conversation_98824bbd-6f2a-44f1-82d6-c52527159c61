import { Card, Image, Modal, Skeleton,  Spin, Tag, Tooltip, Typography } from "antd";
import { FC, lazy, Suspense, useEffect, useState } from "react";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import { getWeatherInfoes } from "apps/Plan/Services";
import dayjs from "dayjs";
import {
  BellOutlined,
  EditOutlined,
  FieldTimeOutlined,
} from "@ant-design/icons";
import { determineTransactionStatus } from "helpers/DetermineTransactionStatusColor";

const DetermineDateTime = lazy(
  () => import("../../NewRequest/DetermineDateTime")
);
const ChangeStatusTransaction = lazy(
  () => import("./ChangeStatusTransactions")
);
const SendNotification = lazy(
  () => import("./SendNotification")
);

interface EventContentProps {
  event: any;
  itemInfos: any;
  viewType: string;
  setSelectedRecord: any;
  setIsShowTableDetails: any;
}
const EventContent: FC<EventContentProps> = (props) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isShowDetermineDateTime, setIsShowDetermineDateTime] = useState(false);
  const { Text } = Typography;

  const [weatherInfoes, setWeatherInfoes] = useState<any| null>(null);
  const getWeatherInformations = async (
    buildingId: string,
    startDate: string,
    endDate: string
  ) => {
    setIsLoading(true);
    try {
      const response: any = await getWeatherInfoes({
        BuildingId: buildingId,
        StartDate: startDate,
        EndDate: endDate,
      });

      if (response?.Value) {
        setWeatherInfoes(response.Value[0]);
      }
    } catch (error: any) {
      showServiceErrorMessage(error, {}, "Get weather info", true);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    let buildingId = props.itemInfos?.BuildingId;
    let date = props.itemInfos?.ApprovedDateTime;
    if (buildingId && date) {
      let formatedDate = dayjs(date).format("YYYY-MM-DD");
      getWeatherInformations(buildingId, formatedDate, formatedDate);
    }
  }, [props.itemInfos?.Id]);

  const { id, start, end } = props.event;
  const [isShowChangeStatusModal, setIsShowChangeStatusModal] = useState(false);
  const [isShowOptions, setIsShowOptions] = useState(false);
  const [isShowSendNotification, setIsShowSendNotification] = useState(false);


  return (
    <>
      <Skeleton loading={isLoading}>
        <Card
          className={`!w-full !h-full   !flex items-center !relative`}
          loading={isLoading}
          bodyStyle={{ padding: "5px", width: "100%" }}
          style={{ border: "1px solid blue ", background: "#fffae4" }}
          onMouseEnter={() => {
            setIsShowOptions(true);
          }}
          onMouseLeave={() => {
            setIsShowOptions(false);
          }}
        >
          { isShowOptions && (
            <>
              <div
                className="!bg-white !w-[20px] !h-full "
                style={{ position: "absolute", bottom: "1px", left: "0px" }}
              >
                {
                  props.itemInfos?.StatusId !== 5&& props.itemInfos?.StatusId !== 6&&
                  <>
                <div className="!w-full  !h-[20px] !flex justify-center items-center">
                  <Tooltip title="Durumu Güncelle">
                    <EditOutlined
                      onClick={() => {
                        setIsShowChangeStatusModal(true);
                      }}
                      className="!text-sm !text-indigo-500"
                    />
                  </Tooltip>
                </div>
                <div className="!w-full  !h-[20px] !flex justify-center items-center !mt-1">
                  <Tooltip title="Zaman ve İstasyonu Güncelle ">
                    <FieldTimeOutlined
                      onClick={() => {
                        setIsShowDetermineDateTime(true);
                      }}
                      className="!text-sm !text-green-500"
                    />
                  </Tooltip>
                </div>
                  </>
                }
                <div className="!w-full  !h-[20px] !flex justify-center items-center !mt-1">
                  <Tooltip title="Bildrim Gönder">
                    <BellOutlined
                      onClick={() => {
                        setIsShowSendNotification(true);
                      }}
                      className="!text-sm !text-red-500"
                    />
                  </Tooltip>
                </div>
              
              </div>
            </>
          )}

          <div
            onClick={async () => {
              await props.setSelectedRecord(props.itemInfos);
              props.setIsShowTableDetails(true);
            }}
            className="!w-full fc-event-title-container "
          >
            {weatherInfoes && (
              <div className="!flex justify-center !w-full items-center gap-2 ">
                <div className="!w-[30px] !h-[30px]">
                  <div dangerouslySetInnerHTML={{ __html: weatherInfoes?.Image }} />
                </div>
                <div>
                
                  <Text className="!text-xs !text-indigo-500">{weatherInfoes?.Degree}</Text>
                <Image preview={false} width={"10px"} height={"10px"} src="https://cdn-icons-png.flaticon.com/512/15059/15059277.png" alt="degree" />

                 
                </div>
              </div>
            )}
            
            <div className="!flex justify-center !text-nowrap !overflow-auto">
              <Text className="!font-bold">
                {props.itemInfos?.Building?.Name}
              </Text>
            </div>

            <div className="!flex justify-center !text-nowrap !overflow-auto ">
              <Text className="!font-bold">{`${
                props.itemInfos?.Product?.Name || ""
              }-${props.itemInfos?.DesiredTotalConcrete || ""}m³`}</Text>
            </div>

            <div className="!flex justify-center gap-2">
              <Text className="!font-bold !text-red-400">{`${
                props?.itemInfos?.TotalConcreteRemaining || 0
              }m³`}</Text>
              <Text className="!font-bold !text-success">{`${
                props?.itemInfos?.TotalConcreteSent || 0
              }m³`}</Text>
            </div>
            <div className="!flex gap-2 justify-center">
              {start && <Text>{`${dayjs(start).format("HH:mm")}`}</Text>}
              {end && <Text>{`${dayjs(end).format("HH:mm")}`}</Text>}
            </div>
            <div className="!flex justify-center !text-nowrap !overflow-auto !mt-1">
              <Tag
                color={determineTransactionStatus(props?.itemInfos?.StatusId)}
              >
                {props?.itemInfos?.Status?.Name || ""}
              </Tag>
            </div>
            <div className="!flex justify-center !text-nowrap !overflow-auto">
              <Text className="">
                {props.itemInfos?.Station?.Name}
              </Text>
            </div>
          </div>
        </Card>
      </Skeleton>

      <>
        <Modal
          title="Durumu Güncelle"
          footer={false}
          open={isShowChangeStatusModal}
          onCancel={() => {
            setIsShowChangeStatusModal(false);
          }}
        >
          <>
          {
            isShowChangeStatusModal&&
            <Suspense fallback={<Spin/>} >
              <ChangeStatusTransaction
                selectedRecord={props.itemInfos}
                onFinish={() => {
                  setIsShowChangeStatusModal(false);
                }}
              />

            </Suspense>

          }
          </>
        </Modal>

        <Modal
          open={isShowSendNotification}
          onCancel={() => {
            setIsShowSendNotification(false);
          }}
          footer={false}
          title={"Şantiye üyelerine bildrim at"}
        >
          <>
          {
            isShowSendNotification&&
            <Suspense fallback={<Spin/>}>
              <SendNotification
                selectedRecord={props.itemInfos}
                onFinish={() => {
                  setIsShowSendNotification(false);
                }}
              />

            </Suspense>

          }
          </>
        </Modal>
        <Modal
          title={"Zaman ve İstasyonu Belirle"}
          footer={false}
          open={isShowDetermineDateTime}
          onCancel={() => {
            setIsShowDetermineDateTime(false);
          }}
        >
          <>
          {
            isShowDetermineDateTime&&
            <Suspense fallback={<Spin/>} >

              <DetermineDateTime
                type="calander"
                selectedRecord={props.itemInfos}
                onFinish={() => {
                  setIsShowDetermineDateTime(false);
                }}
              />
             
            </Suspense>
          }
          </>
        </Modal>
      </>
    </>
  );
};

export default EventContent;
