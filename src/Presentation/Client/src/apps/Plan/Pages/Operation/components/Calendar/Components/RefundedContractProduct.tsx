import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { MazakaButton } from "apps/Common/MazakaButton";

import { openNotificationWithIcon } from "helpers/Notifications";
import { updateContractProductTransactionWithPatch } from "apps/Plan/Services";
import MazakaInputNumber from "apps/Common/MazakaInputNumber";

interface ChangeTransactionStatusProps {
  onFinish?: any;
  selectedRecord?: any;
}

const RefundedContractProduct: FC<ChangeTransactionStatusProps> = ({
  onFinish,
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();

  // Maksimum değeri dinamik olarak hesapla
  const maxRefundAmount = selectedRecord?.SendingAmount ?? 0;

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    try {
      formValues["TypeId"] = 2;
      formValues["Id"] = selectedRecord?.Id;
      formValues["Note"] = formValues["Note"] || "";
      const data = {
        Id: selectedRecord?.Id,
        Patch: [
          {
            Path: "RefundAmount",
            Value: formValues["RefundedValue"]?.toString(),
          },
          { Path: "TypeId", value: "2" },
        ],
      };
      await updateContractProductTransactionWithPatch(data);
      queryClient.resetQueries({
        queryKey: endpoints.getContractProductTransaction,
        exact: false,
      });

      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      openNotificationWithIcon("success", "İşlem Başarılı");

      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ChangeTransactionStatus");
    }
  };

  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 10]}>
            <MazakaInputNumber
              type="number"
              name="RefundedValue"
              label="İade Miktarı"
              placeholder={`Maks İade Miktarı ${maxRefundAmount}`}
              xs={24}
              className="!m-0"
              max={maxRefundAmount} // Dinamik maksimum değer
              step={0.01} // Ondalık değer için step ekle
              rules={[
                {
                  required: true,
                  message: "İade miktarı girilmelidir.",
                },
                {
                  validator: (_, value) => {
                    console.log(value);
                    if (value > maxRefundAmount) {
                      return Promise.reject(
                        "İade miktarı, gönderilen miktardan büyük olamaz."
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            />

            <Col xs={24} className="!flex justify-end">
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                İade et
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default RefundedContractProduct;