import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { updateTransactionWithPatch } from "apps/Plan/Services";
import dayjs from "dayjs";
import { MazakaDatePicker } from "apps/Common/MazakaDatePicker";
import { MazakaInput } from "apps/Common/MazakaInput";
import GeneralStationInput from "apps/Common/GeneralStationInput";

interface DetermineDateTimesProps {
  onFinish?: any;
  selectedRecord?: any;
  type:"request"|"calander"
}

const DetermineDateTime: FC<DetermineDateTimesProps> = ({
  onFinish,
  selectedRecord,
  type
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const disablePastDates = (current: dayjs.Dayjs) => {
    // Bugünden önceki tarihleri devre dışı bırakır
    return current && current < dayjs().startOf('day');
  };
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    let time = formValues["Time"];
    let date = dayjs(formValues["Date"]).format("YYYY-MM-DD");
    let dateTime = `${date}T${time}`;
    let data = {
      Id: selectedRecord.Id,
      patch: [
        { path: "ApprovedDateTime", value: dateTime },
        { path: "StatusId", value: "4" },
        {path:"StationId",value:formValues["StationId"]}
      ],
    };

    try {
      await updateTransactionWithPatch(data);
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getTransactionRequestListFilter,
        exact: false,
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "DetermineDateTime");
    }
  };

  useEffect(()=>{
    if(selectedRecord)
    {
    
      form.setFieldsValue({
        StationId:selectedRecord["StationId"],
        Date:dayjs(type==="calander"?selectedRecord["ApprovedDateTime"]:selectedRecord["DesiredDateTime"]),
        Time:dayjs(type==="calander"?selectedRecord["ApprovedDateTime"]:selectedRecord["DesiredDateTime"]).format("HH:mm")
      })
    }
  },[selectedRecord])

  return (
    <>
      <Col span={24}>
        <MazakaForm initialValues={{Date:dayjs(),Time:dayjs().format("HH:mm")}} form={form} onFinish={handleOnFinish} {...formActions}>
          <Row gutter={[0, 10]}>
          <GeneralStationInput
              name="StationId"
              label={"İstasyon"}
              placeholder="İstasyon"
              className="!m-0"
              xs={24}
              rules={[{ required: true, message: "" }]}
            />
            <MazakaDatePicker
              name={"Date"}
              label={"Tarih"}
              xs={24}
              disablePastDates={disablePastDates }
              className="!m-0"
              rules={[{ required: true, message: "" }]}
            />
            <MazakaInput
              name={"Time"}
              label={"Zaman"}
              placeholder="Saat:Dakika"
              xs={24}
              className="!m-0"
              rules={[
                { required: true, message: "" },
                {
                  pattern: /^([01]?[0-9]|2[0-3]):([0-5][0-9])$/,
                  message:
                    "Lütfen geçerli bir saat:dakika formatında (01:00 - 23:59 arası) giriniz",
                },
              ]}
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetermineDateTime;
