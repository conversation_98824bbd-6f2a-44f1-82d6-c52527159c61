import { Col, Form, Row, } from "antd";
import GeneralStationRequestTree from "apps/Common/GeneralStationRequestTreeInput";
import { MazakaForm } from "apps/Common/MazakaForm";
import { updateContractProductTransactionWithPatch } from "apps/Plan/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC,} from "react";
import endpoints from "apps/Plan/EndPoints"
import { useQueryClient } from "react-query";



interface RedirectAnotherStationProps {
  onFinish?: any;
  selectedRecord?:any;
}

const RedirectAnotherStation: FC<RedirectAnotherStationProps> = ({
  onFinish,
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    let data = {Id:selectedRecord.Id,patch:[
     { path:"TransactionRequestId",value:formValues["TransactionRequestId"]}
    ]}
   
    try {
      await  updateContractProductTransactionWithPatch(data);
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
   
      queryClient.resetQueries({
        queryKey: endpoints.getContractProductTransaction,
        exact: false,
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm,"RedirectToAnotherStation");
    }
  };



 
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          initialValues={{ Active: true }}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
         <GeneralStationRequestTree
         name={"TransactionRequestId"}
         xs={24}
         className="!m-0"
         />
         
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default RedirectAnotherStation;
