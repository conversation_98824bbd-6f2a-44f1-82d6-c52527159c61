import { Form, Row } from "antd";
import GeneralPlanTimePeriodes from "apps/Common/GeneralPlanTimeInput";
import GeneralStationInput from "apps/Common/GeneralStationInput";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import dayjs from "dayjs";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const TopFilterOptions = () => {
  const [form] = Form.useForm();
  const { operationTransactionFilter,calendarFilter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();

  const handleOnChangeStations = (valueIds: string[]) => {
    let currentOperationFilter = { ...operationTransactionFilter };
    let currentCalanderFilter = { ...calendarFilter };
    if (valueIds?.length>0) {
      currentOperationFilter["StationIds"] = valueIds;
      currentCalanderFilter["StationIds"] = valueIds;
    } else {
      delete currentOperationFilter["StationIds"];
      delete currentCalanderFilter["StationIds"];
    }
    dispatch(
      hanldleSetTranasactionFilter({
        filter: currentOperationFilter,
        type: "operationTransactionFilter",
      })
    );
    dispatch(
      hanldleSetTranasactionFilter({
        filter: currentCalanderFilter,
        type: "calendarFilter",
      })
    );
  };

 

  const handleOnChnageTimePeriode = (value: string) => {
    let currentFilter = { ...operationTransactionFilter };
    if (value === "all") {
      delete currentFilter["StartDesiredTime"];
      delete currentFilter["EndDesiredTime"];
    } else {
      let startDate = "";
      let endDate = "";
      if (
        value === dayjs().format("YYYY-MM-DD") ||
        value === dayjs().add(1, "day").format("YYYY-MM-DD")
      ) {
        startDate = value;
        endDate = value;
      } else {
        startDate = dayjs().format("YYYY-MM-DD");
        endDate = value;
      }

      currentFilter["StartDesiredTime"] = startDate;
      currentFilter["EndDesiredTime"] = endDate;
    }
    dispatch(
      hanldleSetTranasactionFilter({
        filter: currentFilter,
        type: "operationTransactionFilter",
      })
    );
  };

  useEffect(() => {
    form.setFieldValue("StationIds", operationTransactionFilter?.StationIds);
    form.setFieldValue(
      "Times",
      operationTransactionFilter?.EndDesiredTime
        ? dayjs(operationTransactionFilter?.EndDesiredTime).format("YYYY-MM-DD")
        : "all"
    );
  }, [operationTransactionFilter]);

  return (
    <>
      <Form form={form}>
        <Row gutter={[10, 10]}>
          <GeneralStationInput
            name="StationIds"
            placeholder="İstasyon"
            className="!m-0"
            xs={24}
            md={12}
            allowClear={true}
            mode="multiple"
            onChange={handleOnChangeStations }
          />
          <GeneralPlanTimePeriodes
            name="Times"
            placeholder="Zamanlama"
            className="!m-0"
            xs={24}
            md={12}
            allowClear={true}
            onChange={handleOnChnageTimePeriode}
          />
        </Row>
      </Form>
    </>
  );
};

export default TopFilterOptions;
