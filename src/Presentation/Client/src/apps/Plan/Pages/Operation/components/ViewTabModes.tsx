import { Tabs, TabsProps } from "antd";
import { handleSetOperationView, } from "apps/Plan/ClientSideStates";
import { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";

const ViewTabModes = () => {
  const { operationTransactionFilter: filter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();
  const items: TabsProps["items"] = [
    {
      key: "calendar",
      label: "Takvim",
      children: <></>,
    },
    {
      key: "list",
      label: "Liste",
      children: <></>,
    },
    
  ];
  const [activeKey, setActiveKey] = useState("calendar");
  const handleOnChange = (value: string) => {
    dispatch(handleSetOperationView({mode:value}))
    setActiveKey(value)
  
   
  };
  return (
    <>
      <Tabs activeKey={activeKey} items={items} onChange={handleOnChange} />
    </>
  );
};

export default ViewTabModes;
