import { PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import DiscoveryTableList from "./Components/DiscoveryTableList";
import TopFilterOptions from "./Components/TopFilterOptions";
const AddOrUpdateDiscoveryRequest = lazy(
  () => import("./Components/AddOrUpdate/AddOrUpdateDiscoveryRequest")
);


const DiscoveryIndex = () => {
  const { Text } = Typography;
  const queryClient = useQueryClient();
  const [isShowAddTransactionDrawer, setisShowAddTransactionDrawer] = useState(false);
 

  return (
    <>
      <MazakaLayout
        title={"Keşif  Talepler"}
        headDescription={
          "Keşif  talepler  sayfası, sistemde tüm keşif taleplerin  görüntülemenizi sağlar"
        }
      >
        <Row gutter={[20, 20]} className="!mt-4">
         
          <Col span={24}>
            <Row gutter={[10, 10]}>

              <Col xs={24} lg={18}>
                <TopFilterOptions />
              </Col>
              <Col xs={24} lg={6} className="!flex justify-end gap-2">
                <Button
                  onClick={() => {
                    setisShowAddTransactionDrawer(true);
                  }}
                  className="!flex items-center"
                  type="primary"
                >
                  <div className="!flex items-center gap-1">
                    <PlusOutlined />
                    <Text className="!text-white">Talep Oluştur</Text>
                  </div>
                </Button>
                
               
              </Col>
            </Row>
          </Col>

          <Col span={24}>
          <DiscoveryTableList/>
          </Col>
        </Row>
      </MazakaLayout>

      <Drawer
        title="Yeni talep oluştur"
        open={isShowAddTransactionDrawer}
        onClose={() => {
          setisShowAddTransactionDrawer(false);
        }}
      >
        <>
        {
          isShowAddTransactionDrawer&&
          <Suspense fallback={<Spin/>} >

            <AddOrUpdateDiscoveryRequest
            onFinish={()=>{
              setisShowAddTransactionDrawer(false)
              queryClient.resetQueries({
                queryKey: endpoints.getTransactionRequestListFilter,
                exact: false,
              });
    
            }} />
          </Suspense>
        }
        </>
      </Drawer>
     
    </>
  );
};

export default DiscoveryIndex;
