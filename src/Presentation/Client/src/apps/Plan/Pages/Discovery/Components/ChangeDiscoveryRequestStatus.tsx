import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import { updateTransactionWithPatch } from "apps/Plan/Services";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { FC, useEffect } from "react";
import { useQueryClient } from "react-query";
import endpoints from "apps/Plan/EndPoints";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";

interface ChangeDiscoveryStatusProps {
  onFinish?: any;
  selectedRecord?: any;
}

const ChangeDiscoveryStatus: FC<ChangeDiscoveryStatusProps> = ({
  onFinish,
  selectedRecord,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const queryClient = useQueryClient();
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    let data = {
      Id: selectedRecord.Id,
      patch: [{ path: "StatusId", value: formValues["StatusId"] },
      { path: "ApprovedNote", value: formValues["ApprovedNote"]||"" },
    
    ],
    };

    try {
      await updateTransactionWithPatch(data);
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      queryClient.resetQueries({
        queryKey: endpoints.getTransactionRequestListFilter,
        exact: false,
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "ChangeTransactionStatus");
    }
  };

  const options = [
    { label: "İptal Et", value: "7" },
    { label: "Açık", value: "1" },
    { label: "Kapalı", value: "6" },
  ];

  useEffect(()=>{
    form.setFieldsValue({
      StatusId:selectedRecord?.StatusId?.toString(),
      ApprovedNote:selectedRecord?.ApprovedNote
    })
  },[selectedRecord])

  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          initialValues={{ Active: true }}
          onFinish={handleOnFinish}
          {...formActions}
        >
          <Row gutter={[0, 10]}>
            <MazakaSelect
              name={"StatusId"}
              placeholder={"Durum"}
              label="Durum"
              xs={24}
              className="!m-0"
              options={options}
              rules={[{ required: true, message: "" }]}
            />
             <MazakaTextArea
              name="ApprovedNote"
              label={"Açıklama"}
              placeholder="Açıklama"
              xs={24}
              className="!m-0"
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default ChangeDiscoveryStatus;
