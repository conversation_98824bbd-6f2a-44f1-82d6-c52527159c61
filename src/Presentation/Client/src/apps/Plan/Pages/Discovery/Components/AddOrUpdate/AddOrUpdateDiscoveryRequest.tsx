import { Col, Form, Row } from "antd";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";
import { FC, useState } from "react";
import { MazakaDatePicker } from "apps/Common/MazakaDatePicker";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import GeneralUserInput from "apps/Common/GeneralUserInput";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import dayjs from "dayjs";
import { addTransaction } from "apps/Plan/Services";

const AddOrUpdateDiscoveryRequest: FC<GeneralAddOrUpdateFormProps> = ({
  onFinish,
}) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const [selectedBuildingId, setSelectedBuildingId] = useState<null | string>(
    null
  );

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    

    for (let key in formValues) {
      if (typeof key !== "boolean" && !formValues[key]) {
        delete formValues[key];
      }
    }
    formValues["DesiredDateTime"] = dayjs(  formValues["DesiredDateTime"]).format("YYYY-MM-DD")
    formValues["TotalConcerete"] = 0
    formValues["TransactionrequestTypeId"]=2

    try {
      await addTransaction(formValues)
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });
      form.resetFields();
      onFinish();
    } catch (error: any) {
      showServiceErrorMessage(error, mazakaForm, "Discovery");
    }
  };
  const statusOptions = [
    { label: "Açık", value: 1 },
    { label: "Kapalı", value: 6 },
  ];
  return (
    <Col span={24}>
      <MazakaForm
        form={form}
        initialValues={{ Active: true }}
        onFinish={handleOnFinish}
        {...formActions}
      >
        <Row gutter={[10, 10]}>
          <GeneralBuildingInput
            name="BuildingId"
            label={"Şantiye"}
            placeholder="Şantiye"
            className="!m-0"
            xs={24}
            rules={[{ required: true, message: "" }]}
            onChange={(value: string) => {
              setSelectedBuildingId(value);
            }}
          />

          {selectedBuildingId && (
            <>
              <GeneralUserInput
                name="RequestedPersonId"
                label="Sorumlu Kişi"
                placeholder="Sorumlu Kişi"
                className="!m-0"
                externalFilter={{
                  PageSize: -1,
                  BuildingId: selectedBuildingId,
                  IncludeProperties: ["BuildingUser"],
                }}
                rules={[{ required: true, message: "" }]}
              />
            </>
          )}

          <MazakaDatePicker
            name={"DesiredDateTime"}
            label={"Keşif Tarihi"}
            xs={24}
            rules={[{ required: true, message: "" }]}
            className="!m-0"
          />
          <MazakaSelect
            name={"StatusId"}
            label={"Durum"}
            placeholder={"Durum"}
            options={statusOptions}
            xs={24}
            rules={[{ required: true, message: "" }]}
            className="!m-0"
          />
          <MazakaTextArea
            xs={24}
            label="Şantiye Sorumlu Açıklaması"
            placeholder="Şantiye Sorumlu Açıklaması"
            name="Note"
            className="!m-0"
          />
          <MazakaTextArea
            xs={24}
            label="Açıklama"
            placeholder="Açıklama"
            name="ApprovedNote"
            className="!m-0"
          />
        </Row>
      </MazakaForm>
    </Col>
  );
};

export default AddOrUpdateDiscoveryRequest;
