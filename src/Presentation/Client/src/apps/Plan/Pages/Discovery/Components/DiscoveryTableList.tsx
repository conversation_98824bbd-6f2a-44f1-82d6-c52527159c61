import { <PERSON><PERSON>, <PERSON>, Mo<PERSON>, Spin, Table, Tag, Tooltip, Typography } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetTransactionRequests } from "apps/Plan/ServerSideStates";
import { hanldleSetTranasactionFilter } from "apps/Plan/ClientSideStates";
import dayjs from "dayjs";
import { SecretText } from "apps/Common/SecretString";
import { lazy, Suspense, useState } from "react";
import ExpandableText from "apps/Common/TruncatedDesc";
const ChangeDiscoveryStatus = lazy(
  () => import("./ChangeDiscoveryRequestStatus")
);

const DiscoveryTableList = () => {
  const { discoveryFilter: filter } = useSelector(
    (state: RootState) => state.transactions
  );
  const dispatch = useDispatch();

  const [selectedRecord, setSelectedRecord] = useState<null | any>(null);
  const [isShowChangeTransactionStatusModal, setIsShowTransactionStatusModal] =
    useState(false);

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(
      hanldleSetTranasactionFilter({
        filter: newFilter,
        type: "discoveryFilter",
      })
    );
  };

  const discoveryTransactions = useGetTransactionRequests(filter);
  const { Text } = Typography;

  const columns = [
    {
      title: "Firma",
      dataIndex: ["Building", "Company", "Name"],
      key: "CompanyName",
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{value}</Text>
            </div>
            {record?.Building?.Company?.Phone && (
              <>
                <div className="!text-primary">
                  <SecretText
                    text={record?.Building?.Company?.Phone}
                    textType="phone"
                  />
                </div>
              </>
            )}
          </>
        );
      },
      width: "10%",
      sorter: (a: any, b: any) => a?.Building?.Company?.Name.localeCompare(b?.Building?.Company?.Name)
    },
    {
      title:"Oluşturan Kişi",
      render:(_:string,record:any)=>{
        return(
          <>
          <Text>{`${record?.RequestedPerson?.Name||""} ${record?.RequestedPerson?.Surname||""}`}</Text>
          </>
        )
      },
       width:"10%",
       sorter: (a: any, b: any) => a?.RequestedPerson?.Name.localeCompare(b?.RequestedPerson?.Name)
    },
    {
      title: "Şantiye",
      dataIndex: ["Building", "Name"],
      key: "BuildingName",
      width: "10%",
      render: (value: string, record: any) => {
        return (
          <>
            <div>
              <Text>{value}</Text>
            </div>
            {/* {record?.Building?.AuditPerson?.PhoneNumber && (
              <>
                <div className="!text-primary">
                  <SecretText
                    text={record?.Building?.AuditPerson?.PhoneNumber}
                    textType="phone"
                  />
                </div>
              </>
            )} */}
          </>
        );
      },
      sorter: (a: any, b: any) => a?.Building?.Name.localeCompare(b?.Building?.Name)
    },
    {
      title: "Adres",
      dataIndex: ["Building", "Address"],
      key: "BuildingAddress",
      width: "8%",
    },

    {
      title: "Keşif Tarihi",
      dataIndex: "DesiredDateTime",
      key: "DesiredDateTime",
      render: (text: any) => {
        if (text) {
          return (
            <>
              <div>
                <Text>{new Date(text).toLocaleDateString("tr-TR")}</Text>
              </div>
              <div>
                <Text>{dayjs(new Date(text)).format("HH:mm")}</Text>
              </div>
            </>
          );
        }
        return <></>;
      },
      width: "8%",
       sorter: (a: any, b: any) => {
              const aTime = a?.DesiredDateTime ? dayjs(a.DesiredDateTime).valueOf() : 0;
              const bTime = b?.DesiredDateTime ? dayjs(b.DesiredDateTime).valueOf() : 0;
              return aTime - bTime;
            }
    },

    {
      title: "Durum",
      dataIndex: ["Status", "Name"],
      render: (value: string, record: any) => {
        return (
          <Tag
            color={
              record.StatusId === 1
                ? "blue"
                : record.StatusId === 6
                ? "green"
                : "error"
            }
          >
            {record.StatusId === 1
              ? "Açık"
              : record.StatusId === 6
              ? "Kapalı"
              : "İptal Et"}
          </Tag>
        );
      },
      width: "8%",
      sorter: (a: any, b: any) => {
        const getStatusLabel = (statusId: number) => {
          if (statusId === 1) return "Açık";
          if (statusId === 6) return "Kapalı";
          return "İptal Et";
        };
        return getStatusLabel(a?.StatusId).localeCompare(getStatusLabel(b?.StatusId));
      }
    },
    {
      title: "Şantiye Açıklaması",
      dataIndex: "Note",
      key: "Note",
      width: "10%",
      render: (value: string) => {
        if (value) {
          return (
            <>
              <ExpandableText
              title={ "Şantiye Açıklaması"}
              limit={20}
              text={value || ""}
            />
            </>
          );
        }
      },
    },
    {
      title: "Rapor",
      dataIndex: "ApprovedNote",
      key: "ApprovedNote",
      width: "10%",
      render: (value: string) => {
        if (value) {
          return (
            <>
             <ExpandableText
              title={ "Rapor"}
              limit={20}
              text={value || ""}
            />
            </>
          );
        }
      },
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <div className="!flex items-center justify-center gap-2">
          <Button
            onClick={async () => {
              await setSelectedRecord(record);
              setIsShowTransactionStatusModal(true);
            }}
            type="text"
            className="!bg-green-500 !text-white"
          >
            Durumu Değiştir
          </Button>
        </div>
      ),
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={
          discoveryTransactions.isLoading || discoveryTransactions.isFetching
        }
        dataSource={
          discoveryTransactions.data ? discoveryTransactions.data.Data : []
        }
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: discoveryTransactions.data?.FilteredCount || 0,
          current: discoveryTransactions.data?.PageIndex,
          pageSize: discoveryTransactions.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />

      <Modal
        title={"Durumu Güncelle"}
        open={isShowChangeTransactionStatusModal}
        onCancel={() => {
          setIsShowTransactionStatusModal(false);
        }}
        footer={false}
      >
        <>
        {
          isShowChangeTransactionStatusModal&&
          <Suspense fallback={<Spin/>} >

            <ChangeDiscoveryStatus
              selectedRecord={selectedRecord}
              onFinish={() => {
                setIsShowTransactionStatusModal(false);
              }}
            />
          </Suspense>
        }
        </>
      </Modal>
    </Col>
  );
};

export default DiscoveryTableList;
