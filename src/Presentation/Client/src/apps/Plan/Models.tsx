import { UserListAndDetails } from "apps/Account/Models";
import { BuildingListAndDetails } from "apps/Building/Models";
import { ContractListAndDetails } from "apps/Contract/Models";
import { ProductListAndDetails } from "apps/Product/Models";
import { StationAndDetails } from "apps/Station/Models";

interface BaseSharedRequestTransaction {
  BuildingId: string;
  Building: BuildingListAndDetails;
  ContractId: string;
  Contract: ContractListAndDetails;
  ProductId: string;
  Product: ProductListAndDetails;

  StatusId: number;
  Status: {
    Id?: number | string;
    Name: string;
    CustomerName: string;
  };
  RequestedPersonId: string;
  RequestedPerson: UserListAndDetails;
  DesiredTotalConcrete: number;
  DesiredDateTime: string;
  ConcreteOption:{Name:string;Id:string}
  ConcreteOptionId:string;
  ConcreteLocationId:string;
  ConcreteLocation:{Name:string;Id:string}
  ConsistencyClassId:string
  ConsistencyClass:{Name:string;Id:string}
  TransactionRequestConcreteOption:any




  PompTypeId: 0;
  PompType: {
    Id: 0;
    Name: string;
  };
  CarTypeId: number;
  CarType: {
    Id?: number;
    Name: string;
  };
  ApprovedDateTime: string;
  TransactionStartDateTime: string;
  TransactionEndDateTime: string;
  ApprovedUserId: string;
  ApprovedUser: UserListAndDetails;
  Note: string;
  InsertDate: string;
  DistanceInDestination: number;
  StationId: string;
  Station: StationAndDetails;
  Certificate:{File:any,FileName:string;}
  
}
export interface AddRequestTransactionFormModel
  extends BaseSharedRequestTransaction {}

export interface RequestTransactionAndDetails
  extends BaseSharedRequestTransaction {
  Id: string;
}
