import { Col, Form, Row } from "antd";
import GeneralPhoneNumber from "apps/Common/GeneralPhoneNumber";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetCarFilter } from "../ClientSideStates";
import GeneralBuildingInput from "apps/Common/GeneralBuildingInput";
import { MazakaSelect } from "apps/Common/MazakaSelect";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import GeneralUserInput from "apps/Common/GeneralUserInput";
import GeneralCarInput from "apps/Common/GeneralCarInput";
import GeneralPompTypeInput from "apps/Common/GenralPompTypeInput";
import GeneralCarTypeInput from "apps/Common/GeneralCarTypeInput";
import GeneralCarStatusInput from "apps/Common/GeneralCarStatusInput";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { carFilter } = useSelector((state: RootState) => state.car);
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();
   
    let currentFilter = { ...carFilter };
    for (let key in formValues) {
      if (!formValues[key]) {
        delete formValues[key];
      }
      if (!currentFilter?.key) {
        delete currentFilter[key];
      }
    }
    const newFilter = { ...currentFilter, ...formValues };

    await dispatch(
      hanldleSetCarFilter({ filter: newFilter, })
    );
    mazakaForm.setSuccess(1000, () => {}, "İşlem Başarılı");
    onFinish();
  };

  useEffect(() => {
    let data = { ...carFilter };
      
    form.setFieldsValue({
    DriverId:data?.DriverId || undefined,
    VehicleId:data?.VehicleId || undefined,
    PompTypeId:data?.PompTypeId || undefined,
    VehicleTypeId:data?.VehicleTypeId || undefined,
    Status:data?.Status || undefined,
    });
  }, [carFilter]);
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
       

          <Row gutter={[0, 20]}>
          <GeneralUserInput
              name="DriverId"
              label={"Şoför"}
              placeholder="Şoför"
              className="!m-0"
              allowClear
              xs={24}
              externalFilter={{
                PageSize: -1,
                RoleId: "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469",
              }}
            />
            <GeneralCarInput
              name="VehicleId"
              label={"Araç"}
              placeholder="Araç"
              className="!m-0"
              span={24}
              allowClear={true}
            />
            <GeneralPompTypeInput
                  name="PompTypeId"
                  label={"Pompa Tipi"}
                  placeholder="Pompa Tipi"
                  className="!m-0"
                  xs={24}
                />
              <GeneralCarTypeInput
                  name="VehicleTypeId"
                  label={"Araç Tipi"}
                  placeholder="Araç Tipi"
                  className="!m-0"
                  xs={24}
                />
              <GeneralCarStatusInput
                name="Status"
                  label={"Durum"}
                  placeholder="Durum"
                  className="!m-0"
                  xs={24}
                />


          
            <Col xs={24}>
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                Filtrele
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
