
import {  Col, Form, Row, Tabs, } from "antd";
import { TabsProps } from "antd/lib";
import { useGetCarTypes } from "apps/Car/ServerSideStates";
import { addCar,  updateCarWithPut } from "apps/Car/Services";
import GeneralCarStatusInput from "apps/Common/GeneralCarStatusInput";
import GeneralCarTypeInput from "apps/Common/GeneralCarTypeInput";
import GeneralUserInput from "apps/Common/GeneralUserInput";
import GeneralPompTypeInput from "apps/Common/GenralPompTypeInput";
import { MazakaForm } from "apps/Common/MazakaForm";
import { MazakaInput } from "apps/Common/MazakaInput";
import MazakaInputNumber from "apps/Common/MazakaInputNumber";
import { MazakaTextArea } from "apps/Common/MazakaTextarea";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralAddOrUpdateFormProps } from "models/Client/GeneralAddOrUpdate";
import { FC, useEffect, useState } from "react";


const AddOrUpdateCarForm: FC<GeneralAddOrUpdateFormProps> = ({ selectedRecord,onFinish }) => {
  const [form] = Form.useForm();
  const carTypes= useGetCarTypes({PageSize:-1})
  const { mazakaForm, formActions } = useMazakaForm(form);
  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    let formValues = form.getFieldsValue();
    formValues["name"] = formValues["Plate"]
    const vehicleTypesData= carTypes?.data?carTypes.data.Data:[]
    const tabName = selectedTab==="pump"?"pompa":"mikser"
    const findItem = vehicleTypesData.find((item:any)=>item.Name?.trim().toLowerCase()===tabName) 
    formValues["VehicleTypeId"] = findItem.Id
    try {
      if(selectedRecord)
      {
        let data = {...selectedRecord}
       
        data["Description"]=formValues?.Description
        delete data["Driver"]
     
        await updateCarWithPut({...data,...formValues})
      }
      else{
        await addCar({...formValues})
      }
  
      mazakaForm.setSuccess(2000, () => {
        "İşlem Başarılı";
      });

      onFinish()
    } catch (error: any) {
     showServiceErrorMessage(error,mazakaForm,"Car")
    }
  };
  useEffect(() => {
    if (selectedRecord) {
      form.setFieldsValue({ ...selectedRecord });
      const vehicleTypeName = selectedRecord?.VehicleType?.Name?.trim().toLowerCase()
      const findTabItem = tabItems?.find((item:any)=>item.label.trim().toLowerCase()===vehicleTypeName)
     
      if(findTabItem)
      setSelectedTab(findTabItem.key)
    }
  }, [selectedRecord]);


  const tabItems: TabsProps['items'] = [
    
    {
      key: 'mixer',
      label: 'Mikser',
     
    },
    {
      key: 'pump',
      label: 'Pompa',
     
    },
  
  ];
  const [selectedTab,setSelectedTab] = useState('mixer')
 
  return (
    <>
      <Col span={24}>
        <MazakaForm form={form} onFinish={handleOnFinish} {...formActions}>
          <Row gutter={[0, 10]}>
            <Col xs={24} >
            <Tabs activeKey={selectedTab} items={tabItems} onChange={(key)=>{setSelectedTab(key)}} />
            </Col>
            
            <MazakaInput
              className="!m-0"
              label={"Plaka"}
              placeholder="Plaka"
              name={"Plate"}
              xs={24}
              rules={[{ required: true, message: "" }]}
            />
            {
              selectedTab ==="mixer"&&
              <>
            <MazakaInputNumber
              name="Capacity"
              label={"Kapasite"}
              placeholder="Kapasite"
              className="!m-0"
              xs={24}
              suffix={"m³"}
              rules={[{ required: true, message: "" }]}
            />
              </>
            }

            <GeneralCarStatusInput
              name="StatusId"
              label={"Durumu"}
              placeholder="Durum"
              className="!m-0"
              xs={24}
              
              rules={[{ required: true, message: "" }]}
            />

           

           

            <Col xs={24}>
              <Row>
                <GeneralUserInput
                  name="DriverId"
                  label={"Şoför"}
                  placeholder="Şoför"
                  className="!m-0"
                  rules={[{ required: true, message: "" }]}
                  xs={24}
                
                  externalFilter={{PageSize:-1,RoleId:"98b3e5cc-cf74-4ee9-bc6a-6871c51a6469"}}
                />
             
              </Row>
            </Col>
            <Col xs={24}>
              <Row>
                {
                  selectedTab==="pump"&&
                  <>
                <GeneralPompTypeInput
                  name="PompTypeId"
                  label={"Pompa Tipi"}
                  placeholder="Pompa Tipi"
                  className="!m-0"
                  rules={[{ required: true, message: "" }]}
                  xs={24}
                />
                  </>
                }
               
              </Row>
            </Col>
            

           
            <MazakaTextArea
              name="Description"
              label={"Açıklama"}
              placeholder="Açıklama"
              xs={24}
              className="!m-0"
            />
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default AddOrUpdateCarForm;
