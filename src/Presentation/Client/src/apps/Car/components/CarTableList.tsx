import {
  DeleteOutlined,
  EditOutlined,
  EllipsisOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { Col, Drawer, Dropdown, Modal, Spin, Table, Tag } from "antd";
import { ItemType } from "antd/es/menu/interface";
import { openNotificationWithIcon } from "helpers/Notifications";
import {  lazy, Suspense, useState } from "react";
import { deleteCar } from "../Services";
import { useQueryClient } from "react-query";
import endpoints from "apps/Car/EndPoints";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetCarFilter } from "../ClientSideStates";
import { useGetCars } from "../ServerSideStates";
import { showServiceErrorMessage } from "helpers/ShowErrorCatch";

const AddOrUpdateCarForm = lazy(
  () => import("./AddOrUpdateCar/AddOrUpdateCarForm")
);

const CarTableList = () => {
  const queryClient = useQueryClient();
  const {  carFilter:filter } = useSelector((state: RootState) => state.car);
  const dispatch = useDispatch();
  const [selectedRecord, setSelectedRecord] = useState<any | null>(null);
  const [isShowEditDrawer, setIsEditDrawer] = useState<boolean>(false);

  const tableItemDropdownMenu = (record: any): ItemType[] => [
    {
      key: "1",
      onClick: async () => {
        await setSelectedRecord(record);
        setIsEditDrawer(true);
      },
      icon: <EditOutlined />,
      label: "Güncelle",
    },
    {
      key: "2",
      onClick: () => {
        confirm(record);
      },
      icon: <DeleteOutlined />,
      label: "Sil",
    },
  ];
  const confirm = (record: any) => {
    Modal.confirm({
      title: "Uyarı",
      icon: <ExclamationCircleOutlined />,
      content: `Bu öğe silinecek. Onaylıyor musunuz?`,
      okText: "Sil",
      cancelText: "Vazgeç",
      onOk: async () => {
        try {
          await deleteCar(record);
          openNotificationWithIcon("success", "İşlem Başarılı");
          queryClient.resetQueries({
            queryKey: endpoints.getCarListFilter,
            exact: false,
          });
        } catch (error: any) {
          showServiceErrorMessage(error,{},"Car",true)
        }
      },
    });
  };

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetCarFilter({ filter: newFilter }));
  };

  const cars = useGetCars(filter);


  const columns = [
    {
      title: "Adı",
      dataIndex: "Name",
      key: "Name",
      sorter: (a: any, b: any) => a?.Name.localeCompare(b?.Name)
    },
    {
      title: "Şoför",
      dataIndex: "DriverName",
      key: "DriverName",
      sorter: (a: any, b: any) => a?.DriverName.localeCompare(b?.DriverName)
    },
  
    {
      title: "Plaka", 
      dataIndex: "Plate",
      key: "Plate",
      sorter: (a: any, b: any) => a?.Plate?.localeCompare(b?.Plate)
    },
    {
      title: "Kapasite (m³)",
      dataIndex: "Capacity",
      key: "Capacity",
      sorter: (a: any, b: any) => Number(a?.Capacity ?? 0) - Number(b?.Capacity ?? 0)
      
    },
    {
      title: "Pompa Tipi", 
      dataIndex: ["PompType", "Name"],
      key: "PompTypeId",
      sorter: (a: any, b: any) => (a?.PompType?.Name || '').localeCompare(b?.PompType?.Name || '')
    },
    {
      title: "Araç Tipi", 
      dataIndex: ["VehicleType", "Name"],
      key: "VehicleTypeId",
      sorter: (a: any, b: any) => (a?.VehicleType?.Name || '').localeCompare(b?.VehicleType?.Name || '')

    },

    {
      title: "Durum",
      render: (_: any, record: any) => {
        return (
          <>
            <Tag color={record.StatusId === 1 ? "blue" : "error"}>
              {record.Status.Name}
            </Tag>
          </>
        );
      },
      sorter: (a: any, b: any) => a?.Status?.Name.localeCompare(b?.Status?.Name),
      key: "status",
    },
    {
      title: "",
      dataIndex: "edit",
      key: "edit",
      width: "8%",
      render: (key: any, record: any) => (
        <Col className="text-end pr-2">
          <Dropdown menu={{ items: tableItemDropdownMenu(record) }}>
            <EllipsisOutlined className="text-xl" />
          </Dropdown>
        </Col>
      ),
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={cars.isLoading || cars.isFetching}
        dataSource={cars.data ? cars.data.Data : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: cars.data?.FilteredCount || 0,
          current: cars.data?.PageIndex,
          pageSize: cars.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <Drawer
        title="Aracı Güncelle"
        open={isShowEditDrawer}
        onClose={() => {
          setIsEditDrawer(false);
        }}
      >
        <>
        {
          isShowEditDrawer&&
          <Suspense fallback={<Spin/>}>
            <AddOrUpdateCarForm selectedRecord={selectedRecord}
              onFinish={()=>{
                setIsEditDrawer(false);
                queryClient.resetQueries({
                  queryKey: endpoints.getCarListFilter,
                  exact: false,
                });
      
              }}
            
            />

          </Suspense>

        }
        </>
      </Drawer>
    </Col>
  );
};

export default CarTableList;
