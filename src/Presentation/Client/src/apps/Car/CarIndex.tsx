import { LinkOutlined, PlusOutlined } from "@ant-design/icons";
import { <PERSON><PERSON>, Col, Drawer, Row, Spin, Typography } from "antd";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import { lazy, Suspense, useState } from "react";
import CarTableList from "./components/CarTableList";
import { addCarType, deleteCarType, updateCarTypeWithPut } from "./Services";
import endpoints from "apps/Car/EndPoints"
import { useGetCarTypes } from "./ServerSideStates";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { handleResetCarFilter, hanldleSetCarTypeFilter } from "./ClientSideStates";
import { useQueryClient } from "react-query";
import DetailsFilter from "./components/DetailsFilter";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
const AddOrUpdateCarForm = lazy(
  () => import("./components/AddOrUpdateCar/AddOrUpdateCarForm")
);
const StatusIndex = lazy(
  () => import("apps/Status/StatusIndex")
);


const CarIndex = () => {
    const queryClient = useQueryClient();
    const {Text} = Typography
    const [isShowAddOrUpdateCarStatusDrawer,setIsShowAddOrUpdateCarStatusDrawer] = useState(false)
    const [isShowAddCarDrawer,setIsShowAddCarDrawer] = useState(false)
    const {carTypeFilter}  = useSelector((state:RootState)=>state.car)
    const {carFilter}  = useSelector((state:RootState)=>state.car)
    const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] = useState(false);
    
    let carTypes= useGetCarTypes(carTypeFilter)
    
    return ( <>
    <MazakaLayout title={"Araç Listesi"} headDescription={"Araç sayfası,sistemde tüm kayıtlı araçların bilgilerini görüntülemenizi sağlar"} >
        <Row gutter={[20,20]}>
            <Col span={24} className="!flex justify-end gap-2" >
                <Button onClick={()=>{setIsShowAddCarDrawer(true)}} className="!flex items-center" type="primary">
                    <div className="!flex items-center gap-1">
                        <PlusOutlined/>
                        <Text className="!text-white">Araç Ekle</Text>
                    </div>
                </Button>
                <Button onClick={()=>{setIsShowAddOrUpdateCarStatusDrawer(true)}} className="!flex items-center" >
                    <div className="!flex items-center gap-1">
                        <LinkOutlined/>
                        <Text >Araç Tipleri</Text>
                    </div>
                </Button>
                <MazakaDetailsFilterButton
                  setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
                />
                {Object.entries(carFilter).length > 3 && (
                  <>
                    <MazakaClearFilterButton
                      actionFunk={ handleResetCarFilter}
                     
                    />
                  </>
                )}
            </Col>
            <Col span={24} >
               <CarTableList/>
            </Col>
        </Row>
        

    </MazakaLayout>

    <MazakaDrawer
        title={"Araç Tipleri"}
        placement="right"
        open={isShowAddOrUpdateCarStatusDrawer}
        toggleVisible={() => {
          
          setIsShowAddOrUpdateCarStatusDrawer(!isShowAddOrUpdateCarStatusDrawer);
        }}
        layoutType="strecth"
      >
        <>
        {
          isShowAddOrUpdateCarStatusDrawer&&
          <Suspense fallback={<Spin/>}>

            <StatusIndex
             postService={addCarType}
             updateService={updateCarTypeWithPut}
             deleteService={deleteCarType}
             getQueryEndPoint={endpoints.getCarTypeListFilter}
             getQueryObj={carTypes}
             filter={carTypeFilter}
             actionFilterFunc={hanldleSetCarTypeFilter}
             formType="status"
           />
          </Suspense>
        }
        </>
      </MazakaDrawer>
      <Drawer title="Yeni Araç Ekle" open={isShowAddCarDrawer} onClose={()=>{setIsShowAddCarDrawer(false)}}>
        <>
        {
          isShowAddCarDrawer&&
          <Suspense fallback={<Spin/>}>

            <AddOrUpdateCarForm  
             onFinish={()=>{
                setIsShowAddCarDrawer(false)
                queryClient.resetQueries({
                  queryKey: endpoints.getCarListFilter,
                  exact: false,
                });
      
              }}
            
            />
          </Suspense>
        }
        </>
    </Drawer>
    <Drawer
          title="Detaylı Filtre"
          open={isShowFilterDetailsDrawer}
          onClose={() => {
            setIsShowFilterDetailsDrawer(false);
          }}
        >
          <>
            {isShowFilterDetailsDrawer && (
              <Suspense fallback={<Spin />}>
                <DetailsFilter
                  onFinish={() => {
                    setIsShowFilterDetailsDrawer(false);
                  }}
                />
              </Suspense>
            )}
          </>
        </Drawer>
    
    
    </> );
}
 
export default CarIndex;