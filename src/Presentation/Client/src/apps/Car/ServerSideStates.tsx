import { useQuery } from "react-query";
import endpoints from "apps/Car/EndPoints";
import { getCarListFilter, getCarStatusFilter, getCarTypeListFilter } from "./Services";


export const useGetCars = (filter: any) => {

  const query = useQuery(
    [endpoints.getCarListFilter, filter],
    () => {
      return getCarListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetCarStatus= (filter: any) => {

  const query = useQuery(
    [endpoints.getCarStatusFilter, filter],
    () => {
      return getCarStatusFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
export const useGetCarTypes= (filter: any) => {
    const query = useQuery(
      [endpoints.getCarTypeListFilter, filter],
      () => {
        return getCarTypeListFilter(filter);
      },
      {
        refetchOnWindowFocus: false,
        refetchOnMount: false,
      }
    );
  
    return query;
  };