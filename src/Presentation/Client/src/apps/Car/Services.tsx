import {deleteRequest, get, patch, post, put} from "services/BaseClient/Client"
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Car/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";
import { AddCarFormModel, CarListAndDetails, CarTypeListAndDetails } from "./Models";



export const getCarListFilter = async (filter: any): Promise<DataResponse<CarListAndDetails>> => {

  const query = CreateUrlFilter(filter)
    const url = `${endpoints.getCarListFilter}?${query}`;
   
    const config = headers.content_type.application_json;
    return get<DataResponse<CarListAndDetails>>(url, config);
  };
  export const getCarStatusFilter = async (filter: any): Promise<DataResponse<CarListAndDetails>> => {

    const query = CreateUrlFilter(filter)
      const url = `${endpoints.getCarStatusFilter}?${query}`;
     
      const config = headers.content_type.application_json;
      return get<DataResponse<CarListAndDetails>>(url, config);
    }

  export const addCar = async (data: any): Promise<DataResponse<AddCarFormModel>> => {
    const url = `${endpoints.addCar}`;
    const config = headers.content_type.application_json;
    return post<DataResponse<CarListAndDetails>>(url, data, config);
  };
  export const updateCarWithPut = async (data: any): Promise<DataResponse<CarListAndDetails>> => {
    const url = `${endpoints.updatCarWithPut}`;
    const config = headers.content_type.application_json;
    return put<DataResponse<CarListAndDetails>>(url, data, config);
  };
  export const updateCarWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
    const url = `${endpoints.updateCarWithPatch}`;
    const config = headers.content_type.application_json;
    return patch<DataResponse<any>>(url, data, config);
  };
  export const deleteCar = async (data: any): Promise<DataResponse<any>> => {
    const url = `${endpoints.deleteCar}/${data.Id}`;
    const config:any = headers.content_type.application_json;
    return deleteRequest<DataResponse<CarListAndDetails>>(url, data, config);
  };

  export const getCarTypeListFilter = async (filter: any): Promise<DataResponse<CarTypeListAndDetails>> => {
    const query = CreateUrlFilter(filter)
      const url = `${endpoints.getCarTypeListFilter}?${query}`;
      const config = headers.content_type.application_json;
      return get<DataResponse<CarTypeListAndDetails>>(url, config);
    };
  
    export const addCarType = async (data: any): Promise<DataResponse<AddCarFormModel>> => {
      const url = `${endpoints.addCarType}`;
      const config = headers.content_type.application_json;
      return post<DataResponse<CarTypeListAndDetails>>(url, data, config);
    };
    export const updateCarTypeWithPut = async (data: any): Promise<DataResponse<CarTypeListAndDetails>> => {
      const url = `${endpoints.updatCarTypeWithPut}`;
      const config = headers.content_type.application_json;
      return put<DataResponse<CarTypeListAndDetails>>(url, data, config);
    };
    export const updateCarTypeWithPatch = async (data: any): Promise<DataResponse<PatchRequest>> => {
      const url = `${endpoints.updateCarTypeWithPatch}`;
      const config = headers.content_type.application_json;
      return patch<DataResponse<any>>(url, data, config);
    };
    export const deleteCarType = async (data: any): Promise<DataResponse<any>> => {
      const url = `${endpoints.deleteCarType}/${data.Id}`;
      const config:any = headers.content_type.application_json;
      return deleteRequest<DataResponse<CarTypeListAndDetails>>(url, data, config);
    };