import { PompType } from "apps/Product/Models";



export interface Status {
  Id: number;
  Name: string;
}

export interface Driver {
  Email: string;
  UserName: string;
  Name: string;
  Surname: string;
  Company: string;
  Phone: string;
  Active: boolean;
}

export interface BaseSharedCarType{
  Name: string;
}

export interface CarTypeListAndDetails {
  Id: number;
 
}

export interface AddCarTypeFormModel extends BaseSharedCarType{

}

interface BaseSharedCar {
  Name: string;
  Plate: string;
  StatusId: number;
  Status: Status;
  CreatedDate: string;
  CreatedUserId: string;
  DriverId: string;
  Driver: Driver;
  PompTypeId: number;
  PompType: PompType;
  CarTypeId: number;
  CarType: CarTypeListAndDetails;
   
  }
  export interface AddCarFormModel extends BaseSharedCar{

  }

  export interface CarListAndDetails extends BaseSharedCar{
 Id: string;
 
  }