import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  carFilter: {
    PageIndex: 1,
    PageSize: 20,
    IncludeProperties:["PompType","VehicleType","Status","Driver"] 
  },
  carTypeFilter: {
    PageIndex: 1,
    PageSize: 20,
  },
};

const carSlice = createSlice({
  name: "carSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetCarFilter: (state, action) => {
      let data = action.payload;
      state.carFilter = data.filter;
      state.carFilter.IncludeProperties =["PompType","VehicleType","Status","Driver"]; 

    },
    handleResetAllFieldsCar: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetCarFilter: (state) => {
      state.carFilter = {
        PageIndex: 1,
        PageSize: 20,
        IncludeProperties:["PompType","VehicleType","Status","Driver"] 

      };
    },
    hanldleSetCarTypeFilter: (state, action) => {
      let data = action.payload;
      state.carTypeFilter = data.filter;
      
    },
    handleResetCarTypeFilter: (state) => {
      state.carTypeFilter = {
        PageIndex: 1,
        PageSize: 20,
      };
    },
  },
});

export const {
  handleResetCarFilter,
  handleResetAllFieldsCar,
  handleResetCarTypeFilter,
  hanldleSetCarFilter,
  hanldleSetCarTypeFilter,
} = carSlice.actions;
export default carSlice;
