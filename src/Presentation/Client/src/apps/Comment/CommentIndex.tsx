import { Col, Drawer, Row, Spin } from "antd";
import { MazakaLayout } from "apps/Common/MazakaLayout";
import CommentTableList from "./Components/CommentTableList";
import MazakaDetailsFilterButton from "apps/Common/MazakaDetailsFilterButton";
import { lazy, Suspense, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import MazakaClearFilterButton from "apps/Common/MazakaClearFilterButton";
import { handleResetCommentFilter } from "./ClientSideStates";

const DetailsFilter = lazy(
  () => import("./Components/DetailsFilter")
);


const CommentIndex = () => {
  const [isShowFilterDetailsDrawer, setIsShowFilterDetailsDrawer] =
    useState(false);
    const { filter } = useSelector((state: RootState) => state.comment);

  return (
    <>
      <MazakaLayout
        title={"Yorum Listesi"}
        headDescription={
          "Yorum Listesi Sayfası, sistemdeki tüm yorum kayıtlarını görüntülemenizi sağlar. "
        }
      >
       
        <Row gutter={[20, 20]}>
        <Col xs={24} className="!flex justify-end" >

          <MazakaDetailsFilterButton
                  setIsShowDetailsFilter={setIsShowFilterDetailsDrawer}
                />
                {Object.entries(filter).length > 3 && (
                  <>
                    <MazakaClearFilterButton
                      actionFunk={ handleResetCommentFilter}
                     
                    />
                  </>
                )}
        </Col>
          <Col span={24}>
            <CommentTableList />
          </Col>
        </Row>
      </MazakaLayout>

      <Drawer
        title="Detaylı Filtre"
        open={isShowFilterDetailsDrawer}
        onClose={() => {
          setIsShowFilterDetailsDrawer(false);
        }}
      >
        <>
          {isShowFilterDetailsDrawer && (
            <Suspense fallback={<Spin />}>
              <DetailsFilter
                onFinish={() => {
                  setIsShowFilterDetailsDrawer(false);
                }}
              />
            </Suspense>
          )}
        </>
      </Drawer>
    </>
  );
};

export default CommentIndex;
