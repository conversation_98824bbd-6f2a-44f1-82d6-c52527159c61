import { useQuery } from "react-query";
import endpoints from "apps/Comment/EndPoints";
import { getCommentFilesFilter, getCommentListFilter } from "./Services";

export const useGetComments = (filter: any) => {
  const query = useQuery(
    [endpoints.getCommentListFilter, filter],
    () => {
      return getCommentListFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};

export const useGetCommentFiles = (filter:any) => {
  const query = useQuery(
    [endpoints.getCommentFilesFilter, filter],
    () => {
      return getCommentFilesFilter(filter);
    },
    {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    }
  );

  return query;
};
