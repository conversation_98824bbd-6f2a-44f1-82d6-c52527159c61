import { Route } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";


const CommentIndex= lazy(() => import('./CommentIndex'))


export const commentRouteList = [
  <Route key={"commentRouteList"}>
    <Route
      path={"/comment/list"}
      element={
        <Suspense fallback={<Spin/>}>
        <CommentIndex/>
        </Suspense>
      }
    />
  </Route>,
];
