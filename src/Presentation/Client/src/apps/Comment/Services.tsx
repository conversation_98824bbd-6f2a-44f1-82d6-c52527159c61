import {
  deleteRequest,
  get,
  patch,
  post,
  put,
} from "services/BaseClient/Client";
import headers from "services/BaseClient/Headers.json";
import { DataResponse } from "services/BaseClient/BaseResponseModel";
import endpoints from "apps/Comment/EndPoints";
import { CreateUrlFilter } from "helpers/CreateURLFilter";
import { PatchRequest } from "models/Services/PatchRequest";

export const getCommentListFilter = async (
  filter: any
): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getCommentListFilter}?${query}`;

  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const getCommentFilesFilter = async (filter:any): Promise<DataResponse<any>> => {
  const query = CreateUrlFilter(filter);
  const url = `${endpoints.getCommentFilesFilter}?${query}`;
  const config = headers.content_type.application_json;
  return get<DataResponse<any>>(url, config);
};

export const addComment = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.addComment}`;
  const config = headers.content_type.application_json;
  return post<DataResponse<any>>(url, data, config);
};
export const updateCommentWithPut = async (
  data: any
): Promise<DataResponse<any>> => {
  const url = `${endpoints.updatCommentWithPut}`;
  const config = headers.content_type.application_json;
  return put<DataResponse<any>>(url, data, config);
};
export const updateCommentWithPatch = async (
  data: any
): Promise<DataResponse<PatchRequest>> => {
  const url = `${endpoints.updateCommentWithPatch}`;
  const config = headers.content_type.application_json;
  return patch<DataResponse<any>>(url, data, config);
};
export const deleteComment = async (data: any): Promise<DataResponse<any>> => {
  const url = `${endpoints.deleteComment}/${data.Id}`;
  const config: any = headers.content_type.application_json;
  return deleteRequest<DataResponse<any>>(url, data, config);
};
