import { createSlice } from "@reduxjs/toolkit";

const InitialState: any = {
  filter: {
    PageIndex: 1,
    PageSize: 20,
    IncludeProperties:["ContractProductTransaction.Station","Vehicle","Commenter","ContractProductTransaction.Contract.ContractProduct","ContractProductTransaction.TransactionRequest.Building.Company"] 
  },
  
};

const commentSlice = createSlice({
  name: "CommentSlice",
  initialState: InitialState,
  reducers: {
    hanldleSetCommentFilter: (state, action) => {
      let data = action.payload;
      state.filter = data.filter;
    },
    handleResetAllFieldsComment: (state) => {
      Object.assign(state, InitialState);
    },
    handleResetCommentFilter: (state) => {
      state.filter = {
        PageIndex: 1,
        PageSize: 20,
        IncludeProperties:["ContractProductTransaction.Station","Vehicle","Commenter","ContractProductTransaction.Contract.ContractProduct","ContractProductTransaction.TransactionRequest.Building.Company"] 

      };
    },
  
  },
});

export const {
  handleResetCommentFilter,
  handleResetAllFieldsComment,
  hanldleSetCommentFilter,
} = commentSlice.actions;
export default commentSlice;
