import { FC } from "react";
import { useGetCommentFiles } from "../ServerSideStates";
import { DownloadOutlined } from "@ant-design/icons";
import { Col, Table, Tooltip } from "antd";
import { setBackEndUrl } from "helpers/SetBackEndUrl";

interface AddOrUpdateCommentFilesProps {
  selectedRecord: any;
 
}
const AddOrUpdateCommentFiles: FC<AddOrUpdateCommentFilesProps> = ({
  selectedRecord,

}) => {
  const files: any = useGetCommentFiles({CommentId:selectedRecord?.Id,PageSize:-1});
  const columns = [
    {
      title: "Adı",
      dataIndex: "FileName",
      render:(value:string)=>{
        if (!value) return "";

        const lastDotIndex = value.lastIndexOf(".");
        const fileNameFull = value.substring(0, lastDotIndex);
        const fileType = value.substring(lastDotIndex + 1);
        const shortenedName = fileNameFull.length > 12 
          ? `${fileNameFull.slice(0, 12)}` 
          : fileNameFull;
    
        return (
          <Tooltip title={fileNameFull}>
            <span>{shortenedName}.{fileType}</span>
          </Tooltip>
        );
      }
    },
    {
      title: "",
      dataIndex: "FileName",
      render: (value:string) => {
        return (
          <Col xs={24} className="!flex justify-end">
            <a
            href={`${setBackEndUrl()}/Uploads/CommentFile/${value}`}
            >

            <DownloadOutlined
              className="!text-blue-500"
             
            />
            </a>
          </Col>
        );
      },
    },
  ];

  return (
    <>
      <Col span={24}>
        <Table
          columns={columns}
          loading={files.isLoading || files.isFetching}
          dataSource={files.data ? files.data.Data : []}
          rowKey={"Id"}
          scroll={{ x: 700 }}
          pagination={{
            position: ["bottomRight"],
            className: "!px-0",

            total: files.data?.Value?.length || 0,

            pageSize: 20,
            showLessItems: true,
            size: "small",
            showSizeChanger: true,
            locale: { items_per_page: "" },
            showTotal: (e) => `${e}`,
          }}
        />
      </Col>
    </>
  );
};

export default AddOrUpdateCommentFiles;
