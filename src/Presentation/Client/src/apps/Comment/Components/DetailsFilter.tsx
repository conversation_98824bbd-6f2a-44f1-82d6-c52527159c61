import { Col, Form, Row } from "antd";
import { MazakaButton } from "apps/Common/MazakaButton";
import { MazakaForm } from "apps/Common/MazakaForm";
import useMazakaForm from "hooks/useMazakaForm";
import { GeneralDetailsFilterProps } from "models/Client/GeneralDetailsFilter";
import { FC, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { hanldleSetCommentFilter } from "../ClientSideStates";
import GeneralUserInput from "apps/Common/GeneralUserInput";
import GeneralCarInput from "apps/Common/GeneralCarInput";
import GeneralCompanies from "apps/Common/GeneralCompanyInput";
import { MazakaSelect } from "apps/Common/MazakaSelect";

const DetailsFilter: FC<GeneralDetailsFilterProps> = ({ onFinish }) => {
  const [form] = Form.useForm();
  const { mazakaForm, formActions } = useMazakaForm(form);
  const { filter } = useSelector((state: RootState) => state.comment);
  const dispatch = useDispatch();

  const handleOnFinish = async () => {
    mazakaForm.setLoading();
    const formValues = form.getFieldsValue();

    let currentFilter = { ...filter };
    for (let key in formValues) {
      if (formValues[key] !== 0 && !formValues[key]) {
        delete formValues[key];
      }
      if (!currentFilter?.key) {
        delete currentFilter[key];
      }
    }
    if (formValues["HasFile"] === 0 || formValues["HasFile"] === 1) {
      formValues["HasFile"] = Boolean(formValues["HasFile"]);
    }
    const newFilter = { ...currentFilter, ...formValues };

    await dispatch(hanldleSetCommentFilter({ filter: newFilter }));
    mazakaForm.setSuccess(1000, () => {}, "İşlem Başarılı");
    onFinish();
  };

  useEffect(() => {
    form.setFieldsValue({
      VehicleId: filter?.VehicleId,
      DriverId: filter?.DriverId,
      CompanyId: filter?.CompanyId,
      HasFile:
        typeof filter?.HasFile === "boolean"
          ? Number(filter?.HasFile)
          : undefined,
    });
  }, [filter]);
  return (
    <>
      <Col span={24}>
        <MazakaForm
          form={form}
          onFinish={handleOnFinish}
          submitButtonVisible={false}
        >
          <Row gutter={[0, 20]}>
            <GeneralCompanies
              name="CompanyId"
              label={"Firma"}
              placeholder="Firma"
              className="!m-0"
              xs={24}
              allowClear={true}
            />
            <GeneralUserInput
              name="DriverId"
              label={"Şoför"}
              placeholder="Şoför"
              className="!m-0"
              allowClear
              xs={24}
              externalFilter={{
                PageSize: -1,
                RoleId: "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469",
              }}
            />
            <GeneralCarInput
              name="VehicleId"
              label={"Araç"}
              placeholder="Araç"
              className="!m-0"
              span={24}
              allowClear={true}
            />
            <MazakaSelect
              label="Resim ve vedeo olanlar"
              placeholder="Resim ve vedeo olanlar"
              xs={24}
              name="HasFile"
              allowClear
              options={[
                { label: "Dosya Sahibi Olanlar", value: 1 },
                { label: "Dosya Sahibi Olmayanlar", value: 0 },
              ]}
              onChange={(status: boolean) => {
                form.setFieldValue("IsHasImageVideo", status);
              }}
            />
            <Col xs={24}>
              <MazakaButton
                htmlType="submit"
                processType={formActions.submitProcessType}
              >
                Filtrele
              </MazakaButton>
            </Col>
          </Row>
        </MazakaForm>
      </Col>
    </>
  );
};

export default DetailsFilter;
