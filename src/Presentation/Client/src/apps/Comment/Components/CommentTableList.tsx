import { Col, Table, Tooltip, Typography } from "antd";

import { useDispatch, useSelector } from "react-redux";
import { RootState } from "store/Reducers";
import { useGetComments } from "../ServerSideStates";
import { hanldleSetCommentFilter } from "../ClientSideStates";
import moment from "moment";
import { FileOutlined } from "@ant-design/icons";
import { useState } from "react";
import { MazakaDrawer } from "apps/Common/MazakaDrawer";
import AddOrUpdateCommentFiles from "./AddOrUpdateCommentFiles";
import { useQueryClient } from "react-query";
import endpoints from "../EndPoints";
import dayjs from "dayjs";

const CommentTableList = () => {
  const queryClient = useQueryClient();
  const { filter } = useSelector((state: RootState) => state.comment);
  const dispatch = useDispatch();
  const { Text } = Typography;
  const [isShowFilesDrawer, setIsShowFilesDrawer] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);

  const handleChangePagination = (pageNum: number, pageSize: number) => {
    let newFilter = { ...filter, PageIndex: pageNum, PageSize: pageSize };
    dispatch(hanldleSetCommentFilter({ filter: newFilter }));
  };

  const comments = useGetComments(filter);

  const columns = [
    {
      title: "Şantiye Adı",
      dataIndex: ["Building", "Name"],
      sorter: (a: any, b: any) =>
        a?.Building?.Name.localeCompare(
          b?.Building?.Name
        ),
    },
    {
      title: "Firma ",

      render: (value: string, record: any) => {
        return (
          <>
            <div className="!flex flex-col ">
              <div className="!flex flex-wrap gap-1">
                
                      <span>
                        <span color="blue">{record?.Building?.Company?.Name || ""}</span>
                      </span>
                <div></div>
              </div>
              <div>
                <Text>{record?.ContractProductTransaction?.DocumentNo}</Text>
              </div>
              <div className="!flex gap-2">
                <span>{record?.ContractProductTransaction?.StartDate}</span>
                <span>{record?.ContractProductTransaction?.EndDate}</span>
              </div>
              <div>
                <Text>
                  {record?.ContractProductTransaction?.SendingAmount + " m³"}
                </Text>
              </div>
            </div>
          </>
        );
      },
    },

    {
      title: "Yorum Yapan Kullanıcı",
      dataIndex: "Commenter",
      key: "Commenter",
      render: (_: string, record: any) => {
        return (
          <>
            <Text>{`${record?.Commenter?.Name || ""} ${
              record?.Commenter?.Surname || ""
            }`}</Text>
          </>
        );
      },
      sorter: (a: any, b: any) =>
        a?.Commenter?.Name.localeCompare(b?.Commenter?.Name),
      
    },
    {
      title: "Yorum Yapılan Şoför",
      dataIndex: ["Driver", "Name"],
      key: "Driver",
      sorter: (a: any, b: any) =>
        a?.Driver?.Name.localeCompare(b?.Driver?.Name),
    },
    {
      title: "Araç",
      dataIndex: ["Vehicle", "Name"],
      key: "Vehicled",
      sorter: (a: any, b: any) =>
        a?.Vehicle?.Name.localeCompare(b?.Vehicle?.Name),
    },

    {
      title: "Açıklama",
      dataIndex: "Text",
      key: "Text",
      render: (value: string) => {
        if (value) {
          return (
            <>
              {value.length <= 30 ? (
                <>
                  <Text>{value}</Text>
                </>
              ) : (
                <>
                  <Tooltip title={value}>
                    <Text>{value.slice(0, 30)}</Text>
                  </Tooltip>
                </>
              )}
            </>
          );
        }
      },
    },
    {
      title: "Yorum Puanı",
      dataIndex: "Rate",
      key: "Rate",
      sorter: (a: any, b: any) => a?.Rate - b?.Rate,
    },

    {
      title: "Yorum Yapılan Tarih",
      dataIndex: "InsertDate",
      key: "InsertDate",
      render: (date: string) => {
        return <>{moment(date).format("DD.MM.YYYY")}</>;
      },
      sorter: (a: any, b: any) =>
        dayjs(a?.InsertDate).valueOf() - dayjs(b?.InsertDate).valueOf(),
    },
    {
      title: "",

      render: (date: string, record: any) => {
        return (
          <>
            {record?.CommentFile?.length > 0 && (
              <Tooltip title={"Dosyaları Gör"}>
                <FileOutlined
                  className="!text-lg !text-blue-500"
                  onClick={async () => {
                    queryClient.resetQueries({
                      queryKey: endpoints.getCommentFilesFilter,
                      exact: false,
                    });
                    await setSelectedRecord(record);
                    setIsShowFilesDrawer(true);
                  }}
                />
              </Tooltip>
            )}
          </>
        );
      },
    },
  ];

  return (
    <Col span={24}>
      <Table
        columns={columns}
        loading={comments.isLoading || comments.isFetching}
        dataSource={comments.data ? comments.data.Data : []}
        rowKey={"Id"}
        scroll={{ x: 700 }}
        pagination={{
          position: ["bottomRight"],
          className: "!px-0",
          onChange: handleChangePagination,
          total: comments.data?.FilteredCount || 0,
          current: comments.data?.PageIndex,
          pageSize: comments.data?.PageSize,
          showLessItems: true,
          size: "small",
          showSizeChanger: true,
          locale: { items_per_page: "" },
          showTotal: (e) => `${e}`,
        }}
      />
      <MazakaDrawer
        title={"Yüklenmiş Dosyalar"}
        placement="right"
        open={isShowFilesDrawer}
        toggleVisible={() => {
          setIsShowFilesDrawer(!isShowFilesDrawer);
        }}
        layoutType="strecth"
      >
        <AddOrUpdateCommentFiles selectedRecord={selectedRecord} />
      </MazakaDrawer>
    </Col>
  );
};

export default CommentTableList;
