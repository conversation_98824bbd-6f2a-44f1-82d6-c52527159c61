@tailwind base;
@tailwind components;
@tailwind utilities;

.mazaka-settings-menu {
  & > .ant-menu-item.ant-menu-item-only-child {
    height: 55px !important;
  }
}
.ant-menu.ant-menu-inline-collapsed-tooltip {
  pointer-events: none;
}
.ant-menu.ant-menu-inline-collapsed-tooltip .ant-menu-item-icon,
.ant-menu.ant-menu-inline-collapsed-tooltip .anticon {
  display: none;
}
.ant-menu.ant-menu-inline-collapsed-tooltip a {
  color: rgba(250, 250, 250, 0.85);
}

#mazaka-drawer .ant-drawer-body {
  background-color: #f5f5f5;
  margin-left: 90px !important;

}
.ant-notification-notice-message{
  color: white !important;
}
.pac-container {
  z-index: 1050; /* This should be higher than the z-index of the drawer */
}
.ant-modal-close:hover{
  background-color: transparent !important;
}
 .ant-card-head{
 padding: 18px 24px !important ;
}

@media (min-width: 2000px) {
  .ant-drawer {
    .ant-drawer-body {
      padding-left: 120px;
    }
  }
}
.sub-menu-sider{
  position: absolute !important;
  left: 5%;
  z-index: 9999;
  flex: 0 0 330px;
    height: 110vh !important;
 
    margin-left: 0px;
    border-right: 2px solid rgb(225, 225, 225);
 
  
}

.truck-animation-container {
  position: relative;
  width: 400px; /* İkonun hareket etmesini istediğiniz genişliği belirleyin */
  height: 100px; /* Kapsayıcının yüksekliği */
  overflow: hidden; /* Kapsayıcı dışına taşmayı engeller */
}
.ant-drawer {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  pointer-events: none;
}
.ant-drawer-inline {
  position: absolute;
}
.ant-drawer-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  background: rgba(38, 38, 38, 0.45);
  pointer-events: auto;
}
.ant-drawer-content-wrapper {
  position: absolute;
  z-index: 1000;
  transition: all 0.3s;
}
.ant-drawer-content-wrapper-hidden {
  display: none;
}
.ant-drawer-left > .ant-drawer-content-wrapper {
  top: 0;
  bottom: 0;
  left: 0;
  box-shadow: 6px 0 16px -8px rgba(0, 0, 0, 0.08), 9px 0 28px 0 rgba(0, 0, 0, 0.05), 12px 0 48px 16px rgba(0, 0, 0, 0.03);
}
.ant-drawer-right > .ant-drawer-content-wrapper {
  top: 0;
  right: 0;
  bottom: 0;
  box-shadow: -6px 0 16px -8px rgba(0, 0, 0, 0.08), -9px 0 28px 0 rgba(0, 0, 0, 0.05), -12px 0 48px 16px rgba(0, 0, 0, 0.03);
}
.ant-drawer-top > .ant-drawer-content-wrapper {
  top: 0;
  right: 0;
  left: 0;
  box-shadow: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);
}
.ant-drawer-bottom > .ant-drawer-content-wrapper {
  right: 0;
  bottom: 0;
  left: 0;
  box-shadow: 0 -6px 16px -8px rgba(0, 0, 0, 0.08), 0 -9px 28px 0 rgba(0, 0, 0, 0.05), 0 -12px 48px 16px rgba(0, 0, 0, 0.03);
}
.ant-drawer-content {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #ffffff;
  pointer-events: auto;
}
.ant-drawer-wrapper-body {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.ant-drawer-header {
  display: flex;
  flex: 0;
  align-items: center;
  padding: 16px 24px;
  font-size: 14px;
  line-height: 22px;
  border-bottom: 1px solid #f0f0f0;
}
.ant-drawer-header-title {
  display: flex;
  flex: 1;
  align-items: center;
  min-width: 0;
  min-height: 0;
}
.ant-drawer-extra {
  flex: none;
}
.ant-drawer-close {
  display: inline-block;
  margin-right: 12px;
  color: rgba(38, 38, 38, 0.45);
  font-weight: 700;
  font-size: 14px;
  font-style: normal;
  line-height: 1;
  text-align: center;
  text-transform: none;
  text-decoration: none;
  background: transparent;
  border: 0;
  outline: 0;
  cursor: pointer;
  transition: color 0.3s;
  text-rendering: auto;
}
.ant-drawer-close:focus,
.ant-drawer-close:hover {
  color: rgba(38, 38, 38, 0.75);
  text-decoration: none;
}
.ant-drawer-title {
  flex: 1;
  margin: 0;
  color: rgba(38, 38, 38, 0.85);
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
}
.ant-drawer-body {
  flex: 1;
  min-width: 0;
  min-height: 0;
  padding: 24px;
  overflow: auto;
}
.ant-drawer-footer {
  flex-shrink: 0;
  padding: 10px 16px;
  border-top: 1px solid #f0f0f0;
}

@keyframes moveTruck {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(100px);
  }
  50% {
    transform: translateX(200px);
  }
  75% {
    transform: translateX(300px);
  }
  100% {
    transform: translateX(400px);
  }
}

.truck-icon {
  font-size: 20px;
  position: absolute;
  top: 0%;
  left: 0;
  transform: translateY(0%);
}

.truck-icon.move-truck {
  animation: moveTruck 4s linear infinite;
}

/* blink */
.activity {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-bottom: 1rem;
}

.blink {
  color: red;
  margin-right: 0.5rem;
  animation: blink 2s steps(5, start) infinite;
  -webkit-animation: blink 1s steps(5, start) infinite;
}
@keyframes blink {
  to {
    visibility: hidden;
  }
}
@-webkit-keyframes blink {
  to {
    visibility: hidden;
  }
}
.noblink {
  margin-right: 0.5rem;
}

.mazaka-settings-menu > .ant-menu-item.ant-menu-item-only-child {
  height: 55px !important;
}
/* @import url(./Ant.css); */

.ant-steps-item-title{
  font-size:14px !important;
}
.ant-steps-item-description{
  font-size:12px !important;
}

.ant-card-actions li{
  display: flex !important ;
  align-items: center !important;
  justify-content: center !important;
}

.fc-timegrid-slot-lane{
  height: 40px !important;
}
.fc-v-event{
  border:none !important
}
.ant-card-actions{
  height: 46px !important;
}

.ant-menu-inline{
  margin-top: 18px !important;
  padding-right: 10px !important;
  padding-left: 2px !important;

}

.sub-menu-sider .ant-menu-item{
  background-color:#e1e1e1 !important ;
  border-radius: 0 !important;
  padding: 19px !important;

}
.sub-menu-sider .ant-menu-item:hover{
 
  color: blue !important;

}

.ant-modal-close:hover{
background-color: none !important;
}
.fc-timegrid-col-frame{
  overflow: auto !important;
}





.fc-timegrid-container {
  overflow-x: scroll;
}



.fc-direction-ltr .fc-timegrid-more-link{
  height: 30px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  background-color: #f64e60;
  color:white;

  
}

.ant-menu-item .anticon{
  display: flex !important;
  justify-content: center;
}
.ant-menu-title-content{
  font-size: 12px !important;
}

.fc-direction-ltr .fc-timegrid-more-link::after {
  content: ''; /* İçerik olarak boş bir alan oluştur */
  position: absolute; /* Pozisyonu ayarlamak için */
  top: 50%; /* Dikeyde ortalamak için */
  left: 50%; /* Yatayda ortalamak için */
  width: 60px; /* Daire genişliği */
  height: 60px; /* Daire yüksekliği */
  border-radius: 50%; /* Daire şeklini oluştur */
  background-color: rgba(246, 78, 96, 0.4); /* Daire rengi */
  transform: translate(-50%, -50%) scale(0.6); /* Ortalamak için dönüşüm */
  animation: pulse 3s infinite; /* Animasyonu başlat */
  z-index: 0; /* Diğer içeriklerin altında görünmesi için */
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.6); /* Başlangıç durumu */
    opacity: 1; /* Opaklık başlangıcı */
  }
  70% {
    transform: translate(-50%, -50%) scale(1.5); /* Genişleme durumu */
    opacity: 0; /* Opaklık azaltma durumu */
  }
  100% {
    transform: translate(-50%, -50%) scale(2); /* Son durumu */
    opacity: 0; /* Tamamen görünmez duruma gelme */
  }
}

.fc-popover-body{
  display: grid;
  justify-content: center;
}

.fc-popover-body > div{
 margin-bottom: 5px;
}
.fc-popover-header{
  display: flex !important;
  justify-content: space-between !important;
  align-items: center;
  background-color: #e1f0ff !important;
  color: #3599ff;
}
#mazaka-drawer .ant-drawer-body{
  background-color: #f5f5f5 !important;
}

.error-row {
  background-color: rgba(255, 0, 0, 0.1); /* Light red background */
}
 #search-place-container .ant-select{
  width: 100% !important;
}

.ant-input-disabled{
  color: black !important;
}

.ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-loading,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-loading,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-loading,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-loading,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-loading,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-loading,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-loading,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-loading,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-loading,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-loading,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-loading,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-loading,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-loading,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-loading,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-loading,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-loading,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-loading,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-loading,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-loading,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-loading,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-loading,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-loading,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-loading,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-loading,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-loading,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-loading,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-loading,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-loading,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-loading,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-loading,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-loading,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-loading,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-loading,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-loading,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-loading,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-loading,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-success,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-success,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-success,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-success,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-success,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-success,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-success,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-success,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-success,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-success,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-success,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-success,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-success,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-success,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-success,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-success,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-success,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-success,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-success,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-success,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-success,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-success,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-success,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-success,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-success,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-success,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-success,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-success,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-success,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-success,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-success,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-success,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-success,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-success,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-success,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-success {
    background-color: #2bc48a !important;
    border-color: #2bc48a !important;
    pointer-events: none !important;
    color: #fafafa !important;
    cursor: default;
  }
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-loading :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-loading :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-loading :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-loading :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-loading :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-loading :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-loading :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-loading :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-loading :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-loading :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-loading :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-loading :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-loading :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-loading :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-loading :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-loading :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-loading :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-loading :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-loading :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-loading :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-loading :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-loading :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-loading :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-loading :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-loading :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-loading :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-loading :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-loading :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-loading :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-loading :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-loading :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-loading :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-loading :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-loading :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-loading :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-loading :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-success :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-success :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-success :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-success :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-success :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-success :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-success :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-success :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-success :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-success :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-success :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-success :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-success :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-success :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-success :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-success :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-success :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-success :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-success :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-success :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-success :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-success :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-success :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-success :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-success :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-success :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-success :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-success :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-success :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-success :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-success :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-success :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-success :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-success :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-success :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-success :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-loading :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-loading :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-loading :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-loading :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-loading :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-loading :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-loading :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-loading :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-loading :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-loading :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-loading :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-loading :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-loading :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-loading :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-loading :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-loading :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-loading :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-loading :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-loading :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-loading :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-loading :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-loading :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-loading :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-loading :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-loading :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-loading :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-loading :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-loading :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-loading :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-loading :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-loading :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-loading :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-loading :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-loading :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-loading :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-loading :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-success :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-success :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-success :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-success :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-success :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-success :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-success :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-success :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-success :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-success :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-success :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-success :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-success :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-success :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-success :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-success :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-success :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-success :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-success :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-success :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-success :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-success :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-success :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-success :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-success :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-success :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-success :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-success :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-success :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-success :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-success :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-success :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-success :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-success :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-success :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-success :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-loading :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-loading :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-loading :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-loading :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-loading :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-loading :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-loading :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-loading :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-loading :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-loading :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-loading :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-loading :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-loading :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-loading :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-loading :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-loading :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-loading :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-loading :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-loading :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-loading :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-loading :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-loading :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-loading :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-loading :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-loading :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-loading :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-loading :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-loading :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-loading :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-loading :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-loading :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-loading :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-loading :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-loading :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-loading :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-loading :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-success :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-success :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-success :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-success :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-success :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-success :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-success :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-success :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-success :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-success :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-success :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-success :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-success :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-success :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-success :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-success :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-success :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-success :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-success :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-success :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-success :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-success :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-success :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-success :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-success :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-success :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-success :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-success :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-success :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-success :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-success :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-success :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-success :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-success :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-success :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-success :focus {
    background-color: #2bc48a !important;
    border-color: #2bc48a !important;
  }
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-loading.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-success.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-success.ant-btn span {
    padding-top: 0.07rem !important;
  }
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-loading.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-success.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-success.ant-btn svg {
    margin-top: 0.07rem;
  }
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-error,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-error,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-error,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-error,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-error,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-error,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-error,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-error,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-error,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-error,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-error,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-error,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-error,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-error,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-error,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-error,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-error,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-error,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-error,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-error,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-error,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-error,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-error,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-error,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-error,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-error,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-error,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-error,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-error,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-error,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-error,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-error,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-error,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-error,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-error,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-error {
    background-color: #db3643 !important;
    border-color: #db3643 !important;
    pointer-events: none !important;
    color: #fafafa !important;
  }
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-error :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-error :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-error :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-error :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-error :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-error :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-error :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-error :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-error :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-error :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-error :hover,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-error :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-error :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-error :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-error :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-error :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-error :hover,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-error :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-error :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-error :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-error :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-error :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-error :hover,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-error :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-error :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-error :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-error :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-error :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-error :hover,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-error :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-error :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-error :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-error :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-error :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-error :hover,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-error :hover,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-error :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-error :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-error :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-error :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-error :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-error :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-error :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-error :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-error :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-error :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-error :active,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-error :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-error :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-error :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-error :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-error :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-error :active,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-error :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-error :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-error :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-error :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-error :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-error :active,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-error :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-error :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-error :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-error :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-error :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-error :active,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-error :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-error :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-error :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-error :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-error :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-error :active,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-error :active,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-error :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-error :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-error :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-error :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-error :focus,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-error :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-error :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-error :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-error :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-error :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-error :focus,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-error :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-error :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-error :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-error :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-error :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-error :focus,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-error :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-error :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-error :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-error :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-error :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-error :focus,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-error :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-error :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-error :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-error :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-error :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-error :focus,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-error :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-error :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-error :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-error :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-error :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-error :focus,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-error :focus {
    background-color: #db3643 !important;
    border-color: #db3643 !important;
  }
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-error.ant-btn span,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-error.ant-btn span {
    padding-top: 0.07rem !important;
  }
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-primary.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-default.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-text.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-primary.ant-btn.ant-btn-link.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-primary.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-default.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-text.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-default.ant-btn.ant-btn-link.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-primary.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-default.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-text.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-ghost.ant-btn.ant-btn-link.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-primary.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-default.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-text.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-dashed.ant-btn.ant-btn-link.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-primary.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-default.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-text.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-text.ant-btn.ant-btn-link.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-primary.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-default.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-ghost.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-dashed.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-text.mzk-button-error.ant-btn svg,
  .ant-btn.ant-btn-link.ant-btn.ant-btn-link.mzk-button-error.ant-btn svg {
    margin-top: 0.07rem;
  }
