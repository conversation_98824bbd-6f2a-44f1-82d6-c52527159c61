import { message } from "antd";

export const copyToClipboard = (text:string, setIsSuccessCopied:React.Dispatch<React.SetStateAction<boolean>>) => {
  navigator.clipboard
    .writeText(text)
    .then(() => {
      setIsSuccessCopied(true);
      message.success("Kopyalandı");
    })
    .catch((error) => {
      message.error("Kopyalanamadı");
      console.error("Copy to clipboard failed:", error);
    });
};
