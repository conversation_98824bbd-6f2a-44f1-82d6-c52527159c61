import { openNotificationWithIcon } from "./Notifications";

export const showServiceErrorMessage = (
  error: any,
  mazakaForm?: any,
  appName?: string,
  isDelete: boolean = false
) => {
  if (error?.errors) {
    if (isDelete) {
      openNotificationWithIcon("error", "Bir hata oluştu");
    } else {
      mazakaForm.setFailed(2000, "Bir hata oluştu");
    }
  } else {
    if (isDelete) {
      openNotificationWithIcon(
        "error",
        error?.Message || error?.Value?.Message || "İşlem Başarısız"
      );
    } else {
      mazakaForm.setFailed(
        2000,
        error?.Message || error?.Value?.Message || "İşlem Başarısız"
      );
      openNotificationWithIcon(
        "error",
        error?.Message || error?.Value?.Message || "İşlem Başarısız"
      );
    }
  }

  console.log(
    "Something went wrong during the sending request:" + appName,
    error
  );
};
