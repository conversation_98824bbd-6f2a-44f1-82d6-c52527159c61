export const generateRandomPassword = () => {
    const lowerCase = "abcdefghijklmnopqrstuvwxyz";
    const upperCase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const numbers = "0123456789";
    const specialChars = "!@#$%^&*()-_=+{};:,<.>";
    const allChars = lowerCase + upperCase + numbers + specialChars;
  
    const getRandomChar = (chars: string) =>
      chars[Math.floor(Math.random() * chars.length)];
  
    let password = "";
    // Pattern'e uygun karakterler ekle
    password += getRandomChar(lowerCase);
    password += getRandomChar(upperCase);
    password += getRandomChar(numbers);
    password += getRandomChar(specialChars);
  
    // <PERSON><PERSON> kalan karakterleri rastgele doldur
    while (password.length < 6) {
      password += getRandomChar(allChars);
    }
  
    // Karakterleri karıştır
    return password
      .split("")
      .sort(() => 0.5 - Math.random())
      .join("");
  };
  