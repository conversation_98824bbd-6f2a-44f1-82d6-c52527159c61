export const getLatLng = async (country:string,stateProvince:string,district:string,city:string) => {
    
    try {
        const url = `https://nominatim.openstreetmap.org/search?format=json&country=${country}&state=${stateProvince}&city=${city}&district=${district}`;
      const response = await fetch(url);
      const data = await response.json();
      
      if (data && data.length > 0) {
        return data[0];
         
      } else {
        console.log("No results found");
        return 0
      }
    } catch (error) {
      console.error("Error fetching geocoding data:", error);
    }
  };

export const getAdressObj = async(lat:string |number,lon:string|number)=>{
    try {
        const url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`;
      const response = await fetch(url);
      const data = await response.json();
      
      if (data && data?.address) {
       
        return data["address"];
         
      } else {
        console.log("No results found");
        return 0
      }
    } 
    catch (error) {
      console.error("Error fetching geocoding data:", error);
    }
  }




  