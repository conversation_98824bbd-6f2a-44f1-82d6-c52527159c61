export const filteredData = (allData: any[], properties: Record<string, any>) => {
  let filteredResults = [...allData]; // Başlangıçta tüm veriyi kopyalayalım

  Object.entries(properties).forEach(([key, value]) => {
    filteredResults = filteredResults.filter((item) => {
      const itemValue = item[key];
      if (typeof itemValue === "string" || typeof itemValue === "number") {
        return itemValue.toString().toLowerCase().includes(value.toString().toLowerCase());
      }
      return false;
    });
  });

  return filteredResults;
};
