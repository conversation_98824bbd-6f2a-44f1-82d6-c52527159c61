import { notification } from "antd";

type NotificationType = "success" | "info" | "warning" | "error";
let environment = process.env.REACT_APP_ENVIRONMENT || "development";

const getClassName = (type?: NotificationType) => {
  switch (type) {
    case "success":
      return "!bg-success !text-white !z-[99999]";
    case "info":
      return "!bg-info !text-white !z-[99999]";
    case "warning":
      return "!bg-warning !z-[99999]";
    case "error":
      return "!bg-danger !text-white !z-[99999]";
    default:
      return "!bg-primary !text-white !z-[99999]";
  }
};

export const openNotificationWithIcon = (
  type: NotificationType,
  title: string,
  message: string | JSX.Element = "",
  internalMessage: string = ""
) => {

  if (
    (!!title && title.replaceAll(" ", "").length > 0) ||
    (typeof message === "string" && message.replaceAll(" ", "").length > 0)
  )
    notification.open({
      message: title,
      description: message,
      className: getClassName(type),
    });
};
