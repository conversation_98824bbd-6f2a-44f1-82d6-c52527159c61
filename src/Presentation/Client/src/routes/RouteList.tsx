import { accountRouteList } from "apps/Account/Routes";
import { buildingRouteList } from "apps/Building/Routes";
import { carRouteList } from "apps/Car/Routes";
import Init from "apps/Common/Init";
import { companyRouteList } from "apps/Company/Routes";
import { contractRouteList } from "apps/Contract/Routes";

import { departmentRouteList } from "apps/Deparment/Routes";
import { galleryRouteList } from "apps/Gallery/Routes";
import { notificationRouteList } from "apps/Notification/Routes";
import { transactionRouteList } from "apps/Plan/Routes";
import { productRouteList } from "apps/Product/Routes";
import { reportRouteList } from "apps/Report/Routes";
import { stationRouteList } from "apps/Station/Routes";
import { userRouteList } from "apps/User/Routes";
import { driverRouteList } from "apps/Drivers/Routes";
import DashboardLayout from "Layout/Dashboard/DashboardLayout";
import { Navigate, Route, Routes } from "react-router-dom";
import React, { lazy, Suspense } from 'react'
import { Spin } from "antd";
import { commentRouteList } from "apps/Comment/Routes";
const DashboardIndex = lazy(() => import('apps/Dashboard/DashboardIndex'))
const NotFound= lazy(() => import('apps/Common/NotFound'))

export const RouteList = () => {
  return (
    <Routes>
      {accountRouteList}
      <Route element={<Init />}>
       
            <Route element={<DashboardLayout />}>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
             <Route path="/dashboard" element={
              <Suspense fallback={<Spin/>}>
                <DashboardIndex/>
              </Suspense>
             } />
             {companyRouteList}
             {buildingRouteList}
             {stationRouteList}
             {carRouteList}
             {productRouteList}
             {contractRouteList}
             {userRouteList}
             {driverRouteList}
             {departmentRouteList}
             {notificationRouteList}
             {reportRouteList}
             {transactionRouteList}
             {galleryRouteList}
             {commentRouteList}
          
          
            </Route>
      </Route>
      <Route path="*" element={<Suspense>
        <NotFound />
      </Suspense>} />
    </Routes>
  );
};
