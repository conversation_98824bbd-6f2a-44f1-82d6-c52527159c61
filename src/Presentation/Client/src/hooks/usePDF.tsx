const usePdf = () => {
    const pdfConverter = (data:any) => {
      let popupWindow = window.open("", "_blank", "");
      popupWindow?.document.open();
      let printContent = `
              <html >
                  
                  <body onload="window.print()">
                      ${data}
                  </body>
                  </html>
              `;
      popupWindow?.document.write(printContent);
      popupWindow?.document.close();
    };
    return { pdfConverter };
  };
  export default usePdf;
  