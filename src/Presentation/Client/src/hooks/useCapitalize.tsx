/**
 * This function works to do what uppercase first character of the words.
 * @returns string
 */
const useCapitalize = () => {
    function capitalize(str: string) {
      if (str) {
        const arr = str.split(" ");
  
        for (var i = 0; i < arr.length; i++) {
          arr[i] =
            arr[i].charAt(0).toLocaleUpperCase() +
            arr[i].toLocaleLowerCase().slice(1);
        }
  
        const str2 = arr.join(" ");
        return str2;
      }
      return "";
    }
    return { capitalize };
  };
  
  export default useCapitalize;
  