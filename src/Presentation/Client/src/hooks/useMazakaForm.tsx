import { FormInstance } from "antd";
import { MazakaButtonProcessType } from "models/Client/MazakaButtonProccessType"; 
import { MazakaFormProps } from "models/Client/MazakaFormProps";
import { useMemo, useState } from "react";
/**
 * How can you use?
 *
 * @example
 * const YourComponent = () => {
 *      const [form] = Form.useForm<FormItems>();
 *      const mazakaForm = useMazakaForm(form);
 *
 *      const anyFunction = () => {
 *          mazakaForm.clearAllFields()
 *      }
 * }
 *
 */
export default function useMazakaForm<Values = any>(
  form: FormInstance<Values>,
  initialFormActions?: MazakaFormProps
) {
  const [processType, setProcessType] = useState<MazakaButtonProcessType>(
    initialFormActions?.submitProcessType || "default"
  );
  const [submitText, setSubmitText] = useState<string | undefined>(
    initialFormActions?.submitText || undefined
  );
  const [submitDisable, setSubmitDisable] = useState(
    initialFormActions?.submitButtonDisable || false
  );
  /**
   * @description This function deletes your forms all fields values.
   */
  const mazakaForm = useMemo(
    function () {
      return {
        clearAllFields: (key: string | null) => {
          const formValues: any = { ...form.getFieldsValue() };

          const clearedValues: any = Object.fromEntries(
            Object.entries(formValues).map((item) => {
              if (key) {
                if (item[0] === key) {
                  const newItem = [key, null];
                  return newItem;
                } else {
                  const newItem = [item[0], item[1]];
                  return newItem;
                }
              } else {
                const newItem = [item[0], null];
                return newItem;
              }
            })
          );

          form.setFieldsValue(clearedValues);
        },
        setSubmitText: (text: string) => {
          setSubmitText(text);
        },
        setLoading: (text?: string) => {
          setProcessType("loading");
          if (!!text) setSubmitText(text);
        },
        /**
         * If your form gives error, you can use this.
         *
         * @param timeOut  is Millisecond
         * @param callbackFunc  works after time out
         */
        setFailed: (
          resetTimeOut?: number,
          failText?: string | undefined,
          resetCallbackFunc?: Function
        ) => {
          setProcessType("error");
          setSubmitText(failText);
          if (!!resetTimeOut) {
            setTimeout(() => {
              setProcessType("default");
              setSubmitText(initialFormActions?.submitText);
              if (!!resetCallbackFunc) resetCallbackFunc();
            }, resetTimeOut);
          }
        },
        /**
         * If your form submit processs is successfuly, you can use this func.
         *
         * @param timeOut  is Millisecond
         * @param callbackFunc  works after time out
         */
        setSuccess: (
          resetTimeOut: number = 2000,
          resetCallbackFunc?: Function,
          text?: string
        ) => {
          setProcessType("success");
          if (!!text) setSubmitText(text);
          setTimeout(() => {
            setProcessType("default");
            if (!!resetCallbackFunc) resetCallbackFunc();
          }, resetTimeOut);
        },
        setDefault: () => setProcessType("default"),
        /**
         * If this status be true, submit button will be disable
         * @param status boolean
         * @returns
         */
        setSubmitDisable: (status: boolean) => setSubmitDisable(status),
      };
    },
    [form]
  );
  const formActions: MazakaFormProps = {
    submitLoading: processType === "loading",
    submitProcessType: processType,
    submitText: submitText,
    submitButtonDisable: submitDisable,
  };

  return { mazakaForm, formActions };
}
