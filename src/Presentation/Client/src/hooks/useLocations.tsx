import { useQuery } from "react-query";
import { endpoints } from "services/BaseClient/Endpoints";
import { getCityList, getCountryList, getDistrictList, getStateProvinceList } from "services/Location";


export const useCountries = () => {
  
  return useQuery([endpoints.getStateProvinces], () => getCountryList(), {
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });
};

export const useStateProvince = (countryId?: string|undefined) => {
    let endPoint = countryId
      ? [endpoints.getStateProvinces, countryId]
      : [endpoints.getStateProvinces];
    return useQuery(endPoint, () => getStateProvinceList(countryId), {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    });
  };

  export const useDistricts = (filter:any) => {
    
    return useQuery([endpoints.getDistricts,filter], () => getDistrictList(filter), {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    });
  };
  
  export const useCities = (filter:any) => {
   
    return useQuery( [endpoints.getCities,filter], () => getCityList(filter), {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
    });
  };
  