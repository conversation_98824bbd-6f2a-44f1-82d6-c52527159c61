using MediatR;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Reports.Query;

public record GetTotalTimeVehicleReportQuery(
    Guid? VehicleId,
    Guid? BuildingId,
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate
) : IRequest<Response<List<TotalTimeVehicleReportDTO>>>;

public class GetTotalTimeVehicleReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetTotalTimeVehicleReportQuery, Response<List<TotalTimeVehicleReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<TotalTimeVehicleReportDTO>>> Handle(GetTotalTimeVehicleReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.ContractProductTransaction.AsQueryable();
        if (request.VehicleId.HasValue)
        {
            data = data.Where(x => x.VehicleId == request.VehicleId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.BuildingId == request.BuildingId);
        }
        if (request.ApprovedStartDate.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.Date);
        }
        var result = await data.GroupBy(x => new { x.VehicleId, x.TransactionRequest.BuildingId, x.TransactionRequest.ApprovedDateTime.Value.Date })
        .Select(x => new TotalTimeVehicleReportDTO
        {
            Vehicle = x.FirstOrDefault().Vehicle.Name,
            ApprovedDate = x.FirstOrDefault().TransactionRequest.ApprovedDateTime.Value.Date,
            Building = x.FirstOrDefault().TransactionRequest.Building.Name,
            TotalSendingAmount = x.Sum(y => (y.StartDate - y.EndDate).Value.Minutes),
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}