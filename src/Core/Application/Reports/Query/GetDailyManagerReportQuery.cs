using MediatR;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Domain.Contracts;
using Application.Shared.Data;

namespace Application.Reports.Query;

public record GetDailyManagerReportQuery(
    Guid? CompanyId,
    Guid? BuildingId,
    Guid? StationId,
    Guid? ProductId,
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate
) : IRequest<Response<List<DailyManagerReportDTO>>>;

public class GetDailyManagerReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetDailyManagerReportQuery, Response<List<DailyManagerReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<DailyManagerReportDTO>>> Handle(GetDailyManagerReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.TransactionRequest.AsQueryable();
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.Building.CompanyId == request.CompanyId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.BuildingId == request.BuildingId);
        }
        if (request.StationId.HasValue)
        {
            data = data.Where(x => x.StationId == request.StationId);
        }
        if (request.ProductId.HasValue)
        {
            data = data.Where(x => x.ProductId == request.ProductId);
        }
        if (request.ApprovedStartDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.Date);
        }
        var finisehStatus = (int)ContractProductTransactionStatusEnum.Finished;
        var result = await data.Select(x => new DailyManagerReportDTO
        {
            Station = x.Station.Name,
            Company = x.Building.Company.Name,
            Building = x.Building.Name,
            Product = x.Product.Name,
            ApprovedDate = x.ApprovedDateTime,
            TotalConcreteRequested = x.DesiredTotalConcrete,
            TotalConcreteSent = x.ContractProductTransaction.Where(x => x.StatusId == finisehStatus).Sum(x => x.SendingAmount),
            TotalConcreteRemaining = x.DesiredTotalConcrete - x.ContractProductTransaction.Where(x => x.StatusId == finisehStatus).Sum(x => x.SendingAmount),
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}