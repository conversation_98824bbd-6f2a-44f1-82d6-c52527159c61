using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Reports.Query;

public record GetPriceGuaranteeReportQuery(
    Guid? ProductId,
    Guid? ContractId,
    Guid? CompanyId,
    DateTime? StartDate,
    DateTime? EndDate
) : IRequest<Response<List<PriceGuaranteeReportDTO>>>;


public class GetPriceGuaranteeReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetPriceGuaranteeReportQuery, Response<List<PriceGuaranteeReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<PriceGuaranteeReportDTO>>> Handle(GetPriceGuaranteeReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.ContractProduct.AsQueryable();
        data = data.Where(x => x.Contract.IsApproved == true);
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.Contract.CompanyId == request.CompanyId);
        }
        if (request.ContractId.HasValue)
        {
            data = data.Where(x => x.ContractId == request.ContractId);
        }
        if (request.ProductId.HasValue)
        {
            data = data.Where(x => x.ProductId == request.ProductId);
        }
        if (request.StartDate.HasValue)
        {
            data = data.Where(x => x.PriceGuaranteeDate.Value.Date >= request.StartDate.Value.Date);
        }
        if (request.EndDate.HasValue)
        {
            data = data.Where(x => x.PriceGuaranteeDate.Value.Date <= request.EndDate.Value.Date);
        }
        var result = await data
            .Select(x => new PriceGuaranteeReportDTO
            {
                Company = x.Contract.Company.Name,
                ContractStartDate = x.Contract.StartDate,
                ContractEndDate = x.Contract.EndDate,
                PriceGuaranteeDate = x.PriceGuaranteeDate,
                ProductName = x.Product.Name,
                ProductAmount = x.Amount,
                ProductPrice = x.Price,
                Contract = x.Contract.Title,
                Buildings = x.ContractProductBuilding.Select(x => x.Building.Name).ToList(),
                LeftAmout = x.LeftAmount
            })
            .ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}