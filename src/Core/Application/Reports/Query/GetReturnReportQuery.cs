using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Domain.Contracts;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Reports.Query;

public record GetReturnReportQuery(
    Guid? VehicleId,
    Guid? BuildingId,
    Guid? CompanyId,
    Guid? StationId,
    Guid? ProductId,
    DateTime? StartDate,
    DateTime? EndDate
) : IRequest<Response<List<ReturnReportDTO>>>;


public class GetReturnReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetReturnReportQuery, Response<List<ReturnReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<ReturnReportDTO>>> Handle(GetReturnReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.ContractProductTransaction.AsQueryable();
        var typeId = (int)TransactionTypeEnum.Return;
        data = data.Where(x => x.TypeId == typeId);
        if (request.VehicleId.HasValue)
        {
            data = data.Where(x => x.VehicleId == request.VehicleId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.BuildingId == request.BuildingId);
        }
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.Building.CompanyId == request.CompanyId);
        }
        if (request.StationId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.StationId == request.StationId);
        }
        if (request.ProductId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ProductId == request.ProductId);
        }
        if (request.StartDate.HasValue)
        {
            data = data.Where(x => x.InsertDate.Date >= request.StartDate.Value.Date);
        }
        if (request.EndDate.HasValue)
        {
            data = data.Where(x => x.InsertDate.Date <= request.EndDate.Value.Date);
        }
        var result = await data
            .Select(x => new ReturnReportDTO
            {
                Vehicle = x.Vehicle.Name,
                Building = x.TransactionRequest.Building.Name,
                Company = x.TransactionRequest.Building.Company.Name,
                Station = x.TransactionRequest.Station.Name,
                ReturnDate = x.InsertDate,
                Product = x.TransactionRequest.Product.Name,
                SendingAmount = x.SendingAmount,
                ReturnAmount = x.RefundAmount
            })
            .ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}