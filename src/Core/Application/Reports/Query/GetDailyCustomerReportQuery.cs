using MediatR;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Domain.Contracts;
using Application.Shared;
using Application.Shared.Data;

namespace Application.Reports.Query;

public record GetDailyCustomerReportQuery(
    Guid? CompanyId,
    Guid? BuildingId,
    Guid? StationId,
    Guid? ProductId,
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate
) : IRequest<Response<List<DailyCustomerReportDTO>>>;

public class GetDailyCustomerReportQueryHandler(
    IWorkContext workContext,
    IApplicationDbContext dbContext)
    : IRequestHandler<GetDailyCustomerReportQuery, Response<List<DailyCustomerReportDTO>>>
{
    private readonly IWorkContext _workContext = workContext;
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<DailyCustomerReportDTO>>> Handle(GetDailyCustomerReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.TransactionRequest.AsQueryable();
        var user = await _workContext.GetUserAsync();
        if (user.CompanyId.HasValue)
        {
            data = data.Where(x => x.Building.CompanyId == user.CompanyId);
        }
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.Building.CompanyId == request.CompanyId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.BuildingId == request.BuildingId);
        }
        if (request.StationId.HasValue)
        {
            data = data.Where(x => x.StationId == request.StationId);
        }
        if (request.ProductId.HasValue)
        {
            data = data.Where(x => x.ProductId == request.ProductId);
        }
        if (request.ApprovedStartDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.Date);
        }
        var result = await data.Select(x => new DailyCustomerReportDTO
        {
            Station = x.Station.Name,
            Company = x.Building.Company.Name,
            Building = x.Building.Name,
            Product = x.Product.Name,
            ApprovedDate = x.ApprovedDateTime,
            TotalConcreteRequested = x.DesiredTotalConcrete,
            TotalConcreteSent = x.ContractProductTransaction.Sum(x => x.SendingAmount),
            TotalConcreteRemaining = x.DesiredTotalConcrete - x.ContractProductTransaction.Sum(x => x.SendingAmount),
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}