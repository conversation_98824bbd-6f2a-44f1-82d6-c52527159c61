using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;
using Domain.Transactions;

namespace Application.Reports.Query;

public record GetStationReportQuery(
    Guid? StationId,
    DateTime? StartDate,
    DateTime? EndDate
) : IRequest<Response<List<StationReportDTO>>>;


public class GetStationReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetStationReportQuery, Response<List<StationReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<StationReportDTO>>> Handle(GetStationReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.TransactionRequest.AsQueryable();
        var typeId = (int)TransactionRequestTypeEnum.Normal;
        data = data.Where(x => x.TransactionrequestTypeId == typeId);
        if (request.StationId.HasValue)
        {
            data = data.Where(x => x.StationId == request.StationId);
        }
        if (request.StartDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date >= request.StartDate.Value.Date);
        }
        if (request.EndDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date <= request.EndDate.Value.Date);
        }
        var result = await data
            .GroupBy(x => x.ApprovedDateTime.Value.Date)
            .Select(x => new StationReportDTO
            {
                Station = x.FirstOrDefault().Station.Name,
                Date = x.Key,
                TotalRequest = x.Count(),
                TotalConcreate = x.Sum(x => x.DesiredTotalConcrete)
            })
            .ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}