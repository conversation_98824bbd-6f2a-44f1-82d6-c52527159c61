using MediatR;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Application.Reports.Query;

public record GetDelayRequestReportQuery(
    Guid? CompanyId,
    Guid? BuildingId,
    Guid? ContractId,
    Guid? ProductId,
    Guid? StationId,
    DateTime? StartDate,
    DateTime? EndDate
) : IRequest<Response<List<DelayRequestReportDTO>>>;

public class GetDelayRequestReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetDelayRequestReportQuery, Response<List<DelayRequestReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<DelayRequestReportDTO>>> Handle(GetDelayRequestReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.TransactionRequest.AsQueryable();
        int[] statusList = [1, 2, 3, 4];
        data = data.Where(x => statusList.Contains(x.StatusId) && x.ApprovedDateTime.Value.Date <= DateTime.Now.Date.AddDays(-1));
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.Building.CompanyId == request.CompanyId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.BuildingId == request.BuildingId);
        }
        if (request.ContractId.HasValue)
        {
            data = data.Where(x => x.ContractId == request.ContractId);
        }
        if (request.ProductId.HasValue)
        {
            data = data.Where(x => x.ProductId == request.ProductId);
        }
        if (request.StationId.HasValue)
        {
            data = data.Where(x => x.StationId == request.StationId);
        }
        if (request.StartDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date >= request.StartDate.Value.Date);
        }
        if (request.EndDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date <= request.EndDate.Value.Date);
        }
        var result = await data.Select(x => new DelayRequestReportDTO
        {
            Company = x.Building.Company.Name,
            Building = x.Building.Name,
            Station = x.Station.Name,
            ApprovedDate = x.ApprovedDateTime,
            RequestedDate = x.InsertDate,
            DesiredTotalConcrete = x.DesiredTotalConcrete,
            CompletedTotalConcrete = x.TotalConcreteSent,
            DelayTime = (DateTime.Now - x.ApprovedDateTime).Value.Minutes,
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}