using MediatR;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Reports.Query;

public record GetRequestReportQuery(
    Guid? CompanyId,
    Guid? BuildingId,
    Guid? ContractId,
    Guid? ProductId,
    Guid? StationId,
    int? StatusId,
    DateTime? StartDate,
    DateTime? EndDate
) : IRequest<Response<List<RequestReportDTO>>>;

public class GetRequestReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetRequestReportQuery, Response<List<RequestReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<RequestReportDTO>>> Handle(GetRequestReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.TransactionRequest.AsQueryable();
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.Building.CompanyId == request.CompanyId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.BuildingId == request.BuildingId);
        }
        if (request.ContractId.HasValue)
        {
            data = data.Where(x => x.ContractId == request.ContractId);
        }
        if (request.ProductId.HasValue)
        {
            data = data.Where(x => x.ProductId == request.ProductId);
        }
        if (request.StationId.HasValue)
        {
            data = data.Where(x => x.StationId == request.StationId);
        }
        if (request.StatusId.HasValue)
        {
            data = data.Where(x => x.StatusId == request.StatusId);
        }
        if (request.StartDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date >= request.StartDate.Value.Date);
        }
        if (request.EndDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date <= request.EndDate.Value.Date);
        }
        var result = await data.Select(x => new RequestReportDTO
        {
            Building = x.Building.Name,
            Station = x.Station.Name,
            ApprovedDate = x.ApprovedDateTime,
            DesiredTotalConcrete = x.DesiredTotalConcrete,
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}