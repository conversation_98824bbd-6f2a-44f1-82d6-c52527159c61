using MediatR;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Reports.Query;

public record GetDailyVehicleReportQuery(
    Guid? CompanyId,
    Guid? VehicleId,
    Guid? BuildingId,
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate
) : IRequest<Response<List<DailyVehicleReportDTO>>>;

public class GetDailyVehicleReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetDailyVehicleReportQuery, Response<List<DailyVehicleReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<DailyVehicleReportDTO>>> Handle(GetDailyVehicleReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.ContractProductTransaction.AsQueryable();
        if (request.VehicleId.HasValue)
        {
            data = data.Where(x => x.VehicleId == request.VehicleId);
        }
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.Building.CompanyId == request.CompanyId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.BuildingId == request.BuildingId);
        }
        if (request.ApprovedStartDate.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.Date);
        }
        var result = await data.GroupBy(x => new { x.VehicleId, x.TransactionRequest.BuildingId, x.TransactionRequest.ApprovedDateTime.Value.Date }).Select(x => new DailyVehicleReportDTO
        {
            Company = x.FirstOrDefault().TransactionRequest.Building.Company.Name,
            Vehicle = x.FirstOrDefault().Vehicle.Name,
            ApprovedDate = x.FirstOrDefault().TransactionRequest.ApprovedDateTime.Value,
            Building = x.FirstOrDefault().TransactionRequest.Building.Name,
            TotalSendingAmount = x.Sum(y => y.SendingAmount),
            TotalTransaction = x.Count(),
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}