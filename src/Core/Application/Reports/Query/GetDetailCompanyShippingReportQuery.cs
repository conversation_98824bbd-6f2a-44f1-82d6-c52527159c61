using MediatR;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Shared.Data;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;

namespace Application.Reports.Query;

public record GetDetailCompanyShippingReportQuery(
    Guid? CompanyId,
    Guid? BuildingId,
    Guid? ProductId,
    Guid? ContractId,
    Guid? VehicleId,
    Guid? DriverId,
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate,
    DateTime? InsertStartDate,
    DateTime? InsertEndDate
) : IRequest<Response<List<DetailCompanyShippingReportDTO>>>;

public class GetDetailCompanyShippingReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetDetailCompanyShippingReportQuery, Response<List<DetailCompanyShippingReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<DetailCompanyShippingReportDTO>>> Handle(GetDetailCompanyShippingReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.ContractProductTransaction.AsQueryable();
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.Contract.CompanyId == request.CompanyId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.BuildingId == request.BuildingId);
        }
        if (request.ProductId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ProductId == request.ProductId);
        }
        if (request.ContractId.HasValue)
        {
            data = data.Where(x => x.ContractId == request.ContractId);
        }
        if (request.VehicleId.HasValue)
        {
            data = data.Where(x => x.VehicleId == request.VehicleId);
        }
        if (request.DriverId.HasValue)
        {
            data = data.Where(x => x.DriverId == request.DriverId);
        }
        if (request.ApprovedStartDate.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.Date);
        }
        if (request.InsertStartDate.HasValue)
        {
            data = data.Where(x => x.InsertDate.Date >= request.InsertStartDate.Value.Date);
        }
        if (request.InsertEndDate.HasValue)
        {
            data = data.Where(x => x.InsertDate.Date <= request.InsertEndDate.Value.Date);
        }
        var result = await data.Select(x => new DetailCompanyShippingReportDTO
        {
            CompanyName = x.TransactionRequest.Building.Company.Name,
            BuildingName = x.TransactionRequest.Building.Name,
            VehiclePlate = x.Vehicle.Plate,
            DriverName = x.Vehicle.Driver.Name,
            Product = x.TransactionRequest.Product.Name,
            SendingAmount = x.SendingAmount,
            InsertDate = x.InsertDate,
            DocumentNo = x.DocumentNo
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}