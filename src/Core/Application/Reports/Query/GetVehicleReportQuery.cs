using MediatR;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Reports.Query;

public record GetVehicleReportQuery(
    Guid? VehicleId,
    Guid? RequestId,
    Guid? StationId,
    Guid? CompanyId,
    Guid? BuildingId,
    int? StatusId,
    Guid? ContractId,
    Guid? DriverId,
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate
) : IRequest<Response<List<VehicleReportDTO>>>;

public class GetVehicleReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetVehicleReportQuery, Response<List<VehicleReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<VehicleReportDTO>>> Handle(GetVehicleReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.ContractProductTransaction.AsQueryable();
        if (request.VehicleId.HasValue)
        {
            data = data.Where(x => x.VehicleId == request.VehicleId);
        }
        if (request.RequestId.HasValue)
        {
            data = data.Where(x => x.TransactionRequestId == request.RequestId);
        }
        if (request.StationId.HasValue)
        {
            data = data.Where(x => x.StationId == request.StationId);
        }
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.Building.CompanyId == request.CompanyId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.BuildingId == request.BuildingId);
        }
        if (request.StatusId.HasValue)
        {
            data = data.Where(x => x.StatusId == request.StatusId);
        }
        if (request.ContractId.HasValue)
        {
            data = data.Where(x => x.ContractId == request.ContractId);
        }
        if (request.DriverId.HasValue)
        {
            data = data.Where(x => x.Vehicle.DriverId == request.DriverId);
        }
        if (request.ApprovedStartDate.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.Date);
        }
        var result = await data.Select(x => new VehicleReportDTO
        {
            Vehicle = x.Vehicle.Name,
            Plate = x.Vehicle.Plate,
            ApprovedDate = x.TransactionRequest.ApprovedDateTime,
            Station = x.TransactionRequest.Station.Name,
            Building = x.TransactionRequest.Building.Name,
            Company = x.TransactionRequest.Building.Company.Name,
            Contract = x.TransactionRequest.Contract.Title,
            Driver = x.Vehicle.Driver.Name,
            Status = x.Status.Name,
            SendingAmount = x.SendingAmount,
            DocumentNo = x.DocumentNo,
            Product = x.TransactionRequest.Product.Name
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}