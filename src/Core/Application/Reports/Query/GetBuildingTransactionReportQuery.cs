using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Reports.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;
using Domain.Contracts;

namespace Application.Reports.Query;

public record GetBuildingTransactionReportQuery(
    Guid? VehicleId,
    Guid? BuildingId,
    Guid? CompanyId,
    Guid? StationId
) : IRequest<Response<List<BuildingTransactionReportDTO>>>;


public class GetBuildingTransactionReportQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetBuildingTransactionReportQuery, Response<List<BuildingTransactionReportDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<BuildingTransactionReportDTO>>> Handle(GetBuildingTransactionReportQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.ContractProductTransaction.AsQueryable();
        var typeId = (int)TransactionTypeEnum.Normal;
        data = data.Where(x => x.TypeId == typeId);
        if (request.VehicleId.HasValue)
        {
            data = data.Where(x => x.VehicleId == request.VehicleId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.BuildingId == request.BuildingId);
        }
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.Building.CompanyId == request.CompanyId);
        }
        if (request.StationId.HasValue)
        {
            data = data.Where(x => x.TransactionRequest.StationId == request.StationId);
        }
        var result = await data
            .Select(x => new BuildingTransactionReportDTO
            {
                DocumentNo = x.DocumentNo,
                Product = x.TransactionRequest.Product.Name,
                Building = x.TransactionRequest.Building.Name,
                DateTime = x.InsertDate,
                Amount = x.SendingAmount
            })
            .ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}