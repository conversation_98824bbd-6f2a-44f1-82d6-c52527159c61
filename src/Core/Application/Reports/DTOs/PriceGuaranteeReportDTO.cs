using Domain.Shared;

namespace Application.Reports.DTOs;

public class PriceGuaranteeReportDTO : BaseDTO
{
    public string? Contract { get; internal set; }
    public string? Company { get; internal set; }
    public DateTime? PriceGuaranteeDate { get; internal set; }
    public decimal? LeftAmout { get; internal set; }
    public decimal? ProductAmount { get; internal set; }
    public decimal? ProductPrice { get; internal set; }
    public string? ProductName { get; internal set; }
    public DateTime? ContractEndDate { get; internal set; }
    public DateTime? ContractStartDate { get; internal set; }
    public List<string>? Buildings { get; internal set; }
}