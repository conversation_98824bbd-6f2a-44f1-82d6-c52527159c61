using Domain.Shared;

namespace Application.Reports.DTOs;

public class DetailCompanyShippingReportDTO : BaseDTO
{
    public string? CompanyName { get; internal set; }
    public string? BuildingName { get; internal set; }
    public string? VehiclePlate { get; internal set; }
    public string? DriverName { get; internal set; }
    public string? Product { get; internal set; }
    public decimal? SendingAmount { get; internal set; }
    public DateTime? InsertDate { get; internal set; }
    public string? DocumentNo { get; internal set; }
}