using Domain.Shared;

namespace Application.Reports.DTOs;

public class DailyCustomerReportDTO : BaseDTO
{
    public string? Station { get; set; }
    public string? Company { get; set; }
    public string? Building { get; set; }
    public DateTime? ApprovedDate { get; set; }
    public decimal? TotalConcreteRequested { get; set; }
    public decimal? TotalConcreteRemaining { get; set; }
    public decimal? TotalConcreteSent { get; set; }
    public string? Product { get; internal set; }
}