using Domain.Shared;

namespace Application.Reports.DTOs;

public class VehicleReportDTO : BaseDTO
{
    public string? Vehicle { get; set; }
    public string? Plate { get; set; }
    public DateTime? ApprovedDate { get; set; }
    public string? Station { get; set; }
    public string? Building { get; set; }
    public string? Company { get; set; }
    public string? Contract { get; set; }
    public string? Driver { get; set; }
    public string? Status { get; set; }
    public decimal SendingAmount { get; set; }
    public string? DocumentNo { get; set; }
    public string? Product { get; set; }
}