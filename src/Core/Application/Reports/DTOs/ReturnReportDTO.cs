using Domain.Shared;

namespace Application.Reports.DTOs;

public class ReturnReportDTO : BaseDTO
{
    public string? Vehicle { get; set; }
    public string? Building { get; set; }
    public string? Company { get; set; }
    public string? Station { get; set; }
    public string? Product { get; set; }
    public decimal? SendingAmount { get; set; }
    public decimal? ReturnAmount { get; set; }
    public DateTime? ReturnDate { get; set; }
}