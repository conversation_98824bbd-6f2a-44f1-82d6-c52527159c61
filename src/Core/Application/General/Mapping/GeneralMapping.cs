using Application.General.DTOs;
using AutoMapper;
using Domain.General;

namespace Application.General.Mapping;

public class GeneralMapping : Profile
{
    public GeneralMapping()
    {
        CreateMap<Notification, NotificationDTO>().ReverseMap();
        CreateMap<StateProvince, StateProvinceDTO>().ReverseMap();
        CreateMap<District, DistrictDTO>().ReverseMap();
        CreateMap<City, CityDTO>().ReverseMap();
        CreateMap<NotAvailableDate, NotAvailableDateDTO>().ReverseMap();
        CreateMap<Gallery, GalleryDTO>().ReverseMap();
        CreateMap<Comment, CommentDTO>().ReverseMap();
        CreateMap<CommentFile, CommentFileDTO>().ReverseMap();
    }
}