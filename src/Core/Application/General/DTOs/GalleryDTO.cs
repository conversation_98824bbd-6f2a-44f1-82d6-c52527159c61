using Domain.Shared;
using Microsoft.AspNetCore.Http;

namespace Application.General.DTOs;

public class GalleryDTO : BaseDTO
{
    public Guid Id { get; set; }
    public required string Title { get; set; }
    public required string Description { get; set; }
    public string? Type { get; set; }
    public string? Data { get; set; }
    public IFormFile? File { get; set; }
    public string? FileName { get; set; }
}