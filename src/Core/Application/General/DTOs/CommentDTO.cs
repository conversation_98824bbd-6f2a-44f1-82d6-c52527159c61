using Application.Account.DTOs;
using Application.Companies.DTOs;
using Application.Contracts.DTOs;
using Application.Stations.DTOs;
using Domain.Shared;

namespace Application.General.DTOs;

public class CommentDTO : BaseDTO
{
    public Guid Id { get; set; }
    public DateTime InsertDate { get; set; }
    public string? Text { get; set; }
    public decimal Rate { get; set; }
    public Guid CommenterId { get; set; }
    public UserDTO? Commenter { get; set; }
    public Guid ContractProductTransactionId { get; set; }
    public ContractProductTransactionDTO? ContractProductTransaction { get; set; }
    public Guid VehicleId { get; set; }
    public VehicleDTO? Vehicle { get; set; }
    public Guid DriverId { get; set; }
    public UserDTO? Driver { get; set; }
    public virtual List<CommentFileDTO>? CommentFile { get; set; }
}