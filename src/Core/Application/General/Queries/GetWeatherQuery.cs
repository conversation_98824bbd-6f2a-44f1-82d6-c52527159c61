using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Domain.Shared;
using Application.Shared.Extensions;
using Domain.External;
using Microsoft.Extensions.Caching.Memory;
using Domain.External.DTOs;
using Application.Shared.Data;

namespace Application.General.Queries;

public record GetWeatherQuery(
    Guid BuildingId,
    DateTime? StartDate,
    DateTime? EndDate
) : IRequest<Response<List<WeatherDTO>>>;

public class GetWeatherQueryHandler(
    IApplicationDbContext dbContext,
    IWeatherManager weatherService,
    IMemoryCache memoryCache)
    : IRequestHandler<GetWeatherQuery, Response<List<WeatherDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;
    private readonly IWeatherManager _weatherService = weatherService;
    private readonly IMemoryCache _memoryCache = memoryCache;

    public async Task<Response<List<WeatherDTO>?>> Handle(GetWeatherQuery request, CancellationToken cancellationToken)
    {
        var building = await _dbContext.Building.FindAsync([request.BuildingId, cancellationToken], cancellationToken: cancellationToken)
            ?? throw new AppException("Şantiye bulunamadı");
        if (building.Longitude == 0 || building.Latitude == 0)
        {
            throw new AppException("Konum bilgisi bulunamadı");
        }
        var startDate = (request.StartDate ?? DateTime.Now).ToString("yyyy-MM-dd");
        var endDate = (request.EndDate ?? DateTime.Now.AddDays(30)).ToString("yyyy-MM-dd");
        var prekey = "weather_";
        var city = building.Longitude.ToString("N").Replace(",", ".") + "," + building.Latitude.ToString("N").Replace(",", ".");
        var date = "_" + startDate + "_" + endDate;
        var key = prekey + city + date;
        var result = _memoryCache.TryGetValue<List<WeatherDTO>>(key, out var weathers);
        if (result == false)
        {
            weathers = await _weatherService.GetWeather(city, startDate, endDate);
            if (weathers?.Count > 0)
            {
                _memoryCache.Set(key, weathers, TimeSpan.FromDays(1));
            }
        }
        return weathers.ToResponse();
    }
}