using MediatR;
using Application.Shared.Wrappers;
using Domain.External;
using Domain.External.DTOs;
using Domain.Shared;
using Application.Shared.Extensions;

namespace Application.General.Queries;

public record GetLiveTrackingQuery(
    string VehiclePlate
) : IRequest<Response<LiveTrackingLatestLocationDTO>>;

public class GetLiveTrackingQueryHandler(
    ILiveTrackingManager liveTrackingService)
    : IRequestHandler<GetLiveTrackingQuery, Response<LiveTrackingLatestLocationDTO>>
{
    private readonly ILiveTrackingManager _liveTrackingService = liveTrackingService;

    public async Task<Response<LiveTrackingLatestLocationDTO>?> Handle(GetLiveTrackingQuery request, CancellationToken cancellationToken)
    {
        var vehicle = await _liveTrackingService.GetVehicleByPlateAsync(request.VehiclePlate);
        if (vehicle == null)
        {
            throw new AppException("Plakaya ait araç bulunamadı");
        }
        var vehicleId = vehicle._id;
        var token = vehicle.Token;
        var latestLocation = await _liveTrackingService.GetLatestLocationAsync(token,vehicleId);
        if (latestLocation == null)
        {
            throw new AppException("Araç konumu bulunamadı");
        }
        return latestLocation.ToResponse();
    }
}