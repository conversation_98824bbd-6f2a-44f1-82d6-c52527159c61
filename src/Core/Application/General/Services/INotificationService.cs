using Application.Shared.Services;
using Application.Shared.Wrappers;
using Application.General.DTOs;
using Application.General.FilterModel;

namespace Application.General.Services;

public interface INotificationService : IBaseService<NotificationDTO, Guid>
{
    Task<Response<BulkSendNotificationResultDTO>> BulkSendAsync(BulkSendNotificationDTO postData);
    Task<PagedResponse<NotificationDTO>> FilterAsync(NotificationFilterModel filter);
    Task SetAllReadAsync();
}