using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Domain.Shared;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.General;
using Application.General.DTOs;
using Application.General.FilterModel;
using Application.Shared;
using Domain.External;
using Domain.External.DTOs;
using Domain.Account;
using Application.Shared.Data;

namespace Application.General.Services;

public class NotificationService(
    IMapper mapper,
    IWorkContext workContext,
    INotificationManager notificationManager,
    IApplicationDbContext dbContext)
    : BaseService<Notification, NotificationDTO, Guid>(mapper, dbContext), INotificationService
{
    private readonly IWorkContext _workContext = workContext;
    private readonly INotificationManager _notificationManager = notificationManager;

    public async Task<PagedResponse<NotificationDTO>> FilterAsync(NotificationFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Title))
        {
            filteredData = filteredData.Where(x => x.Title.Contains(filter.Title));
        }
        if (filter.UserId.HasValue)
        {
            filteredData = filteredData.Where(x => x.UserId == filter.UserId);
        }
        if (filter.IsRead.HasValue)
        {
            filteredData = filteredData.Where(x => x.IsRead == filter.IsRead);
        }
        if (filter.StartInsertDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.InsertDate.Date >= filter.StartInsertDate.Value.Date);
        }
        if (filter.EndInsertDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.InsertDate.Date <= filter.EndInsertDate.Value.Date);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<NotificationDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public async Task SetAllReadAsync()
    {
        var user = await _workContext.GetUserAsync() ?? throw new AppException("Kullanıcı bulunamadı");
        await _dbSet
            .AsQueryable()
            .Where(x => x.UserId == user.Id && !x.IsRead)
            .ExecuteUpdateAsync(x => x.SetProperty(x => x.IsRead, true));
    }

    public override async Task<Response<NotificationDTO>> InsertAsync(NotificationDTO entityVM)
    {
        entityVM.IsSend = false;
        entityVM.IsRead = false;
        var user = await _dbContext.Users.FindAsync(entityVM.UserId);
        if (string.IsNullOrEmpty(user?.FCMDeviceId))
        {
            throw new AppException("Kullanıcıda uygulama yüklü değil veya bildirim izni yok.");
        }
        var result = await base.InsertAsync(entityVM);
        var notificationResult = await _notificationManager.SendNotificationAsync(new SendNotificationDTO
        {
            FCMDeviceId = user.FCMDeviceId,
            Subject = entityVM.Title,
            Message = entityVM.Note,
            Type = entityVM.Type,
            Data = entityVM.Data,
        });
        if (notificationResult)
        {
            result.Value.IsSend = true;
            return await UpdateAsync(result.Value);
        }
        return result;
    }

    public override async Task<Response<NotificationDTO>> UpdateAsync(NotificationDTO entityVM)
    {
        var entity = await _dbSet.FindAsync(entityVM.Id)
            ?? throw new Exception("Kayıt bulunamadı");
        entity.IsSend = entityVM.IsSend;
        entity.IsRead = entityVM.IsRead;
        var local = _dbSet.Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<NotificationDTO>(_mapper);
        return data.ToResponse();
    }

    public async Task<Response<BulkSendNotificationResultDTO>> BulkSendAsync(BulkSendNotificationDTO postData)
    {
        var allUsers = _dbContext.Users.AsQueryable().Where(x => !string.IsNullOrWhiteSpace(x.FCMDeviceId));
        var predicateBarcode = PredicateBuilder.False<User>();
        if (postData.UserIds?.Length > 0)
        {
            predicateBarcode = predicateBarcode.CustomOr(k => postData.UserIds.Contains(k.Id));
        }
        if (postData.RoleIds?.Length > 0)
        {
            foreach (var roleId in postData.RoleIds)
            {
                predicateBarcode = predicateBarcode.CustomOr(k => k.UserRole.Any(x => x.RoleId == roleId));
            }
        }
        if (postData.CompanyIds?.Length > 0)
        {
            predicateBarcode = predicateBarcode.CustomOr(k => postData.CompanyIds.Contains(k.CompanyId.Value));
        }
        if (postData.BuildingIds?.Length > 0)
        {
            foreach (var buildingId in postData.BuildingIds)
            {
                predicateBarcode = predicateBarcode.CustomOr(k => k.BuildingUser.Any(x => x.BuildingId == buildingId));
            }
        }
        allUsers = allUsers.Where(predicateBarcode);
        var allUsersList = await allUsers.ToListAsync();
        var result = new BulkSendNotificationResultDTO
        {
            TotalTry = allUsersList.Count,
            TotalSend = 0
        };
        foreach (var user in allUsersList)
        {
            var resultData = await InsertAsync(new NotificationDTO
            {
                UserId = user.Id,
                Title = postData.Title,
                Note = postData.Note,
                Type = postData.Type,
                Data = postData.Data
            });
            if (resultData.Value.IsSend)
            {
                result.TotalSend++;
            }
        }
        return result.ToResponse();
    }
}