using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.General;
using Application.General.DTOs;
using Application.General.FilterModel;
using Application.Shared.Data;

namespace Application.General.Services;

public class GalleryService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<Gallery, GalleryDTO, Guid>(mapper, dbContext), IGalleryService
{

    public async Task<PagedResponse<GalleryDTO>> FilterAsync(GalleryFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Title))
        {
            filteredData = filteredData.Where(x => x.Title.Contains(filter.Title));
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<GalleryDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public override async Task<Response<GalleryDTO>> InsertAsync(GalleryDTO entityVM)
    {
        if (entityVM.File != null)
        {
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "Gallery");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }
            var extension = Path.GetExtension(entityVM.File.FileName);
            var fileName = Guid.NewGuid() + extension;
            var filePath = Path.Combine(uploadsPath, fileName);
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await entityVM.File.CopyToAsync(stream);
            }
            entityVM.FileName = fileName;
        }
        return await base.InsertAsync(entityVM);
    }

    public override async Task<Response<GalleryDTO>> UpdateAsync(GalleryDTO entityVM)
    {
        //return base.UpdateAsync(entityVM);
        var entity = await _dbSet.FindAsync(entityVM.Id)
            ?? throw new Exception("Kayıt bulunamadı");
        if (entityVM.File != null)
        {
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "Gallery");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }
            if (!string.IsNullOrWhiteSpace(entity.FileName) && File.Exists(Path.Combine(uploadsPath, entity.FileName)))
            {
                File.Delete(Path.Combine(uploadsPath, entity.FileName));
            }
            var extension = Path.GetExtension(entityVM.File.FileName);
            var fileName = Guid.NewGuid() + extension;
            var filePath = Path.Combine(uploadsPath, fileName);
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                await entityVM.File.CopyToAsync(stream);
            }
            entity.FileName = fileName;
        }
        entity.Title = entityVM.Title;
        entity.Description = entityVM.Description;
        entity.Type = entityVM.Type;
        entity.Data = entityVM.Data;
        var local = _dbSet.Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<GalleryDTO>(_mapper);
        return data.ToResponse();
    }
}