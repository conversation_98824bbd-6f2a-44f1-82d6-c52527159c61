using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Domain.Shared;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.General;
using Application.General.DTOs;
using Application.General.FilterModel;
using Application.Shared.Data;

namespace Application.General.Services;

public class StateProvinceService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<StateProvince, StateProvinceDTO, Guid>(mapper, dbContext), IStateProvinceService
{
    public async Task<PagedResponse<StateProvinceDTO>> FilterAsync(StateProvinceFilterModel filter)
    {
        var allData = _dbContext.Set<StateProvince>().AsQueryable();
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<StateProvinceDTO>>(list);
        return data.ToPagedResponse(filter);
    }
}