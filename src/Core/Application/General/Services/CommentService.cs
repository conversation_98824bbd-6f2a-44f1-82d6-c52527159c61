using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Application.Shared.Data;
using Domain.Shared;
using Application.General.Services;
using Application.General.DTOs;
using Application.Shared;
using Application.General.FilterModel;
using Domain.General;

namespace Application.Comments.Services;

public class CommentService(
    IMapper mapper,
    AppSettings appSettings,
    IWorkContext workContext,
    IApplicationDbContext dbContext)
    : BaseService<Comment, CommentDTO, Guid>(mapper, dbContext), ICommentService
{
    private readonly AppSettings _appSettings = appSettings;
    private readonly IWorkContext _workContext = workContext;

    public async Task<PagedResponse<CommentDTO>> FilterAsync(CommentFilterModel filter)
    {
        var allData = _dbSet
                .Include(x => x.Driver)
                .Include(x => x.CommentFile)
                .AsQueryable();
        var filteredData = allData;

        if (filter.VehicleId.HasValue)
        {
            filteredData = filteredData.Where(x => x.VehicleId == filter.VehicleId.Value);
        }

        if (filter.DriverId.HasValue)
        {
            filteredData = filteredData.Where(x => x.DriverId == filter.DriverId.Value);
        }

        if (filter.CompanyId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ContractProductTransaction.TransactionRequest.Building.CompanyId == filter.CompanyId.Value);
        }

        if (filter.BuildingId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ContractProductTransaction.TransactionRequest.BuildingId == filter.BuildingId.Value);
        }

        if (filter.HasFile.HasValue)
        {
            if (filter.HasFile.Value == true)
                filteredData = filteredData.Where(x => x.CommentFile.Any());
            else
                filteredData = filteredData.Where(x => !x.CommentFile.Any());
        }

        if (filter.ContractProductTransactionId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ContractProductTransactionId == filter.ContractProductTransactionId.Value);
        }


        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<CommentDTO>>(list);
        return data.ToPagedResponse(filter);
    }


    public override async Task<Response<CommentDTO>> InsertAsync(CommentDTO entityVM)
    {
        var user = await _workContext.GetUserAsync();
        entityVM.CommenterId = user.Id;
        entityVM.InsertDate = DateTime.Now;
        return await base.InsertAsync(entityVM);
    }

}