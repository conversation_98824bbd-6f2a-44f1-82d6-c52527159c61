using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Contracts;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;
using Application.Shared.Data;
using Application.General.DTOs;
using Application.General.FilterModel;
using Domain.General;

namespace Application.General.Services;

public class CommentFileService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<CommentFile, CommentFileDTO, Guid>(mapper, dbContext), ICommentFileService
{
    public async Task<PagedResponse<CommentFileDTO>> FilterAsync(CommentFileFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (filter.CommentId.HasValue)
        {
            filteredData = filteredData.Where(x => x.CommentId == filter.CommentId.Value);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<CommentFileDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public override async Task<Response<CommentFileDTO>> InsertAsync(CommentFileDTO entityVM)
    {
        if (entityVM.File != null)
        {
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "CommentFile");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }
            var extension = Path.GetExtension(entityVM.File.FileName);
            var CommentFileId = Guid.NewGuid();
            var fileName = entityVM.File.FileName + "_" + CommentFileId + extension;
            var filePath = Path.Combine(uploadsPath, fileName);
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                entityVM.File.CopyTo(stream);
            }
            entityVM.FileName = fileName;
        }
        return await base.InsertAsync(entityVM);
    }

    public override async Task<PagedResponse<CommentFileDTO>> InsertListAysnc(List<CommentFileDTO> entityListVM)
    {
        if (entityListVM?.Any(x => x.File != null) == true)
        {
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "CommentFile");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }
            foreach (var file in entityListVM.Where(x => x.File != null))
            {
                var extension = Path.GetExtension(file.File.FileName);
                var CommentFileId = Guid.NewGuid();
                var fileName = file.File.FileName + "_" + CommentFileId + extension;
                var filePath = Path.Combine(uploadsPath, fileName);
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    file.File.CopyTo(stream);
                }
                file.FileName = fileName;
            }
        }
        return await base.InsertListAysnc(entityListVM);
    }

    public override async Task DeleteAsync(Guid id)
    {
        var entity = await _dbSet
            .FindAsync(id)
            .ConfigureAwait(false);
        if (!string.IsNullOrWhiteSpace(entity.FileName) && File.Exists(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "CommentFile", entity.FileName)))
        {
            File.Delete(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "CommentFile", entity.FileName));
        }
        _dbSet.Remove(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public override async Task DeleteListAsync(List<Guid> ids)
    {
        var entityList = new List<CommentFile>();
        foreach (var id in ids)
        {
            var entity = await _dbSet
                .FindAsync(id)
                .ConfigureAwait(false);
            if (entity != null)
            {
                if (!string.IsNullOrWhiteSpace(entity.FileName) && File.Exists(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "CommentFile", entity.FileName)))
                {
                    File.Delete(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "CommentFile", entity.FileName));
                }
                entityList.Add(entity);
            }
        }
        _dbSet.RemoveRange(entityList);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }
}