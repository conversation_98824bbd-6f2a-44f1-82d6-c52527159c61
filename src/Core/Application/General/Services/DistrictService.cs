using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.General;
using Application.General.DTOs;
using Application.General.FilterModel;
using Application.Shared.Data;

namespace Application.General.Services;

public class DistrictService(
    IMapper mapper,
    IApplicationDbContext dbContext
) : BaseService<District, DistrictDTO, Guid>(mapper, dbContext), IDistrictService
{

    public async Task<PagedResponse<DistrictDTO>> FilterAsync(DistrictFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        }
        if (filter.StateProvinceId.HasValue)
        {
            filteredData = filteredData.Where(x => x.StateProvinceId == filter.StateProvinceId);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<DistrictDTO>>(list);
        return data.ToPagedResponse(filter);
    }
}