using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.General;
using Application.General.DTOs;
using Application.General.FilterModel;
using Application.Shared.Data;

namespace Application.General.Services;

public class NotAvailableDateService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<NotAvailableDate, NotAvailableDateDTO, Guid>(mapper, dbContext), INotAvailableDateService
{

    public async Task<PagedResponse<NotAvailableDateDTO>> FilterAsync(NotAvailableDateFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (filter.StartDate.HasValue)
        {
            filteredData = filteredData.Where(x => filter.StartDate.Value.Date <= x.Date.Date);
        }
        if (filter.EndDate.HasValue)
        {
            filteredData = filteredData.Where(x => filter.EndDate.Value.Date >= x.Date.Date);
        }
        if (filter.StationId.HasValue)
        {
            filteredData = filteredData.Where(x => x.StationId == filter.StationId);
        }
        if (filter.BuildingId.HasValue)
        {
            var stationId = (await _dbContext.Building.FindAsync(filter.BuildingId)).StationId;
            filteredData = filteredData.Where(x => x.StationId == stationId);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<NotAvailableDateDTO>>(list);
        return data.ToPagedResponse(filter);
    }
}