using Application.Shared.Behaviours;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using FluentValidation;
using Application.Catalog.Services;
using Application.Companies.Services;
using Application.General.Services;
using Application.Stations.Services;
using Application.Contracts.Services;
using Application.Transactions.Services;
using Application.Shared;
using Application.Account.Services;
using Application.Comments.Services;

namespace Application;

public static class ServiceRegistration
{
    public static void AddApplication(this IServiceCollection serviceCollection)
    {
        var assembly = Assembly.Load("Application");
        serviceCollection.AddAutoMapper(assembly);
        serviceCollection.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssembly(assembly);
            cfg.RegisterGenericHandlers = false;
        });
        serviceCollection.AddValidatorsFromAssembly(assembly);
        serviceCollection.AddTransient(typeof(IPipelineBehavior<,>), typeof(ValidationBehaviour<,>));

        serviceCollection.AddScoped<IWorkContext, WorkContext>();

        serviceCollection.AddScoped<IUserService, UserService>();

        serviceCollection.AddScoped<IProductService, ProductService>();

        serviceCollection.AddScoped<ICompanyService, CompanyService>();
        serviceCollection.AddScoped<IBuildingUserService, BuildingUserService>();
        serviceCollection.AddScoped<IBuildingService, BuildingService>();

        serviceCollection.AddScoped<IGalleryService, GalleryService>();
        serviceCollection.AddScoped<ICityService, CityService>();
        serviceCollection.AddScoped<IDistrictService, DistrictService>();
        serviceCollection.AddScoped<IStateProvinceService, StateProvinceService>();
        serviceCollection.AddScoped<INotificationService, NotificationService>();
        serviceCollection.AddScoped<INotAvailableDateService, NotAvailableDateService>();
        serviceCollection.AddScoped<ICommentService, CommentService>();
        serviceCollection.AddScoped<ICommentFileService, CommentFileService>();

        serviceCollection.AddScoped<IDepartmentService, DepartmentService>();
        serviceCollection.AddScoped<IStationService, StationService>();
        serviceCollection.AddScoped<IStationDepartmentService, StationDepartmentService>();
        serviceCollection.AddScoped<IVehicleService, VehicleService>();
        serviceCollection.AddScoped<IPompTypeService, PompTypeService>();
        serviceCollection.AddScoped<IVehicleTypeService, VehicleTypeService>();

        serviceCollection.AddScoped<IContractProductService, ContractProductService>();
        serviceCollection.AddScoped<IContractService, ContractService>();
        serviceCollection.AddScoped<IContractFileService, ContractFileService>();
        serviceCollection.AddScoped<IContractProductTransactionService, ContractProductTransactionService>();
        serviceCollection.AddScoped<IContractProductTransactionLogService, ContractProductTransactionLogService>();

        serviceCollection.AddScoped<ITransactionRequestService, TransactionRequestService>();
        serviceCollection.AddScoped<ILabResultService, LabResultService>();

    }
}