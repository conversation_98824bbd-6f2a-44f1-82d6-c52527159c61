using AutoMapper;
using Application.Account.DTOs;
using Domain.Account;

namespace Application.Account.Mapping;

public class AccountMapping : Profile
{
    public AccountMapping()
    {
        CreateMap<User, UserDTO>();
        CreateMap<UserDepartment, UserDepartmentDTO>().ReverseMap();
        CreateMap<RegisterDTO, User>();
        CreateMap<UserDTO, User>().ForMember(d => d.UserRole, o => o.MapFrom(s => s.RoleId.HasValue ? new List<UserRole> { new UserRole { UserId = s.Id, RoleId = s.RoleId.Value } } : null));
        CreateMap<Role, RoleDTO>().ReverseMap();
    }
}