using MediatR;
using Application.Shared.Wrappers;
using Application.Account.DTOs;
using Application.Shared.Extensions;
using Application.Shared;
using Domain.Shared;

namespace Application.Account;

public record GetUserInfoQuery() : IRequest<Response<UserDTO>>;

public class GetUserInfoQueryHandler(
    IWorkContext workContext)
    : IRequestHandler<GetUserInfoQuery, Response<UserDTO>>
{
    private readonly IWorkContext _workContext = workContext;

    public async Task<Response<UserDTO>> Handle(GetUserInfoQuery request, CancellationToken cancellationToken)
    {
        var user = await _workContext.GetUserAsync();
        if (user == null || user.Id == Guid.Empty)
        {
            throw new AppException("Kullanıcı bulunamadı");
        }
        return user.ToResponse();
    }
}