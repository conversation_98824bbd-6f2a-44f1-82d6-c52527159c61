using Application.Companies.DTOs;
using Application.Stations.DTOs;
using Domain.Shared;

namespace Application.Account.DTOs;

public class UserDTO : BaseDTO
{
    public Guid Id { get; set; }
    public string? Email { get; set; }
    public string? UserName { get; set; }
    public string? Name { get; set; }
    public string? Surname { get; set; }
    public bool Active { get; set; }
    public bool IsMaster { get; set; }
    public Guid? CompanyId { get; set; }
    public CompanyDTO? Company { get; set; }
    public string? CompanyName { get; set; }
    public string? TaxNumber { get; set; }
    public bool Kvkk { get; set; }
    public string? FCMDeviceId { get; set; }
    public DateTime? InsertDate { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Password { get; set; }
    public Guid? RoleId { get; set; }
    public RoleDTO? Role { get; set; }
    public List<UserDepartmentDTO>? UserDepartment { get; set; }
    //public List<TransactionRequestDTO>? TransactionRequest { get; set; }
    public List<BuildingUserDTO>? BuildingUser { get; set; }
    public List<VehicleDTO>? Vehicle { get; set; }
}