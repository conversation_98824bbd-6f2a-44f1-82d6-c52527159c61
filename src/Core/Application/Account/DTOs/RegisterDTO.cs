using Domain.Shared;

namespace Application.Account.DTOs;

public class RegisterDTO : BaseDTO
{
    public bool Active { get; set; }
    public string? Email { get; set; }
    public string? Password { get; set; }
    public string? Name { get; set; }
    public string? Surname { get; set; }
    public string? CompanyName { get; set; }
    public string? TaxNumber { get; set; }
    public bool Kvkk { get; set; }
    public Guid? CompanyId { get; set; }
    public bool IsMaster { get; set; }
    public Guid? RoleId { get; set; }
    public string? PhoneNumber { get; set; }
}