using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using MediatR;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using FluentValidation;
using Application.Account.DTOs;
using Domain.Account;
using Domain.Shared;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;

namespace Application.Account;

public class GetTokenQuery : IRequest<Response<TokenDTO>>
{
    public required string Email { get; set; }
    public required string Password { get; set; }
}

public class GetTokenQueryHandler : IRequestHandler<GetTokenQuery, Response<TokenDTO>>
{
    private readonly UserManager<User> _userManager;
    private readonly SignInManager<User> _signInManager;
    private readonly AppSettings _settings;

    public GetTokenQueryHandler(
        UserManager<User> userManager,
        SignInManager<User> signInManager,
        AppSettings settings)
    {
        _userManager = userManager;
        _signInManager = signInManager;
        _settings = settings;
    }

    public async Task<Response<TokenDTO>> Handle(GetTokenQuery request, CancellationToken cancellationToken)
    {
        var user = await _userManager.Users
        .FirstOrDefaultAsync(k => k.Email == request.Email, cancellationToken: cancellationToken)
        .ConfigureAwait(false);

        if (user is null)
        {
            throw new AppException("Kullanıcı bulunamadı");
        }
        // var userRole = await _userManager
        //     .GetRolesAsync(user)
        //     .ConfigureAwait(false);
        // if (!(userRole.Contains("ADMINISTRATOR") || userRole.Contains("EMPLOYEE")))
        // {
        //     throw new AppException( _localizer["Errors.Users.InvalidAuthority"]);
        // }
        //await ((DbSet<User>)_userManager.Users).GetContext().Entry(user).ReloadAsync().ConfigureAwait(false);
        if (!await _userManager.IsEmailConfirmedAsync(user).ConfigureAwait(false))
        {
            throw new AppException("Email Onaylanmadı");
        }
        if (user.Active == false)
        {
            throw new AppException("Kullanıcı aktif Değil");
        }
        var signinCheck = await _signInManager.CheckPasswordSignInAsync(user, request.Password, false).ConfigureAwait(false);
        if (signinCheck.Succeeded)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var securityToken = tokenHandler
                .CreateToken(
                    new SecurityTokenDescriptor
                    {
                        Subject = new ClaimsIdentity(new Claim[]
                        {
                            new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                            new(ClaimTypes.Name, user.Email)
                        }),
                        Expires = DateTime.UtcNow.AddDays(365),
                        SigningCredentials = new SigningCredentials(
                            new SymmetricSecurityKey(Encoding.ASCII.GetBytes(_settings.Id.ToString("N")[..32])),
                            SecurityAlgorithms.HmacSha256Signature
                        )
                    }
                );
            var token = tokenHandler.WriteToken(securityToken);
            return new TokenDTO
            {
                access_token = token,
                expires_in = "365",
                token_type = "Bearer",
                UserKey = user.Id
            }.ToResponse();
        }
        if (signinCheck.IsLockedOut)
        {
            throw new AppException("Kullanıcı bloklandı");
        }
        throw new AppException("Giris Yapılamadı");
    }
}

public class GetTokenQueryValidator : AbstractValidator<GetTokenQuery>
{
    public GetTokenQueryValidator()
    {
        RuleFor(x => x.Email).NotEmpty().EmailAddress();
        RuleFor(x => x.Password).NotEmpty().MaximumLength(100).MinimumLength(6);
    }
}