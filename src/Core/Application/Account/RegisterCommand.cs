using MediatR;
using FluentValidation;
using Domain.Account;
using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using Application.Account.DTOs;
using Domain.Shared;
using Domain.External;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;
using Application.Shared.Extensions;
using AutoMapper;
using Application.Shared.Wrappers;

namespace Application.Account;

public record RegisterCommand(RegisterDTO Registration) : IRequest<Response<User>>;

public class RegisterCommandHandler(
    IUserStore<User> userStore,
    UserManager<User> userManager,
    AppSettings settings,
    IMapper mapper,
    ISMSManager smsService,
    IApplicationDbContext dbContext)
    : IRequestHandler<RegisterCommand, Response<User>>
{
    private readonly IUserStore<User> _userStore = userStore;
    private readonly UserManager<User> _userManager = userManager;
    private readonly AppSettings _settings = settings;
    private readonly IMapper _mapper = mapper;
    private readonly ISMSManager _smsService = smsService;
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<User>> Handle(RegisterCommand request, CancellationToken cancellationToken)
    {
        if (!_userManager.SupportsUserEmail)
        {
            throw new NotSupportedException($"Register API requires a user store with email support.");
        }
        var emailStore = (IUserEmailStore<User>)_userStore;
        var email = request.Registration.Email ?? request.Registration.PhoneNumber + "@tokgoz.com";
        var _emailAddressAttribute = new EmailAddressAttribute();
        if (string.IsNullOrEmpty(email) || !_emailAddressAttribute.IsValid(email))
        {
            throw new AppException("Geçerli bir email adresi girmediniz.");
        }
        if (!string.IsNullOrWhiteSpace(request.Registration.PhoneNumber))
        {
            var phoneResult = await _userManager.Users.AnyAsync(x => x.PhoneNumber == request.Registration.PhoneNumber);
            if (phoneResult)
            {
                throw new AppException("Bu telefon numarası zaten kayıtlı");
            }
        }
        var user = new User
        {
            Active = false,
            Name = request.Registration.Name,
            Surname = request.Registration.Surname,
            CompanyId = request.Registration.CompanyId,
            CompanyName = request.Registration.CompanyName,
            PhoneNumber = request.Registration.PhoneNumber,
            EmailConfirmed = true,
            PhoneNumberConfirmed = false
        };
        await _userStore.SetUserNameAsync(user, email, CancellationToken.None);
        await emailStore.SetEmailAsync(user, email, CancellationToken.None);
        var password = request.Registration.Password ?? "Aa-" + new Random().Next(100000, 999999).ToString("D6");
        var result = await _userManager.CreateAsync(user, password);
        if (!result.Succeeded)
        {
            throw new AppException(string.Join(",", result.Errors.Select(k => k.Description)));
        }
        if (request.Registration.RoleId.HasValue)
        {
            _dbContext.UserRoles.Add(new UserRole { UserId = user.Id, RoleId = request.Registration.RoleId.Value });
        }
        if (!string.IsNullOrWhiteSpace(request.Registration.PhoneNumber))
        {
            var validationCode = new Random().Next(100000, 999999).ToString();
            var hasher = new PasswordHasher<User>();
            var hashedCode = hasher.HashPassword(user, validationCode);
            var userToken = await _dbContext.UserTokens.FirstOrDefaultAsync(x => x.LoginProvider == "REGISTERPHONE" && x.UserId == user.Id, cancellationToken);
            if (userToken != null)
            {
                _dbContext.UserTokens.Remove(userToken);
            }
            userToken = new UserToken
            {
                UserId = user.Id,
                Name = DateTime.Now.ToString(),
                LoginProvider = "REGISTERPHONE",
                Value = hashedCode
            };
            _dbContext.UserTokens.Add(userToken);
            await _dbContext.SaveChangesAsync(cancellationToken);
            var phoneNumber = Clean(user.PhoneNumber);
            var smsMessages = _settings.SmsPhoneConfirmMessagePrefix + validationCode + _settings.SmsPhoneConfirmMessageSuffix;
            await _smsService.SendSmsAsync(phoneNumber, smsMessages);
        }
        var userVM = _mapper.Map<UserDTO>(user);
        return user.ToResponse();
    }

    public static string? Clean(string? phone)
    {
        if (string.IsNullOrWhiteSpace(phone))
        {
            return phone;
        }
        phone = phone
            .Replace(" ", "")
            .TrimStart('+')
            .TrimStart('9')
            .TrimStart('0');
        return phone;
    }
}

public class RegisterCommandValidator : AbstractValidator<RegisterCommand>
{
    public RegisterCommandValidator()
    {
        RuleFor(x => x.Registration).Must(x => !string.IsNullOrWhiteSpace(x.Email) || !string.IsNullOrWhiteSpace(x.PhoneNumber))
            .WithMessage("Email veya Telefondan dan en az birisi dolu olmalı");
    }
}