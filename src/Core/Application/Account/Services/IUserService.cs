using Application.Shared.Wrappers;
using Application.Account.DTOs;
using Application.Account.FilterModel;
using System.Linq.Expressions;

namespace Application.Account.Services;

public interface IUserService
{
    Task<PagedResponse<UserDTO>> FilterAsync(UserFilterModel filter);
    Task<List<RoleDTO>> GetRoleListAsync();
    Task<Response<UserDTO>> FirstOrDefaultAsync(params string[] includeProperties);
    Task<Response<UserDTO>> FirstOrDefaultAsync(Expression<Func<UserDTO, bool>> query, params string[] includeProperties);
    Task<Response<UserDTO>> FindAsync(Guid id, params string[] includeProperties);
    Task<Response<UserDTO>> InsertAsync(RegisterDTO entityVM);
    Task<PagedResponse<UserDTO>> InsertListAysnc(List<RegisterDTO> entityListVM);
    Task<Response<UserDTO>> UpdateAsync(UserDTO entityVM);
    Task UpdateFcmDeviceIdAsync(Guid id, string fcmDeviceId);
    Task UpdateListAsync(List<UserDTO> entityListVM);
    Task ChangePasswordAsync(Guid UserId, string Password);
    Task DeleteAsync(Guid id);
    Task DeleteAsync(UserDTO entityVM);
    Task DeleteListAsync(List<Guid> id);
    Task DeleteListAsync(List<UserDTO> entityListVM);
}