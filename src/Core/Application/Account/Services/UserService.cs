using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Domain.Shared;
using Application.Shared.Extensions;
using Application.Shared.Wrappers;
using Domain.Account;
using Application.Account.DTOs;
using Application.Account.FilterModel;
using AutoMapper.Extensions.ExpressionMapping;
using System.Linq.Expressions;
using FluentValidation;
using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;
using Domain.Companies;
using Application.Shared.Data;
using Application.Shared;

namespace Application.Account.Services;

public class UserService(
    IMapper mapper,
    IWorkContext workContext,
    IUserStore<User> userStore,
    UserManager<User> userManager,
    IApplicationDbContext dbContext)
    : IUserService
{
    protected readonly IMapper _mapper = mapper;
    private readonly IWorkContext _workContext = workContext;
    private readonly IUserStore<User> _userStore = userStore;
    private readonly UserManager<User> _userManager = userManager;
    private readonly IApplicationDbContext _dbContext = dbContext;
    protected readonly DbSet<User> _dbSet = dbContext.Set<User>();
    protected string[] BaseIncludes { get; set; } = ["UserRole.Role"];

    public async Task<PagedResponse<UserDTO>> FilterAsync(UserFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Email))
        {
            filteredData = filteredData.Where(x => x.Email.Contains(filter.Email));
        }
        if (!string.IsNullOrWhiteSpace(filter.UserName))
        {
            filteredData = filteredData.Where(x => x.UserName.Contains(filter.UserName));
        }
        if (!string.IsNullOrWhiteSpace(filter.FullName))
        {
            filteredData = filteredData.Where(x => (x.Name + " " + x.Surname).Contains(filter.FullName));
        }
        if (!string.IsNullOrWhiteSpace(filter.PhoneNumber))
        {
            var phoneNumber = filter.PhoneNumber.Replace(" ", "");
            if (phoneNumber.StartsWith('0'))
            {
                phoneNumber = phoneNumber[1..];
            }
            if (!string.IsNullOrWhiteSpace(phoneNumber))
            {
                filteredData = filteredData.Where(x => x.PhoneNumber.Contains(phoneNumber));
            }
        }
        if (filter.CompanyId.HasValue)
        {
            var buildingIds = await _dbContext.Building.Where(x => x.CompanyId == filter.CompanyId).Select(x => x.Id).ToListAsync();
            filteredData = filteredData.Where(x => x.CompanyId == filter.CompanyId || x.BuildingUser.Any(y => buildingIds.Contains(y.BuildingId.Value)));
        }
        if (filter.RoleId.HasValue)
        {
            filteredData = filteredData.Where(x => x.UserRole.Any(y => y.RoleId == filter.RoleId));
        }
        if (filter.RoleIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => x.UserRole.Any(y => filter.RoleIds.Contains(y.RoleId)));
        }
        if (filter.Active.HasValue)
        {
            filteredData = filteredData.Where(x => x.Active == filter.Active);
        }
        if (filter.IsMaster.HasValue)
        {
            filteredData = filteredData.Where(x => x.IsMaster == filter.IsMaster);
        }
        if (filter.StartInsertDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.InsertDate.Value.Date >= filter.StartInsertDate.Value.Date);
        }
        if (filter.EndInsertDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.InsertDate.Value.Date <= filter.EndInsertDate.Value.Date);
        }
        if (filter.IsCompanyUser.HasValue)
        {
            // if (filter.IsCompanyUser.Value)
            // {
            //     filteredData = filteredData.Where(x => x.CompanyId.HasValue);
            // }
            // else
            // {
            //     filteredData = filteredData.Where(x => !x.CompanyId.HasValue);
            // }
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<UserDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public async Task<List<RoleDTO>> GetRoleListAsync()
    {
        return await _dbContext.Roles
            .AsQueryable()
            .Select(x => new RoleDTO
            {
                Id = x.Id,
                Name = x.Name
            }).ToListAsync()
            .ConfigureAwait(false);
    }

    public virtual async Task<Response<UserDTO?>> FirstOrDefaultAsync(params string[] includeProperties)
    {
        if (BaseIncludes?.Length > 0)
        {
            includeProperties = includeProperties.Concat(BaseIncludes).ToArray();
        }
        var data = await _dbSet.AsQueryable()
            .Including(includeProperties)
            .UseAsDataSource(_mapper)
            .For<UserDTO>()
            .FirstOrDefaultAsync();
        return data.ToResponse();
    }

    public virtual async Task<Response<UserDTO>> FirstOrDefaultAsync(Expression<Func<UserDTO, bool>> query, params string[] includeProperties)
    {
        if (BaseIncludes?.Length > 0)
        {
            includeProperties = includeProperties.Concat(BaseIncludes).ToArray();
        }
        var data = await _dbSet.AsQueryable()
            .Including(includeProperties)
            .UseAsDataSource(_mapper)
            .For<UserDTO>()
            .FirstOrDefaultAsync(query);
        return data.ToResponse();
    }

    public virtual async Task<Response<UserDTO>> FindAsync(Guid id, params string[] includeProperties)
    {
        if (BaseIncludes?.Length > 0)
        {
            includeProperties = includeProperties.Concat(BaseIncludes).ToArray();
        }
        var entity = await _dbSet
            .FindAsync(id)
            .ConfigureAwait(false);
        var data = entity.MapTo<UserDTO>(_mapper);
        return data.ToResponse();
    }

    public virtual async Task<Response<UserDTO>> InsertAsync(RegisterDTO entityVM)
    {
        if (!_userManager.SupportsUserEmail)
        {
            throw new NotSupportedException($"Register API requires a user store with email support.");
        }
        var emailStore = (IUserEmailStore<User>)_userStore;
        var email = entityVM.Email;
        if (string.IsNullOrWhiteSpace(email))
        {
            email = email + "_" + Guid.NewGuid().ToString("N") + "@tokgoz.com";
        }
        var _emailAddressAttribute = new EmailAddressAttribute();
        if (!_emailAddressAttribute.IsValid(email))
        {
            throw new AppException("Geçerli bir email adresi girmediniz.");
        }
        if (!string.IsNullOrWhiteSpace(entityVM.PhoneNumber))
        {
            var phoneResult = await _dbSet.AsQueryable().AnyAsync(x => x.PhoneNumber == entityVM.PhoneNumber);
            if (phoneResult)
            {
                throw new AppException("Bu telefon numarası zaten kayıtlı");
            }
        }
        var user = new User
        {
            Id = Guid.NewGuid(),
            Active = entityVM.Active,
            Name = entityVM.Name,
            Surname = entityVM.Surname,
            CompanyId = entityVM.CompanyId,
            CompanyName = entityVM.CompanyName,
            PhoneNumber = entityVM.PhoneNumber,
            IsMaster = entityVM.IsMaster,
            PhoneNumberConfirmed = true,
            EmailConfirmed = true
        };
        if (entityVM.RoleId.HasValue)
        {
            _dbContext.UserRoles.Add(new UserRole
            {
                UserId = user.Id,
                RoleId = entityVM.RoleId.Value
            });
        }
        await _userStore.SetUserNameAsync(user, email, CancellationToken.None);
        await emailStore.SetEmailAsync(user, email, CancellationToken.None);
        var result = await _userManager.CreateAsync(user, entityVM.Password ?? "Abc123-");
        if (!result.Succeeded)
        {
            throw new AppException(string.Join(",", result.Errors.Select(k => k.Description)));
        }
        var data = user.MapTo<UserDTO>(_mapper);
        return data.ToResponse();
    }

    public virtual async Task<PagedResponse<UserDTO>> InsertListAysnc(List<RegisterDTO> entityListVM)
    {
        if (!_userManager.SupportsUserEmail)
        {
            throw new NotSupportedException($"Register API requires a user store with email support.");
        }
        var emailStore = (IUserEmailStore<User>)_userStore;
        var userList = new List<UserDTO>();
        foreach (var entityVM in entityListVM)
        {
            var email = entityVM.Email;
            if (string.IsNullOrWhiteSpace(email))
            {
                email = email + "_" + Guid.NewGuid().ToString("N") + "@tokgoz.com";
            }
            var _emailAddressAttribute = new EmailAddressAttribute();
            if (!_emailAddressAttribute.IsValid(email))
            {
                throw new AppException("Geçerli bir email adresi girmediniz.");
            }
            if (!string.IsNullOrWhiteSpace(entityVM.PhoneNumber))
            {
                var phoneResult = await _dbSet.AsQueryable().AnyAsync(x => x.PhoneNumber == entityVM.PhoneNumber);
                if (phoneResult)
                {
                    throw new AppException("Bu telefon numarası zaten kayıtlı: " + entityVM.PhoneNumber);
                }
            }
            var user = new User
            {
                Active = entityVM.Active,
                Name = entityVM.Name,
                Surname = entityVM.Surname,
                CompanyId = entityVM.CompanyId,
                CompanyName = entityVM.CompanyName,
                PhoneNumber = entityVM.PhoneNumber,
                IsMaster = entityVM.IsMaster,
                PhoneNumberConfirmed = true,
                EmailConfirmed = true
            };
            if (entityVM.RoleId.HasValue)
            {
                _dbContext.UserRoles.Add(new UserRole
                {
                    UserId = user.Id,
                    RoleId = entityVM.RoleId.Value
                });
            }
            await _userStore.SetUserNameAsync(user, email, CancellationToken.None);
            await emailStore.SetEmailAsync(user, email, CancellationToken.None);
            var result = await _userManager.CreateAsync(user, entityVM.Password ?? "Abc123-");
            if (!result.Succeeded)
            {
                throw new AppException(string.Join(",", result.Errors.Select(k => k.Description)));
            }
            userList.Add(user.MapTo<UserDTO>(_mapper));
        }
        return userList.ToPagedResponse();
    }

    public virtual async Task<Response<UserDTO>> UpdateAsync(UserDTO entityVM)
    {
        var entity = await _dbSet
            .AsQueryable()
            .Including(["UserRole", "UserDepartment", "BuildingUser"])
            .FirstOrDefaultAsync(x => x.Id == entityVM.Id)
            ?? throw new Exception("Kullanıcı bulunamadı");
        entity.Active = entityVM.Active;
        entity.IsMaster = entityVM.IsMaster;
        entity.Name = entityVM.Name;
        entity.Surname = entityVM.Surname;
        entity.CompanyId = entityVM.CompanyId;
        entity.CompanyName = entityVM.CompanyName;
        entity.FCMDeviceId = entityVM.FCMDeviceId;
        if (entityVM.RoleId.HasValue && !(entity.UserRole?.Any(x => x.RoleId == entityVM.RoleId) == true))
        {
            entity.UserRole?.Clear();
            _dbContext.UserRoles.Add(new UserRole
            {
                UserId = entityVM.Id,
                RoleId = entityVM.RoleId.Value
            });
        }
        if (!string.IsNullOrWhiteSpace(entityVM.Email) && entity.Email != entityVM.Email)
        {
            var setEmailResult = await _userManager.SetEmailAsync(entity, entityVM.Email);
            if (setEmailResult.Succeeded == false)
            {
                throw new AppException("Email adresi kaydedilemedi");
            }
        }
        if (!string.IsNullOrWhiteSpace(entityVM.PhoneNumber) && entity.PhoneNumber != entityVM.PhoneNumber)
        {
            var phoneResult = await _dbSet.AsQueryable().AnyAsync(x => x.PhoneNumber == entityVM.PhoneNumber && x.Id != entityVM.Id);
            if (phoneResult)
            {
                throw new AppException("Bu telefon numarası zaten kayıtlı: " + entityVM.PhoneNumber);
            }
            var setPhoneResult = await _userManager.SetPhoneNumberAsync(entity, entityVM.PhoneNumber);
            if (setPhoneResult.Succeeded == false)
            {
                throw new AppException("Telefon kaydedilemedi");
            }
        }
        if (!string.IsNullOrWhiteSpace(entityVM.Password))
        {
            _ = await _userManager.RemovePasswordAsync(entity);
            var setPasswordResult = await _userManager.AddPasswordAsync(entity, entityVM.Password);
            if (setPasswordResult.Succeeded == false)
            {
                throw new AppException("Şifre kaydedilemedi");
            }
        }
        foreach (var item in entity.UserDepartment.Where(k => !entityVM.UserDepartment?.Any(l => l.DepartmentId == k.DepartmentId) == true).ToList())
        {
            entity.UserDepartment.Remove(item);
        }
        entityVM.UserDepartment ??= [];
        foreach (var item in entityVM.UserDepartment.Where(k => !entity.UserDepartment.Any(l => l.DepartmentId == k.DepartmentId)).ToList())
        {
            entity.UserDepartment.Add(item.MapTo<UserDepartment>(_mapper));
        }
        foreach (var item in entity.BuildingUser.Where(k => !entityVM.BuildingUser?.Any(l => l.BuildingId == k.BuildingId) == true).ToList())
        {
            entity.BuildingUser.Remove(item);
        }
        entityVM.BuildingUser ??= [];
        foreach (var item in entityVM.BuildingUser?.Where(k => !entity.BuildingUser.Any(l => l.BuildingId == k.BuildingId)).ToList())
        {
            entity.BuildingUser.Add(item.MapTo<BuildingUser>(_mapper));
        }
        var local = _dbSet.Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<UserDTO>(_mapper);
        return data.ToResponse();
    }

    public async Task UpdateFcmDeviceIdAsync(Guid id, string fcmDeviceId)
    {
        var entity = await _dbSet
            .AsQueryable()
            .FirstOrDefaultAsync(x => x.Id == id)
            ?? throw new Exception("Kullanıcı bulunamadı");
        entity.FCMDeviceId = fcmDeviceId;
        var local = _dbSet.Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public virtual async Task UpdateListAsync(List<UserDTO> entityListVM)
    {
        var idList = entityListVM.Select(x => x.Id).ToList();
        var entityList = await _dbSet
            .AsQueryable()
            .Including(["UserRole", "UserDepartment", "BuildingUser"])
            .Where(x => idList.Contains(x.Id))
            .ToListAsync();
        foreach (var entity in entityList)
        {
            var entityVM = entityListVM.First(x => x.Id == entity.Id);
            entity.Active = entityVM.Active;
            entity.IsMaster = entityVM.IsMaster;
            entity.Name = entityVM.Name;
            entity.Surname = entityVM.Surname;
            entity.CompanyId = entityVM.CompanyId;
            entity.CompanyName = entityVM.CompanyName;
            entity.FCMDeviceId = entityVM.FCMDeviceId;
            if (entityVM.RoleId.HasValue && !(entity.UserRole?.Any(x => x.RoleId == entityVM.RoleId) == true))
            {
                entity.UserRole.Clear();
                entity.UserRole.Add(new UserRole
                {
                    UserId = entityVM.Id,
                    RoleId = entityVM.RoleId.Value
                });
            }
            if (!string.IsNullOrWhiteSpace(entityVM.Email))
            {
                var setEmailResult = await _userManager.SetEmailAsync(entity, entityVM.Email);
                if (setEmailResult.Succeeded == false)
                {
                    throw new AppException("Email adresi kaydedilemedi");
                }
            }
            if (!string.IsNullOrWhiteSpace(entityVM.PhoneNumber))
            {
                var phoneResult = await _dbSet.AsQueryable().AnyAsync(x => x.PhoneNumber == entityVM.PhoneNumber && x.Id != entityVM.Id);
                if (phoneResult)
                {
                    throw new AppException("Bu telefon numarası zaten kayıtlı: " + entityVM.PhoneNumber);
                }
                var setPhoneResult = await _userManager.SetPhoneNumberAsync(entity, entityVM.PhoneNumber);
                if (setPhoneResult.Succeeded == false)
                {
                    throw new AppException("Telefon kaydedilemedi");
                }
            }
            if (!string.IsNullOrWhiteSpace(entityVM.Password))
            {
                _ = await _userManager.RemovePasswordAsync(entity);
                var setPasswordResult = await _userManager.AddPasswordAsync(entity, entityVM.Password);
                if (setPasswordResult.Succeeded == false)
                {
                    throw new AppException("Şifre kaydedilemedi");
                }
            }
            foreach (var item in entity.UserDepartment.Where(k => !entityVM.UserDepartment.Any(l => l.DepartmentId == k.DepartmentId)).ToList())
            {
                entity.UserDepartment.Remove(item);
            }
            foreach (var item in entityVM.UserDepartment.Where(k => !entity.UserDepartment.Any(l => l.DepartmentId == k.DepartmentId)).ToList())
            {
                entity.UserDepartment.Add(item.MapTo<UserDepartment>(_mapper));
            }
            foreach (var item in entity.BuildingUser.Where(k => !entityVM.BuildingUser.Any(l => l.BuildingId == k.BuildingId)).ToList())
            {
                entity.BuildingUser.Remove(item);
            }
            foreach (var item in entityVM.BuildingUser.Where(k => !entity.BuildingUser.Any(l => l.BuildingId == k.BuildingId)).ToList())
            {
                entity.BuildingUser.Add(item.MapTo<BuildingUser>(_mapper));
            }
            _dbSet.Update(entity);
        }
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public async Task ChangePasswordAsync(Guid UserId, string Password)
    {
        var user = await _dbSet.FindAsync(UserId).ConfigureAwait(false)
            ?? throw new Exception("Kullanıcı bulunamadı");
        await _userManager.RemovePasswordAsync(user).ConfigureAwait(false);
        await _userManager.AddPasswordAsync(user, Password).ConfigureAwait(false);
    }

    public virtual async Task DeleteAsync(Guid id)
    {
        var user = await _workContext.GetUserAsync()
            ?? throw new AppException("Giriş yapan kullanıcı bulunamadı");
        var entity = await _dbSet.AsQueryable().Including("BuildingUser", "UserRole").FirstOrDefaultAsync(x => x.Id == id).ConfigureAwait(false);
        if (user.RoleId == Role.SUPERADMIN || user.RoleId == Role.ADMIN || user.RoleId == Role.STATION || user.RoleId == Role.SALES)
        {
            entity.IsDeleted = true;
            entity.Active = false;
            entity.UserName = null;
            entity.NormalizedUserName = null;
            entity.Email = null;
            entity.NormalizedEmail = null;
            entity.PhoneNumber = null;
            entity.PhoneNumberConfirmed = false;
            entity.PasswordHash = null;
            entity.SecurityStamp = null;
            entity.ConcurrencyStamp = null;
            entity.UserRole?.ForEach(x => _dbContext.UserRoles.Remove(x));
        }
        entity.BuildingUser?.ForEach(x => _dbContext.BuildingUser.Remove(x));
        _dbSet.Update(entity);
        await _dbContext.SaveChangesAsync().ConfigureAwait(false);
    }

    public virtual async Task DeleteListAsync(List<Guid> ids)
    {
        foreach (var id in ids)
        {
            await DeleteAsync(id);
        }
    }

    public virtual async Task DeleteAsync(UserDTO entityVM)
    {
        await DeleteAsync(entityVM.Id);
    }

    public virtual async Task DeleteListAsync(List<UserDTO> entityListVM)
    {
        foreach (var entityVM in entityListVM)
        {
            await DeleteAsync(entityVM.Id);
        }
    }
}