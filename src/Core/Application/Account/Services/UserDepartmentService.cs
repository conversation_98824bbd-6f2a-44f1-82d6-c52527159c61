using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Domain.Shared;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Account;
using Application.Account.DTOs;
using Application.Account.FilterModel;
using Application.Shared.Data;

namespace Application.Account.Services;

public class UserDepartmentService(
    IMapper mapper,
    IApplicationDbContext dbContext
) : BaseService<UserDepartment, UserDepartmentDTO, Guid>(mapper, dbContext), IUserDepartmentService
{

    public async Task<PagedResponse<UserDepartmentDTO>> FilterAsync(UserDepartmentFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (filter.DepartmentId.HasValue)
        {
            filteredData = filteredData.Where(x => x.DepartmentId == filter.DepartmentId.Value);
        }
        if (filter.UserId.HasValue)
        {
            filteredData = filteredData.Where(x => x.UserId == filter.UserId.Value);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<UserDepartmentDTO>>(list);
        return data.ToPagedResponse(filter);
    }
}