using Domain.Shared;

namespace Application.Account.FilterModel;

public class UserFilterModel : BaseFilterModel
{
    public string? Email { get; set; }
    public string? UserName { get; set; }
    public string? FullName { get; set; }
    public string? PhoneNumber { get; set; }
    public Guid? CompanyId { get; set; }
    public Guid? RoleId { get; set; }
    public Guid[]? RoleIds { get; set; }
    public bool? Active { get; set; }
    public bool? IsMaster { get; set; }
    public bool? IsCompanyUser { get; set; }
    public DateTime? StartInsertDate { get; set; }
    public DateTime? EndInsertDate { get; set; }
}