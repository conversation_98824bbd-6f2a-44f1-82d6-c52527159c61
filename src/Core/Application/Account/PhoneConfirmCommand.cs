using MediatR;
using FluentValidation;
using Microsoft.AspNetCore.Identity;
using Domain.Shared;
using Domain.Account;
using Application.Shared.Data;
using Microsoft.EntityFrameworkCore;

namespace Application.Account;

public record PhoneConfirmCommand(string PhoneNumber, string Code) : IRequest;

public class PhoneConfirmCommandHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<PhoneConfirmCommand>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task Handle(PhoneConfirmCommand request, CancellationToken cancellationToken)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(x => x.PhoneNumber == request.PhoneNumber, cancellationToken)
            ?? throw new AppException("Kullanıcı bulunamadı");
        var userToken = await _dbContext.UserTokens.FirstOrDefaultAsync(x => x.LoginProvider == "REGISTERPHONE" && x.UserId == user.Id, cancellationToken)
            ?? throw new AppException("Kod Hatalı");
        var hasher = new PasswordHasher<User>();
        var hashedResult = hasher.VerifyHashedPassword(user, userToken.Value, request.Code);
        var expiredDate = DateTime.Parse(userToken.Name).AddMinutes(5);
        if (hashedResult != PasswordVerificationResult.Success || expiredDate < DateTime.Now)
        {
            throw new AppException("Kod Hatalı");
        }
        _dbContext.UserTokens.Remove(userToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
        user.PhoneNumberConfirmed = true;
        _dbContext.Users.Update(user);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}

public class PhoneConfirmCommandValidator : AbstractValidator<PhoneConfirmCommand>
{
    public PhoneConfirmCommandValidator()
    {
        RuleFor(x => x.PhoneNumber).NotEmpty();
        RuleFor(x => x.Code).NotEmpty();
    }
}