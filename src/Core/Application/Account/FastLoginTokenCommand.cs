using MediatR;
using FluentValidation;
using Microsoft.AspNetCore.Identity;
using Domain.Shared;
using Domain.Account;
using Application.Shared.Data;
using Microsoft.EntityFrameworkCore;

namespace Application.Account;

public record FastLoginTokenCommand(string PhoneNumber, string Code, bool IsPersistent) : IRequest;

public class FastLoginTokenCommandHandler(
    IApplicationDbContext dbContext,
    SignInManager<User> signinManager)
    : IRequestHandler<FastLoginTokenCommand>
{
    private readonly IApplicationDbContext _dbContext = dbContext;
    private readonly SignInManager<User> _signinManager = signinManager;

    public async Task Handle(FastLoginTokenCommand request, CancellationToken cancellationToken)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(x => x.PhoneNumber == request.PhoneNumber, cancellationToken)
            ?? throw new AppException("Kullanıcı bulunamadı");
        // if (!user.PhoneNumberConfirmed)
        // {
        //     throw new AppException("Telefon numarası onaylanmamış");
        // }
        var userToken = await _dbContext.UserTokens.FirstOrDefaultAsync(x => x.LoginProvider == "FASTLOGIN" && x.UserId == user.Id, cancellationToken)
            ?? throw new AppException("Kod Hatalı");
        var hasher = new PasswordHasher<User>();
        var hashedResult = hasher.VerifyHashedPassword(user, userToken.Value, request.Code);
        var expiredDate = DateTime.Parse(userToken.Name).AddMinutes(5);
        if (hashedResult != PasswordVerificationResult.Success || expiredDate < DateTime.Now)
        {
            throw new AppException("Kod Hatalı");
        }
        _dbContext.UserTokens.Remove(userToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
        _signinManager.AuthenticationScheme = IdentityConstants.BearerScheme;
        await _signinManager.SignInAsync(user, isPersistent: request.IsPersistent).ConfigureAwait(false);
        //Token Response a otomatik yazılıyormuş
    }
}

public class FastLoginTokenCommandValidator : AbstractValidator<FastLoginTokenCommand>
{
    public FastLoginTokenCommandValidator()
    {
        RuleFor(x => x.PhoneNumber).NotEmpty();
        RuleFor(x => x.Code).NotEmpty();
    }
}