using MediatR;
using FluentValidation;
using Microsoft.AspNetCore.Identity;
using Domain.Shared;
using Domain.Account;
using Domain.External;
using Application.Shared.Data;
using Microsoft.EntityFrameworkCore;

namespace Application.Account;

public record FastLoginCommand(string PhoneNumber) : IRequest;

public class FastLoginCommandHandler(
    AppSettings settings,
    ISMSManager smsService,
    IApplicationDbContext dbContext) : IRequestHandler<FastLoginCommand>
{
    private readonly AppSettings _settings = settings;
    private readonly ISMSManager _smsService = smsService;
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task Handle(FastLoginCommand request, CancellationToken cancellationToken)
    {
        var user = await _dbContext.Users.FirstOrDefaultAsync(x => x.PhoneNumber == request.PhoneNumber, cancellationToken)
            ?? throw new AppException("Kullanıcı bulunamadı");
        // if (!user.PhoneNumberConfirmed)
        // {
        //     throw new AppException("Telefon numarası onaylanmamış");
        // }
        var validationCode = new Random().Next(100000, 999999).ToString();
        if (user.Email == "<EMAIL>")
        {
            validationCode = "000000";
        }
        var hasher = new PasswordHasher<User>();
        var hashedCode = hasher.HashPassword(user, validationCode);
        var userToken = await _dbContext.UserTokens.FirstOrDefaultAsync(x => x.LoginProvider == "FASTLOGIN" && x.UserId == user.Id, cancellationToken);
        if (userToken != null)
        {
            _dbContext.UserTokens.Remove(userToken);
        }
        userToken = new UserToken
        {
            UserId = user.Id,
            Name = DateTime.Now.ToString(),
            LoginProvider = "FASTLOGIN",
            Value = hashedCode
        };
        _dbContext.UserTokens.Add(userToken);
        await _dbContext.SaveChangesAsync(cancellationToken);
        var phoneNumber = Clean(user.PhoneNumber);
        var smsMessages = _settings.SmsFastLoginMessagePrefix + validationCode + _settings.SmsFastLoginMessageSuffix;
        await _smsService.SendSmsAsync(phoneNumber, smsMessages);
    }

    public static string Clean(string phone)
    {
        phone = phone
            .Replace(" ", "")
            .TrimStart('+')
            .TrimStart('9')
            .TrimStart('0');
        return phone;
    }
}

public class FastLoginCommandValidator : AbstractValidator<FastLoginCommand>
{
    public FastLoginCommandValidator()
    {
        RuleFor(x => x.PhoneNumber).NotEmpty();
    }
}