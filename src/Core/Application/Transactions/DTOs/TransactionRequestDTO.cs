using Application.Account.DTOs;
using Application.Catalog.DTOs;
using Application.Companies.DTOs;
using Application.Contracts.DTOs;
using Application.Stations.DTOs;
using Domain.Shared;

namespace Application.Transactions.DTOs;

public class TransactionRequestDTO : BaseDTO
{
    public Guid Id { get; set; }
    public Guid BuildingId { get; set; }
    public BuildingDTO? Building { get; set; }
    public Guid? ContractId { get; set; }
    public ContractDTO? Contract { get; set; }
    public Guid? ProductId { get; set; }
    public ProductDTO? Product { get; set; }
    public List<TransactionRequestConcreteOptionDTO>? TransactionRequestConcreteOption { get; set; }
    public List<TransactionRequestPompTypeDTO>? TransactionRequestPompType { get; set; }
    
    // public int? ConcreteOptionId { get; set; }
    // public ConcreteOptionDTO? ConcreteOption { get; set; }
    public int? ConcreteLocationId { get; set; }
    public ConcreteLocationDTO? ConcreteLocation { get; set; }
    public int? ConsistencyClassId { get; set; }
    public ConsistencyClassDTO? ConsistencyClass { get; set; }
    public int StatusId { get; set; }
    public TransactionStatusDTO? Status { get; set; }
    public Guid RequestedPersonId { get; set; }
    public UserDTO? RequestedPerson { get; set; }
    public DateTime DesiredDateTime { get; set; }
    public int? CarTypeId { get; set; }
    public VehicleTypeDTO? CarType { get; set; }
    public DateTime? TransactionStartDateTime { get; set; }
    public DateTime? TransactionEndDateTime { get; set; }
    public DateTime? ApprovedDateTime { get; set; }
    public Guid? ApprovedUserId { get; set; }
    public UserDTO? ApprovedUser { get; set; }
    public string? ApprovedNote { get; set; }
    public string? Note { get; set; }
    public string? RejectNote { get; set; }
    public DateTime InsertDate { get; set; }
    public decimal? DistanceInDestination { get; set; }
    public decimal DesiredTotalConcrete { get; set; }
    public decimal? TotalConcreteRemaining { get; set; }
    public decimal? TotalConcreteSent { get; set; }
    public decimal? TotalConcreteRefundable { get; set; }
    public Guid? CanceledUserId { get; set; }
    public UserDTO? CanceledUser { get; set; }
    public string? CanceledNote { get; set; }
    public string? ErpOrderNo { get; set; }
    public int PriceTypeId { get; set; } // 1: Fişli, 2: Fişsiz, 3: Bantaltı
    public int TransactionrequestTypeId { get; set; }
    public TransactionRequestTypeDTO? TransactionrequestType { get; set; }
    public Guid? StationId { get; set; }
    public StationDTO? Station { get; set; }
    public List<LabResultDTO>? LabResult { get; set; }
    public List<ContractProductTransactionDTO>? ContractProductTransaction { get; set; }
}