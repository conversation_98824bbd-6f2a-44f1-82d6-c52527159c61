using Domain.Shared;

namespace Application.Transactions.FilterModel;

public class TransactionRequestFilterModel : BaseFilterModel
{
    public Guid[]? Ids { get; set; }
    public Guid[]? CompanyIds { get; set; }
    public Guid[]? BuildingIds { get; set; }
    public Guid[]? ContractIds { get; set; }
    public Guid[]? ProductIds { get; set; }
    public Guid[]? StationIds { get; set; }
    public int[]? StatusIds { get; set; }
    public int[]? ConsistencyClassId { get; set; }
    public Guid[]? RequestedPersonIds { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public DateTime? ApprovedStartDate { get; set; }
    public DateTime? ApprovedEndDate { get; set; }
    public DateTime? StartDesiredTime { get; set; }
    public DateTime? EndDesiredTime { get; set; }
    public Guid? VehicleId { get; set; }
    public Guid? DriverId { get; set; }
    public int? TransactionrequestTypeId { get; set; }
    public string? SearchKey { get; set; }
}