
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Transactions;
using Application.Transactions.DTOs;
using Application.Transactions.FilterModel;
using Application.Shared;
using Application.Shared.Data;
using Application.General.Services;
using Application.General.DTOs;
using Domain.Account;
using Domain.External;
using Domain.Shared;
using Domain.Companies;
using Application.Companies.DTOs;
using Application.Contracts.DTOs;
using Domain.Contracts;
using Application.Catalog.DTOs;
using Domain.Catalog;
using Application.Stations.DTOs;
using Domain.Stations;
using Application.Account.DTOs;
using Application.Account.Services;

namespace Application.Transactions.Services;

public class TransactionRequestService(
    IMapper mapper,
    IWorkContext workContext,
    ISMSManager smsManager,
    IEmailManager emailManager,
    IERPManager erpManager,
    INotificationService notificationService,
    IUserService userService,
    IApplicationDbContext dbContext)
    : BaseService<TransactionRequest, TransactionRequestDTO, Guid>(mapper, dbContext), ITransactionRequestService
{
    private readonly IWorkContext _workContext = workContext;
    private readonly ISMSManager _smsManager = smsManager;
    private readonly IEmailManager _emailManager = emailManager;
    private readonly IERPManager _erpManager = erpManager;
    private readonly INotificationService _notificationService = notificationService;
    private readonly IUserService _userService = userService;

    public async Task<PagedResponse<TransactionRequestDTO>> FilterAsync(TransactionRequestFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        var user = await _workContext.GetUserAsync();
        if (user != null)
        {
            if (user.RoleId == Role.BUILDING || user.RoleId == Role.COMPANY)
            {
                filteredData = filteredData.Where(x => x.Building.CompanyId == user.CompanyId);
            }
            if (user.RoleId == Role.CONTROL || user.RoleId == Role.MOULDER)
            {
                var userBuildingIds = await _dbContext.BuildingUser.Where(x => x.UserId == user.Id).Select(x => x.BuildingId).ToListAsync();
                filteredData = filteredData.Where(x => userBuildingIds.Contains(x.BuildingId));
            }
        }
        if (filter.Ids?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.Ids.Contains(x.Id));
        }
        if (filter.CompanyIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.CompanyIds.Contains(x.Building.CompanyId));
        }
        if (filter.VehicleId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ContractProductTransaction.Any(y => y.Vehicle.DriverId == filter.VehicleId));
        }
        if (filter.BuildingIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.BuildingIds.Contains(x.BuildingId));
        }
        if (filter.ContractIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.ContractIds.Contains(x.ContractId.Value));
        }
        if (filter.ProductIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.ProductIds.Contains(x.ProductId.Value));
        }
        if (filter.StatusIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.StatusIds.Contains(x.StatusId));
        }
        if (filter.StationIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.StationIds.Contains(x.StationId.Value));
        }
        if (filter.RequestedPersonIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.RequestedPersonIds.Contains(x.RequestedPersonId));
        }
        if (filter.ConsistencyClassId?.Length > 0)
        {
            filteredData = filteredData.Where(x => x.ConsistencyClassId.HasValue && filter.ConsistencyClassId.Contains(x.ConsistencyClassId.Value));
        }

        if (filter.StartDate.HasValue)
        {
            filteredData = filteredData.Where(x =>
            x.ApprovedDateTime.Value.Date >= filter.StartDate.Value.Date ||
            x.DesiredDateTime.Date >= filter.StartDate.Value.Date);
        }
        if (filter.EndDate.HasValue)
        {
            filteredData = filteredData.Where(x =>
            x.ApprovedDateTime.Value.Date <= filter.EndDate.Value.Date ||
            x.DesiredDateTime.Date <= filter.EndDate.Value.Date);
        }
        if (filter.ApprovedStartDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.ApprovedDateTime.Value.Date >= filter.ApprovedStartDate.Value.Date);
        }
        if (filter.ApprovedEndDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.ApprovedDateTime.Value.Date <= filter.ApprovedEndDate.Value.Date);
        }
        if (filter.StartDesiredTime.HasValue)
        {
            filteredData = filteredData.Where(x => x.DesiredDateTime.Date >= filter.StartDesiredTime.Value.Date);
        }
        if (filter.EndDesiredTime.HasValue)
        {
            filteredData = filteredData.Where(x => x.DesiredDateTime.Date <= filter.EndDesiredTime.Value.Date);
        }
        if (filter.TransactionrequestTypeId.HasValue)
        {
            filteredData = filteredData.Where(x => x.TransactionrequestTypeId == filter.TransactionrequestTypeId);
        }
        if (!string.IsNullOrWhiteSpace(filter.SearchKey))
        {
            var predicateBarcode = PredicateBuilder.False<TransactionRequest>();
            predicateBarcode = predicateBarcode.CustomOr(k => k.Note.Contains(filter.SearchKey));
            predicateBarcode = predicateBarcode.CustomOr(k => k.ApprovedNote.Contains(filter.SearchKey));
            predicateBarcode = predicateBarcode.CustomOr(k => k.Building.Name.Contains(filter.SearchKey));
            filteredData = filteredData.Where(predicateBarcode);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<TransactionRequestDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public Task<List<ConcreteLocationDTO>> GetConcreteLocationAsync()
    {
        return _dbContext
            .ConcreteLocation
            .AsQueryable()
            .Select(x =>
                new ConcreteLocationDTO
                {
                    Id = x.Id,
                    Name = x.Name,
                    SubItem = x.SubItem
                }
            )
            .ToListAsync();
    }

    public Task<List<ConcreteOptionDTO>> GetConcreteOptionAsync()
    {
        return _dbContext
            .ConcreteOption
            .AsQueryable()
            .Select(x =>
                new ConcreteOptionDTO
                {
                    Id = x.Id,
                    Name = x.Name
                }
            )
            .ToListAsync();
    }

    public Task<List<ConsistencyClassDTO>> GetConsistencyClassAsync()
    {
        return _dbContext
            .ConsistencyClass
            .AsQueryable()
            .Select(x =>
                new ConsistencyClassDTO
                {
                    Id = x.Id,
                    Name = x.Name,
                    Description = x.Description
                }
            )
            .ToListAsync();
    }

    public Task<List<TransactionRequestTypeDTO>> GetTransactionRequestTypeAsync()
    {
        return _dbContext
            .TransactionRequestType
            .Select(x => new TransactionRequestTypeDTO
            {
                Id = x.Id,
                Name = x.Name
            }).ToListAsync();
    }

    public Task<List<TransactionStatusDTO>> GetTransactionStatusAsync()
    {
        return _dbContext
            .TransactionStatus
            .AsQueryable()
            .Select(x =>
                new TransactionStatusDTO
                {
                    Id = x.Id,
                    Name = x.Name,
                    CustomerName = x.CustomerName
                }
            )
            .ToListAsync();
    }

    private async Task<Response<string>> CheckRequestApprove(TransactionRequest transactionRequest)
    {
        var contractList = await _dbContext
            .Contract
            .Where(x => x.ContractProduct.Any(x =>
                x.ProductId == transactionRequest.ProductId &&
                x.ContractProductBuilding.Any(x => x.BuildingId == transactionRequest.BuildingId)
                )).Including("ContractProduct.ContractProductBuilding").ToListAsync();
        if (contractList?.Count <= 0)
        {
            transactionRequest.ContractId = null;
            transactionRequest.ApprovedUserId = null;
            transactionRequest.ApprovedNote = null;
            transactionRequest.StatusId = 1;
            transactionRequest.RejectNote = "Bu isteğe ait ilgili sözleşme bulunamadı.";

            return new Response<string>(default!)
            {
                Value = "false",
                Message = transactionRequest.RejectNote
            };
        }
        var contractProduct = contractList
            .Where(x => x.ContractProduct != null)
            .SelectMany(x => x.ContractProduct)
            .FirstOrDefault(x => x.ProductId == transactionRequest.ProductId &&
                x.ContractProductBuilding != null &&
                x.ContractProductBuilding.Any(x => x.BuildingId == transactionRequest.BuildingId));
        if (contractProduct == null)
        {
            transactionRequest.ContractId = null;
            transactionRequest.ApprovedUserId = null;
            transactionRequest.ApprovedNote = null;
            transactionRequest.StatusId = 1;
            transactionRequest.RejectNote = "Sözleşmede talep edilen ürün bulunamadı.";

            return new Response<string>(default!)
            {
                Value = "false",
                Message = transactionRequest.RejectNote
            };
        }

        var findContract = await _dbContext.Contract
            .FirstOrDefaultAsync(x => x.Id == contractProduct.ContractId);

        // Fiyat tipine göre uygun fiyatı seç, yoksa varsayılan Price'ı kullan
        var unitPrice = transactionRequest.PriceTypeId switch
        {
            1 => contractProduct.Price, // Fişli
            2 => contractProduct.PriceWithoutPlug ?? contractProduct.Price, // Fişsiz (yoksa Price)
            3 => contractProduct.PriceUnderPowerPlant ?? contractProduct.Price, // Bantaltı (yoksa Price)
            _ => contractProduct.Price // Varsayılan
        };

        if (findContract != null &&
            (findContract.LeftWorth.HasValue ?
                findContract.LeftWorth < transactionRequest.DesiredTotalConcrete * unitPrice :
                findContract.LeftAmount < transactionRequest.DesiredTotalConcrete))
        {
            var result = RejectTransaction(transactionRequest);

            return new Response<string>(default!)
            {
                Value = result.Value,
                Message = result.Message
            };
        }

        var user = await _workContext.GetUserAsync();
        transactionRequest.ContractId = contractProduct.ContractId;
        transactionRequest.ApprovedUserId = user.Id;
        transactionRequest.ApprovedNote = "Onaylandı";
        transactionRequest.RejectNote = null;
        transactionRequest.StatusId = 2;

        return new Response<string>(default!)
        {
            Value = "true",
            Message = transactionRequest.ApprovedNote
        };
    }

    public override async Task<Response<TransactionRequestDTO>> InsertAsync(TransactionRequestDTO entityVM)
    {
        var entity = entityVM.MapTo<TransactionRequest>(_mapper);
        entity.Id = Guid.NewGuid();
        entity.TotalConcreteRefundable = 0;
        entity.TotalConcreteSent = 0;
        entity.TotalConcreteRemaining = entity.DesiredTotalConcrete;
        entity.PriceTypeId = 1; // Fişli
        var chechResult = await CheckRequestApprove(entity);
        var building = await _dbContext.Building.Including("Company").FirstOrDefaultAsync(x => x.Id == entity.BuildingId);
        var product = await _dbContext.Product.FindAsync(entity.ProductId);
        if (!entity.StationId.HasValue && building != null)
        {
            entity.StationId = building.StationId;
        }
        if (entity.TransactionRequestConcreteOption.Count > 0)
        {
            foreach (var item in entity.TransactionRequestConcreteOption)
            {
                item.TransactionRequestId = entity.Id;
                _dbContext.TransactionRequestConcreteOption.Add(item);
            }
        }
        if (entity.TransactionRequestPompType?.Count > 0)
        {
            foreach (var item in entity.TransactionRequestPompType)
            {
                item.Id = Guid.NewGuid();
                item.TransactionRequestId = entity.Id;
                _dbContext.TransactionRequestPompType.Add(item);
            }
        }
        var local = _dbSet.Add(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<TransactionRequestDTO>(_mapper);

        if (entity.TransactionRequestPompType?.Count > 0)
        {
            foreach (var item in entity.TransactionRequestPompType)
            {
                //Pompa Tipi için Araç Kontrolü
                var vehicle = await _dbContext.Vehicle
                    .Where(x => x.PompTypeId == item.PompTypeId)
                    .FirstOrDefaultAsync();

                //Seçilen Pompa Tipi kadar Hareket Kartı Eklenir
                var addContractProductTransaction = new ContractProductTransactionDTO
                {
                    Id = Guid.NewGuid(),
                    ContractId = data.ContractId,
                    TransactionRequestId = data.Id,
                    VehicleId = vehicle.Id,
                    DriverId = vehicle.DriverId,
                    StatusId = 1,
                    DocumentNo = "",
                    SendingAmount = 0,
                    StationId = data.StationId,
                    TypeId = 1,
                    PompTypeId = item.PompTypeId,
                };
                _dbContext.ContractProductTransaction.Add(_mapper.Map<ContractProductTransaction>(addContractProductTransaction));

                await _dbContext
                .SaveChangesAsync()
                .ConfigureAwait(false);

                // Hareket Kartı için Log Ekleme
                await AddLog(addContractProductTransaction.Id, ContractProductTransactionStatusEnum.Pending)
                    .ConfigureAwait(false);
            }
        }

        var title = "Onay bekleyen talep var.";
        var description = title + " Müşteri:" + building?.Company?.Name + ", Şantiye:" + building?.Name + ", Tarih:" + entity.DesiredDateTime.ToString("dd.MM.yyyy HH:mm") + ", İstenilen: " + product?.Name + ", Miktar: " + entity.DesiredTotalConcrete;
        if (chechResult.Value == "true")
        {
            title = "Yeni bir talep eklendi.";
        }
        var salesUsers = await _dbContext
                .Users
                .Where(x => x.UserRole.Any(x => x.RoleId == Role.SALES)).ToListAsync();
        foreach (var salesUser in salesUsers)
        {
            // SMS gönderimi - hata durumunda sistem patlamasın
            if (!string.IsNullOrWhiteSpace(salesUser.PhoneNumber))
            {
                try
                {
                    await _smsManager.SendSmsAsync(salesUser.PhoneNumber, description);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"SMS gönderim hatası - Kullanıcı: {salesUser.Id}, Telefon: {salesUser.PhoneNumber}, Hata: {ex.Message}");
                }
            }

            // Email gönderimi - hata durumunda sistem patlamasın
            try
            {
                await _emailManager.SendEmailAsync(salesUser.Email, title, description);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Email gönderim hatası - Kullanıcı: {salesUser.Id}, Email: {salesUser.Email}, Hata: {ex.Message}");
            }
        }
        return data.ToResponse();
    }

    public override async Task<PagedResponse<TransactionRequestDTO>> InsertListAysnc(List<TransactionRequestDTO> entityListVM)
    {
        var list = new List<TransactionRequestDTO>();
        foreach (var entityVM in entityListVM)
        {
            var result = await InsertAsync(entityVM);
            list.Add(result.Value);
        }
        return list.ToPagedResponse();
    }

    public async Task<Response<string>> Approve(Guid Id, string? note)
    {
        var entity = await _dbSet.FindAsync(Id) ?? throw new Exception("Kayıt bulunamadı");
        var result = await CheckRequestApprove(entity);
        if (result.Value == "true")
        {
            entity.ApprovedNote = note;
        }
        await _dbContext.SaveChangesAsync();
        if (result.Value == "false")
        {
            return new Response<string>(default!)
            {
                Value = result.Value,
                Message = result.Message
            };
        }
        else
        {
            return new Response<string>(default!)
            {
                Value = result.Value,
                Message = result.Message
            };
        }
    }

    public async Task Reject(Guid Id, string note)
    {
        var entity = await _dbSet.FindAsync(Id) ?? throw new Exception("Kayıt bulunamadı");
        entity.RejectNote = note;
        entity.StatusId = 8;
        await _dbContext.SaveChangesAsync();
    }

    public async Task Cancel(Guid Id, string note)
    {
        var entity = await _dbSet.FindAsync(Id) ?? throw new Exception("Kayıt bulunamadı");
        entity.StatusId = 7;
        entity.CanceledNote = note;
        var loginUser = await _workContext.GetUserAsync();
        entity.CanceledUserId = loginUser.Id;
        await _dbContext.SaveChangesAsync();
    }

    public override async Task<Response<TransactionRequestDTO>> UpdateAsync(TransactionRequestDTO entityVM)
    {
        if (entityVM.ApprovedDateTime.HasValue && entityVM.ApprovedDateTime.Value.Date < DateTime.Now.Date)
        {
            throw new Exception("Geçmiş tarih seçilemez");
        }
        var entity = _dbSet.AsQueryable().Including("Building", "TransactionRequestConcreteOption","TransactionRequestPompType", "Product").FirstOrDefault(x => x.Id == entityVM.Id) ?? throw new Exception("Kayıt bulunamadı");
        entity.BuildingId = entityVM.BuildingId;
        entity.ContractId = entityVM.ContractId;
        entity.ProductId = entityVM.ProductId;
        entity.ConcreteLocationId = entityVM.ConcreteLocationId;
        entity.ConsistencyClassId = entityVM.ConsistencyClassId;
        entity.RequestedPersonId = entityVM.RequestedPersonId;
        entity.ApprovedDateTime = entityVM.ApprovedDateTime;
        entity.ApprovedNote = entityVM.ApprovedNote;
        entity.CarTypeId = entityVM.CarTypeId;
        entity.Note = entityVM.Note;
        entity.DistanceInDestination = entityVM.DistanceInDestination;
        entity.DesiredTotalConcrete = entityVM.DesiredTotalConcrete;
        entity.TransactionrequestTypeId = entityVM.TransactionrequestTypeId;
        entity.StationId = entityVM.StationId;
        entity.TransactionRequestConcreteOption ??= [];
        entityVM.TransactionRequestConcreteOption ??= [];
        entity.TransactionRequestPompType ??= [];
        entityVM.TransactionRequestPompType ??= [];


        if (entityVM.StatusId == 7) // İptal işlemi yapan kullanıcı bilgisi ve açıklaması alınır
        {
            entity.StatusId = 7;
            entity.CanceledNote = entityVM.CanceledNote;
            var loginUser = await _workContext.GetUserAsync();
            entity.CanceledUserId = loginUser.Id;
        }

        if (!(entityVM.StatusId == 1 && entity.StatusId == 2) && !(entityVM.StatusId == 7 && entity.StatusId >= 5))
        {
            entity.StatusId = entityVM.StatusId;
        }

        foreach (var item in entity.TransactionRequestConcreteOption.Where(k => !entityVM.TransactionRequestConcreteOption?.Any(l => l.ConcreteOptionId == k.ConcreteOptionId) == true).ToList())
        {
            entity.TransactionRequestConcreteOption.Remove(item);
        }
        foreach (var item in entityVM.TransactionRequestConcreteOption.Where(k => !entity.TransactionRequestConcreteOption.Any(l => l.ConcreteOptionId == k.ConcreteOptionId)).ToList())
        {
            entity.TransactionRequestConcreteOption.Add(item.MapTo<TransactionRequestConcreteOption>(_mapper));
        }

        foreach (var item in entity.TransactionRequestPompType.Where(k => !entityVM.TransactionRequestPompType?.Any(l => l.PompTypeId == k.PompTypeId) == true).ToList())
        {
            entity.TransactionRequestPompType.Remove(item);
        }
        foreach (var item in entityVM.TransactionRequestPompType.Where(k => !entity.TransactionRequestPompType.Any(l => l.PompTypeId == k.PompTypeId)).ToList())
        {
            entity.TransactionRequestPompType.Add(item.MapTo<TransactionRequestPompType>(_mapper));
        }

        var local = _dbSet.Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<TransactionRequestDTO>(_mapper);
        if (entity.StatusId == (int)TransactionStatusEnum.Planned || entity.StatusId == (int)TransactionStatusEnum.InProgress)
        {
            var userIds = _dbContext.BuildingUser.AsQueryable().Where(x => x.BuildingId == entity.BuildingId).Select(x => x.UserId);
            try
            {
                var note = string.Empty;
                if (entity.StatusId == (int)TransactionStatusEnum.Planned)
                {
                    note = "Talebiniz planlandı";
                }
                else if (entity.StatusId == (int)TransactionStatusEnum.InProgress)
                {
                    note = "Talebiniz başlatıldı";
                }
                note += ", Şantiye:" + entity.Building.Name;
                note += ", Ürün:" + entity.Product.Name;
                note += ", Miktar:" + entity.DesiredTotalConcrete.ToString("0.#");
                await _notificationService.BulkSendAsync(new BulkSendNotificationDTO
                {
                    UserIds = [.. userIds],
                    Title = "Araç Durumu",
                    Note = note,
                    Type = "TransactionRequest",
                    Data = entity.Id.ToString()
                });
            }
            catch (Exception ex)
            {
                // Notification gönderim hatası loglanabilir ama sistem devam etsin
                Console.WriteLine($"Notification gönderim hatası - TransactionRequest: {entity.Id}, Hata: {ex.Message}");
            }
        }
        return data.ToResponse();
    }

    public override async Task DeleteAsync(Guid id)
    {
        var entity = await _dbSet.FindAsync(id);
        entity.IsDeleted = true;
        _ = _dbSet.Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public override async Task DeleteAsync(TransactionRequestDTO entityVM)
    {
        await DeleteAsync(entityVM.Id);
    }

    public override async Task DeleteListAsync(List<Guid> ids)
    {
        foreach (var id in ids)
        {
            await DeleteAsync(id);
        }
    }

    public override async Task DeleteListAsync(List<TransactionRequestDTO> entityListVM)
    {
        foreach (var entityVM in entityListVM)
        {
            await DeleteAsync(entityVM.Id);
        }
    }

    public override async Task PatchAsync(PatchDTO patchDTO)
    {
        var entity = await _dbSet
            .FindAsync(patchDTO.Id)
            .ConfigureAwait(false);
        if (entity != null)
        {
            var a = entity.GetType();
            patchDTO.PatchModel.ApplyTo(entity);
            _dbSet.Update(entity);
            await _dbContext
                .SaveChangesAsync()
                .ConfigureAwait(false);
            if (entity.StatusId == (int)TransactionStatusEnum.Planned || entity.StatusId == (int)TransactionStatusEnum.InProgress)
            {
                var userIds = _dbContext.BuildingUser.AsQueryable().Where(x => x.BuildingId == entity.BuildingId).Select(x => x.UserId);
                try
                {
                    var note = string.Empty;
                    if (entity.StatusId == (int)TransactionStatusEnum.Planned)
                    {
                        note = "Talebiniz planlandı";
                    }
                    else if (entity.StatusId == (int)TransactionStatusEnum.InProgress)
                    {
                        note = "Talebiniz başlatıldı";
                    }
                    await _notificationService.BulkSendAsync(new BulkSendNotificationDTO
                    {
                        UserIds = [.. userIds],
                        Title = "Araç Durumu",
                        Note = note,
                        Type = "TransactionRequest",
                        Data = entity.Id.ToString()
                    });
                }
                catch (Exception ex)
                {
                    // Notification gönderim hatası loglanabilir ama sistem devam etsin
                    Console.WriteLine($"Notification gönderim hatası - TransactionRequest: {entity.Id}, Hata: {ex.Message}");
                }
            }
        }
    }

    public override async Task PatchListAsync(List<PatchDTO> patchListVM)
    {
        foreach (var item in patchListVM)
        {
            await PatchAsync(item);
        }
    }

    public async Task<Response<string>> TransferOrderByErpAsync()
    {
        try
        {
            var getOrderList = await _erpManager.GetOrderList()
                .ConfigureAwait(false);

            if (getOrderList == null || getOrderList.Count == 0)
            {
                throw new Exception("ERP sisteminden sipariş alınamadı.");
            }

            // Giriş yapmış kullanıcı kontrolü
            var loginUser = await _workContext.GetUserAsync();
            if (loginUser == null)
            {
                throw new Exception("Kullanıcı oturumu bulunamadı.");
            }

            // Default sözleşme oluşturma
            var contract = new Contract()
            {
                Title = "ERP Default Sözleşmesi",
                StartDate = DateTime.Now,
                EndDate = DateTime.Now.AddYears(1),
                InsertDate = DateTime.Now,
                Active = true,
                Description = "ERP sisteminden otomatik olarak aktarıldı."
            };

            foreach (var order in getOrderList)
            {
                // İstasyon kontrolü
                var stationMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
                {
                    ["Merkez"] = "SANTRAL 1",
                    ["INCESU"] = "İNCESU SANTRAL",
                    ["HACILAR"] = "HACILAR SANTRAL",
                    ["SARIOGLAN"] = "SARIOĞLAN SANTRAL"
                };

                string mappedStationName = stationMap.TryGetValue(order.SantralAdi, out var name)
                    ? name
                    : order.SantralAdi;

                var station = await _dbContext.Station
                    .Where(x => x.IsDeleted == false)
                    .FirstOrDefaultAsync(x => x.Name == mappedStationName)
                    .ConfigureAwait(false);

                if (station == null)
                {
                    var stationDTO = new StationDTO
                    {
                        Name = mappedStationName,
                        Description = "ERP sisteminden otomatik olarak aktarıldı.",
                        StatusId = 1,
                        InsertDate = DateTime.Now,
                        Address = "",
                    };
                    station = _mapper.Map<Station>(stationDTO);
                    _dbContext.Station.Add(station);

                    await _dbContext
                        .SaveChangesAsync()
                        .ConfigureAwait(false);
                }

                // Firma kontrolü
                Company? company = null;

                if (!string.IsNullOrWhiteSpace(order.CariKodu))
                {
                    // Önce CariKodu ile AccountingCode'dan ara
                    company = await _dbContext
                        .Company
                        .Where(x => x.IsDeleted == false)
                        .FirstOrDefaultAsync(x => x.AccountingCode == order.CariKodu)
                        .ConfigureAwait(false);
                }
                else if (!string.IsNullOrWhiteSpace(order.FirmaAdi))
                {
                    // CariKodu boşsa FirmaAdi ile Name'den ara
                    company = await _dbContext
                        .Company
                        .Where(x => x.IsDeleted == false)
                        .FirstOrDefaultAsync(x => x.Name == order.FirmaAdi)
                        .ConfigureAwait(false);
                }

                // Eğer her iki alan da boşsa bu siparişi atla
                if (string.IsNullOrWhiteSpace(order.CariKodu) && string.IsNullOrWhiteSpace(order.FirmaAdi))
                {
                    continue;
                }

                if (company == null)
                {
                    var companyDTO = new CompanyDTO
                    {
                        Name = order.FirmaAdi,
                        AccountingCode = !string.IsNullOrWhiteSpace(order.CariKodu) ? order.CariKodu : null,
                        Description = "ERP sisteminden otomatik olarak aktarıldı.",
                        Active = true,
                    };
                    company = _mapper.Map<Company>(companyDTO);
                    _dbContext.Company.Add(company);

                    await _dbContext
                        .SaveChangesAsync()
                        .ConfigureAwait(false);
                }

                // Sözleşme kontrolü
                contract = await _dbContext
                    .Contract
                    .Where(x => x.IsDeleted == false)
                    .FirstOrDefaultAsync(x => x.CompanyId == company.Id)
                    .ConfigureAwait(false);

                if (contract == null)
                {
                    // Yeni sözleşme oluşturma
                    contract = new Contract
                    {
                        Id = Guid.NewGuid(),
                        Title = $"{order.FirmaAdi} - {DateTime.Now:dd.MM.yyyy}",
                        StartDate = DateTime.Now,
                        EndDate = DateTime.Now.AddYears(1),
                        InsertDate = DateTime.Now,
                        Description = "ERP sisteminden otomatik olarak aktarıldı.",
                        Active = true,
                        CompanyId = company.Id,
                        ApprovedDateTime = DateTime.Now,
                        ApprovedNote = "ERP sisteminden otomatik olarak aktarıldı.",
                        InsertUserId = loginUser.Id,
                        ApprovedUserId = loginUser.Id,
                        UpdateDate = DateTime.Now,

                    };
                    _dbContext.Contract.Add(contract);

                    await _dbContext
                        .SaveChangesAsync()
                        .ConfigureAwait(false);
                }


                // Ürün kontrolü
                var product = await _dbContext
                    .Product
                    .Where(x => x.IsDeleted == false)
                    .FirstOrDefaultAsync(x => x.Name == order.UrunAdi)
                    .ConfigureAwait(false);

                if (product == null)
                {
                    var productDTO = new ProductDTO
                    {
                        Name = order.UrunAdi,
                        Description = "ERP sisteminden otomatik olarak aktarıldı.",
                    };

                    product = _mapper.Map<Product>(productDTO);
                    _dbContext.Product.Add(product);

                    await _dbContext
                        .SaveChangesAsync()
                        .ConfigureAwait(false);
                }


                // Şantiye kontrolü
                var building = await _dbContext
                    .Building
                    .Where(x => x.IsDeleted == false)
                    .FirstOrDefaultAsync(x =>
                        x.Name == order.SantiyeAdi
                        && x.CompanyId == company.Id
                        && x.StationId == station.Id
                    )
                    .ConfigureAwait(false);

                if (building == null)
                {
                    var buildingDTO = new BuildingDTO
                    {
                        Name = order.SantiyeAdi,
                        CompanyId = company.Id,
                        Active = true,
                        StationId = station.Id
                    };
                    building = _mapper.Map<Building>(buildingDTO);

                    _dbContext.Building.Add(building);
                    await _dbContext
                        .SaveChangesAsync()
                        .ConfigureAwait(false);
                }

                //Kullanıcının sözleşmesi ve sözleşmesinde ürünleri varsa ve talepte gelen şantiye bu sözleşmedeki ürünlerde yok ise
                // bu sözleşmedeki ürünlerin tamamına şantiye eklenir
                if (contract.ContractProduct != null && contract.ContractProduct.Any())
                {
                    var existingBuildingIds = contract.ContractProduct
                        .SelectMany(cp => cp.ContractProductBuilding)
                        .Select(cb => cb.BuildingId)
                        .Distinct()
                        .ToList();

                    if (!existingBuildingIds.Contains(building.Id))
                    {
                        var newContractProductBuilding = contract.ContractProduct
                            .Select(cp => new ContractProductBuilding
                            {
                                ContractProductId = cp.Id,
                                BuildingId = building.Id
                            })
                            .ToList();

                        _dbContext.ContractProductBuilding.AddRange(newContractProductBuilding);
                        await _dbContext
                            .SaveChangesAsync()
                            .ConfigureAwait(false);
                    }
                }
                

                // Kullanıcı kontrolü
                var userId = loginUser.Id;

                var user = await _dbContext
                    .Users
                    .Where(x => x.IsDeleted == false)
                    .FirstOrDefaultAsync(x => x.CompanyId == company.Id && x.Name == "Erp Kullanıcı")
                    .ConfigureAwait(false);

                if (user == null)
                {
                    var registerUser = new RegisterDTO
                    {
                        Active = true,
                        CompanyId = company.Id,
                        Name = "Erp Kullanıcı",
                        Surname = ""
                    };
                    var insertUser = await _userService.InsertAsync(registerUser)
                        .ConfigureAwait(false);

                    userId = insertUser.Value.Id;
                }
                else
                {
                    userId = user.Id;
                }
                

                var findTransaction = await _dbContext
                    .TransactionRequest
                    .FirstOrDefaultAsync(x =>
                       x.ErpOrderNo == order.SiparisNo
                    )
                    .ConfigureAwait(false);

                if (findTransaction != null)
                {
                    // Eğer aynı talep zaten varsa, atla
                    continue;
                }

                // PriceTypeId belirleme
                int priceTypeId = 1; // Varsayılan: Fişli
                if (!string.IsNullOrWhiteSpace(order.FirmaAdi))
                {
                    var firmaAdi = order.FirmaAdi.ToUpperInvariant();
                    if (firmaAdi.Contains("FİŞSİZ"))
                    {
                        priceTypeId = 2; // Fişsiz
                    }
                    else if (firmaAdi.Contains("SANTRAL ALTI"))
                    {
                        priceTypeId = 3; // Santral Altı
                    }
                    // "FİŞLİ" içeriyorsa veya hiçbiri yoksa varsayılan 1 kalır
                }

                var transactionRequestDTO = new TransactionRequestDTO
                {
                    BuildingId = building.Id,
                    ProductId = product.Id,
                    StationId = station.Id,
                    RequestedPersonId = userId,
                    ContractId = contract.Id,
                    DesiredTotalConcrete = order.Istenen,
                    TotalConcreteRemaining = order.Kalan,
                    TotalConcreteSent = order.Verilen,
                    TotalConcreteRefundable = 0,
                    DesiredDateTime = order.SiparisTarihi,
                    Note = order.Aciklama,
                    ApprovedDateTime = DateTime.Now,
                    ApprovedNote = "Sipariş ERP sisteminden otomatik olarak aktarıldı.",
                    InsertDate = DateTime.Now,
                    TransactionrequestTypeId = 1, // Normal sipariş
                    ErpOrderNo = order.SiparisNo,
                    PriceTypeId = priceTypeId,
                };

                if (order.HasPomp)
                {
                    transactionRequestDTO.TransactionRequestPompType = new List<TransactionRequestPompTypeDTO>
                    {
                        new TransactionRequestPompTypeDTO
                        {
                            PompTypeId = 9, // Sabit Pompa Türü
                        }
                    };
                }

                var transactionRequest = _mapper.Map<TransactionRequest>(transactionRequestDTO);

                // Sözleşme kontrolü ve onaylama
                var isApproved = await CheckRequestApprove(transactionRequest);         
                transactionRequest.StatusId = isApproved.Value == "true" ? 2 : 1; // Onaylandı veya onay bekliyor

                _dbContext.TransactionRequest.Add(transactionRequest);
                await _dbContext
                    .SaveChangesAsync()
                    .ConfigureAwait(false);
            }

            return "Siparişler ERP sisteminden başarıyla aktarıldı.".ToResponse();
        }
        catch (Exception ex)
        {
            throw new Exception("Sipariş aktarımı sırasında bir hata oluştu: " + ex.Message);
        }
    }

    private Response<string> RejectTransaction(TransactionRequest transactionRequest)
    {
        transactionRequest.ContractId = null;
        transactionRequest.ApprovedUserId = null;
        transactionRequest.ApprovedNote = null;
        transactionRequest.StatusId = 1;
        transactionRequest.RejectNote = "Kalan sözleşme bedeli sipariş için yeterli değildir.";

        return new Response<string>(default!)
        {
            Value = "false",
            Message = transactionRequest.RejectNote
        };
    }

    private async Task AddLog(Guid ContractProductTransactionId, ContractProductTransactionStatusEnum StatusId)
    {
        var description = StatusId switch
        {
            ContractProductTransactionStatusEnum.Pending => "Araç Beklemede",
            ContractProductTransactionStatusEnum.OnRoad => "Araç Yola Çıktı",
            ContractProductTransactionStatusEnum.Finished => "Araç Varış Noktasına Ulaştı",
            ContractProductTransactionStatusEnum.Cancel => "İptal Edildi",
            _ => ""
        };
        var log = new ContractProductTransactionLog
        {
            Description = description,
            InsertDate = DateTime.Now,
            ContractProductTransactionId = ContractProductTransactionId,
        };
        _dbContext.ContractProductTransactionLog.Add(log);
        await _dbContext.SaveChangesAsync().ConfigureAwait(false);
    }
}
