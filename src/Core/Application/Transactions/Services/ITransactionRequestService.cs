using Application.Shared.Services;
using Application.Shared.Wrappers;
using Application.Transactions.DTOs;
using Application.Transactions.FilterModel;

namespace Application.Transactions.Services;

public interface ITransactionRequestService : IBaseService<TransactionRequestDTO, Guid>
{
    Task<PagedResponse<TransactionRequestDTO>> FilterAsync(TransactionRequestFilterModel filter);
    Task<List<TransactionStatusDTO>> GetTransactionStatusAsync();
    Task<List<ConcreteLocationDTO>> GetConcreteLocationAsync();
    Task<List<ConcreteOptionDTO>> GetConcreteOptionAsync();
    Task<List<ConsistencyClassDTO>> GetConsistencyClassAsync();
    Task<List<TransactionRequestTypeDTO>> GetTransactionRequestTypeAsync();
    Task<Response<string>> Approve(Guid Id, string? note);
    Task Reject(Guid Id, string note);
    Task Cancel(Guid Id, string note);
    Task<Response<string>> TransferOrderByErpAsync();
}