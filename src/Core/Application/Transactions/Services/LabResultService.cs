using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Transactions;
using Application.Transactions.DTOs;
using Application.Transactions.FilterModel;
using Application.Shared.Data;

namespace Application.Transactions.Services;

public class LabResultService(
    IMapper mapper,
    IApplicationDbContext dbContext
) : BaseService<LabResult, LabResultDTO, Guid>(mapper, dbContext), ILabResultService
{
    public async Task<PagedResponse<LabResultDTO>> FilterAsync(LabResultFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (filter.TransactionRequestId.HasValue)
        {
            filteredData = filteredData.Where(x => x.TransactionRequestId == filter.TransactionRequestId.Value);
        }
        if (filter.CompanyId.HasValue)
        {
            filteredData = filteredData.Where(x => x.TransactionRequest.Building.CompanyId == filter.CompanyId.Value);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<LabResultDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public override async Task<Response<LabResultDTO>> InsertAsync(LabResultDTO entityVM)
    {
        if (entityVM.File != null)
        {
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "LabResult");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }
            var extension = Path.GetExtension(entityVM.File.FileName);
            var labResultId = Guid.NewGuid();
            var fileName = labResultId + extension;
            var filePath = Path.Combine(uploadsPath, fileName);
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                entityVM.File.CopyTo(stream);
            }
            entityVM.FileName = fileName;
        }
        return await base.InsertAsync(entityVM);
    }

    public override async Task<PagedResponse<LabResultDTO>> InsertListAysnc(List<LabResultDTO> entityListVM)
    {
        if (entityListVM?.Any(x => x.File != null) == true)
        {
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "LabResult");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }
            foreach (var file in entityListVM.Where(x => x.File != null))
            {
                var extension = Path.GetExtension(file.File.FileName);
                var labResultId = Guid.NewGuid();
                var fileName = labResultId + extension;
                var filePath = Path.Combine(uploadsPath, fileName);
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    file.File.CopyTo(stream);
                }
                file.FileName = fileName;
            }
        }
        return await base.InsertListAysnc(entityListVM);
    }

    public override async Task DeleteAsync(Guid id)
    {
        var entity = await _dbSet
            .FindAsync(id)
            .ConfigureAwait(false);
        if (!string.IsNullOrWhiteSpace(entity.FileName) && File.Exists(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "LabResult", entity.FileName)))
        {
            File.Delete(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "LabResult", entity.FileName));
        }
        _dbSet.Remove(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public override async Task DeleteListAsync(List<Guid> ids)
    {
        var entityList = new List<LabResult>();
        foreach (var id in ids)
        {
            var entity = await _dbSet
                .FindAsync(id)
                .ConfigureAwait(false);
            if (entity != null)
            {
                if (!string.IsNullOrWhiteSpace(entity.FileName) && File.Exists(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "LabResult", entity.FileName)))
                {
                    File.Delete(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "LabResult", entity.FileName));
                }
                entityList.Add(entity);
            }
        }
        _dbSet.RemoveRange(entityList);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }
}