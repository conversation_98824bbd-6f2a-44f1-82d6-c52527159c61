using Application.Transactions.DTOs;
using AutoMapper;
using Domain.Transactions;

namespace Application.Transactions.Mapping;

public class TransactionMapping : Profile
{
    public TransactionMapping()
    {
        CreateMap<TransactionRequest, TransactionRequestDTO>().ReverseMap();
        CreateMap<TransactionStatus, TransactionStatusDTO>().ReverseMap();
        CreateMap<ConcreteOption, ConcreteOptionDTO>().ReverseMap();
        CreateMap<ConcreteLocation, ConcreteLocationDTO>().ReverseMap();
        CreateMap<ConsistencyClass, ConsistencyClassDTO>().ReverseMap();
        CreateMap<LabResult, LabResultDTO>().ReverseMap();
        CreateMap<TransactionRequestConcreteOption, TransactionRequestConcreteOptionDTO>().ReverseMap();
        CreateMap<TransactionRequestPompType, TransactionRequestPompTypeDTO>().ReverseMap();
    }
}