using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Companies;
using Application.Companies.DTOs;
using Application.Companies.FilterModel;
using Application.Shared.Data;

namespace Application.Companies.Services;

public class BuildingUserService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<BuildingUser, BuildingUserDTO, Guid>(mapper, dbContext), IBuildingUserService
{

    public async Task<PagedResponse<BuildingUserDTO>> FilterAsync(BuildingUserFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (filter.BuildingIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.BuildingIds.Contains(x.BuildingId.Value));
        }
        if (filter.UserIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.UserIds.Contains(x.UserId));
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<BuildingUserDTO>>(list);
        return data.ToPagedResponse(filter);
    }
}