using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Domain.Shared;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Companies;
using Application.Companies.DTOs;
using Application.Companies.FilterModel;
using Application.Shared.Data;

namespace Application.Companies.Services;

public class CompanyService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<Company, CompanyDTO, Guid>(mapper, dbContext), ICompanyService
{
    public async Task<PagedResponse<CompanyDTO>> FilterAsync(CompanyFilterModel filter)
    {
        var allData = _dbContext.Set<Company>().AsQueryable().Where(x => !x.IsDeleted);
        var filteredData = allData;
        if (filter.Ids?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.Ids.Contains(x.Id));
        }
        if (filter.Active.HasValue)
        {
            filteredData = filteredData.Where(x => x.Active == filter.Active);
        }
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        }
        if (!string.IsNullOrWhiteSpace(filter.Email))
        {
            filteredData = filteredData.Where(x => x.Email.Contains(filter.Email));
        }
        if (!string.IsNullOrWhiteSpace(filter.Phone))
        {
            filteredData = filteredData.Where(x => x.Phone.Contains(filter.Phone));
        }
        if (!string.IsNullOrWhiteSpace(filter.TaxOffice))
        {
            filteredData = filteredData.Where(x => x.TaxOffice.Contains(filter.TaxOffice));
        }
        if (!string.IsNullOrWhiteSpace(filter.TaxNumber))
        {
            filteredData = filteredData.Where(x => x.TaxNumber.Contains(filter.TaxNumber));
        }
        if (!string.IsNullOrWhiteSpace(filter.Address))
        {
            filteredData = filteredData.Where(x => x.Address.Contains(filter.Address));
        }
        if (!string.IsNullOrWhiteSpace(filter.Description))
        {
            filteredData = filteredData.Where(x => x.Description.Contains(filter.Description));
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<CompanyDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    //Delete
    public override async Task DeleteAsync(Guid Id)
    {
        var entity = await _dbContext.Company.FindAsync(Id);
        if (entity == null)
            throw new Exception("Firma bulunamadı.");

        entity.IsDeleted = true;
        _dbContext.Company.Update(entity);
        await _dbContext.SaveChangesAsync();
    }
}