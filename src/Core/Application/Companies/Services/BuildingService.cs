using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Companies;
using Application.Companies.DTOs;
using Application.Companies.FilterModel;
using Application.Shared;
using Application.Shared.Data;
using Domain.Account;

namespace Application.Companies.Services;

public class BuildingService(
    IMapper mapper,
    IWorkContext workContext,
    IApplicationDbContext dbContext)
    : BaseService<Building, BuildingDTO, Guid>(mapper, dbContext), IBuildingService
{
    private readonly IWorkContext _workContext = workContext;

    public async Task<PagedResponse<BuildingDTO>> FilterAsync(BuildingFilterModel filter)
    {
        var allData = _dbContext.Set<Building>().AsQueryable().Where(x => !x.IsDeleted);
        var filteredData = allData;
        var user = await _workContext.GetUserAsync();
        if (user != null)
        {
            if (user.RoleId == Role.BUILDING || user.RoleId == Role.COMPANY)
            {
                filteredData = filteredData.Where(x => x.CompanyId == user.CompanyId);
            }
            if (user.RoleId == Role.CONTROL || user.RoleId == Role.MOULDER)
            {
                var userBuildingIds = await _dbContext.BuildingUser.Where(x => x.UserId == user.Id).Select(x => x.BuildingId).ToListAsync();
                filteredData = filteredData.Where(x => userBuildingIds.Contains(x.Id));
            }
        }
        if (filter.Ids?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.Ids.Contains(x.Id));
        }
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        }
        if (filter.CompanyId.HasValue)
        {
            filteredData = filteredData.Where(x => x.CompanyId == filter.CompanyId);
        }
        if (filter.StationId.HasValue)
        {
            filteredData = filteredData.Where(x => x.StationId == filter.StationId);
        }
        if (filter.Status.HasValue)
        {
            filteredData = filteredData.Where(x => x.Active == filter.Status);
        }
        if (filter.StateProvinceId.HasValue)
        {
            filteredData = filteredData.Where(x => x.StateProvinceId == filter.StateProvinceId);
        }
        if (filter.DistrictId.HasValue)
        {
            filteredData = filteredData.Where(x => x.DistrictId == filter.DistrictId);
        }
        if (filter.CityId.HasValue)
        {
            filteredData = filteredData.Where(x => x.CityId == filter.CityId);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<BuildingDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    //Delete
    public override async Task DeleteAsync(Guid Id)
    {
        var entity = await _dbContext.Building.FindAsync(Id);
        if (entity == null)
            throw new Exception("Şantiye bulunamadı.");

        entity.IsDeleted = true;
        _dbContext.Building.Update(entity);
        await _dbContext.SaveChangesAsync();
    }
}