using Application.Account.DTOs;
using Application.General.DTOs;
using Application.Stations.DTOs;
using Domain.Shared;

namespace Application.Companies.DTOs;

public class BuildingDTO : BaseDTO
{
    public Guid Id { get; set; }
    public bool Active { get; set; }
    public required string Name { get; set; }
    public Guid CompanyId { get; set; }
    public CompanyDTO? Company { get; set; }
    public string? Address { get; set; }
    public Guid? StateProvinceId { get; set; }
    public StateProvinceDTO? StateProvince { get; set; }
    public Guid? DistrictId { get; set; }
    public DistrictDTO? District { get; set; }
    public Guid? CityId { get; set; }
    public CityDTO? City { get; set; }
    public bool IsDeleted { get; set; }
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public Guid? StationId { get; set; }
    public StationDTO? Station { get; set; }
    public string? Color { get; set; }
    public List<string>? IsAuditPersonList { get; set; }

    //public List<TransactionRequestDTO>? TransactionRequest { get; set; }
    //public List<BuildingUserDTO>? BuildingUser { get; set; }
}