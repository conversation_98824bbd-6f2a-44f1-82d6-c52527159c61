using Domain.Shared;

namespace Application.Companies.DTOs;

public class CompanyDTO : BaseDTO
{
    public Guid Id { get; set; }
    public bool Active { get; set; }
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? TaxOffice { get; set; }
    public string? TaxNumber { get; set; }
    public string? Address { get; set; }
    public string? Description { get; set; }
    public string? AccountingCode { get; set; }
    public bool IsDeleted { get; set; }
    public List<BuildingUserDTO>? CompanyUser { get; set; }
}