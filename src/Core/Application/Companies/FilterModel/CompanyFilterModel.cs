using Domain.Shared;

namespace Application.Companies.FilterModel;

public class CompanyFilterModel : BaseFilterModel
{
    public Guid[]? Ids { get; set; }
    public bool? Active { get; set; }
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? TaxOffice { get; set; }
    public string? TaxNumber { get; set; }
    public string? Address { get; set; }
    public string? Description { get; set; }
}