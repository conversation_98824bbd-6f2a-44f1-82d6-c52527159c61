using Domain.Shared;

namespace Application.Companies.FilterModel;

public class BuildingFilterModel : BaseFilterModel
{
    public Guid[]? Ids { get; set; }
    public string? Name { get; set; }
    public Guid? CompanyId { get; set; }
    public Guid? StationId { get; set; }
    public Guid? StateProvinceId { get; set; }
    public Guid? DistrictId { get; set; }
    public Guid? CityId { get; set; }
    public bool? Status { get; set; }

}