using Application.Companies.DTOs;
using AutoMapper;
using Domain.Companies;

namespace Application.Companies.Mapping;

public class CompanyMapping : Profile
{
    public CompanyMapping()
    {
        CreateMap<Building, BuildingDTO>()
        .ForMember(dest => dest.IsAuditPersonList,
            opt => opt.MapFrom(src =>
                (src.BuildingUser ?? new List<BuildingUser>())
                    .Where(bu => bu.IsAuditPerson && bu.User != null)
                    .Select(bu => (bu.User.Name ?? "") + " " + (bu.User.Surname ?? ""))
                    .ToList()))
        .ReverseMap();

        CreateMap<Company, CompanyDTO>().ReverseMap();
        CreateMap<BuildingUser, BuildingUserDTO>().ReverseMap();
    }
} 