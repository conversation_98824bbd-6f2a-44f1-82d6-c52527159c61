using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Domain.Shared;
using Domain.Catalog;
using Application.Catalog.DTOs;
using Application.Catalog.FilterModel;
using Application.Shared.Wrappers;
using Application.Shared.Data;

namespace Application.Catalog.Services;

public class ProductService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<Product, ProductDTO, Guid>(mapper, dbContext), IProductService
{
    public async Task<PagedResponse<ProductDTO>> FilterAsync(ProductFilterModel filter)
    {
        var allData = _dbContext.Set<Product>().AsQueryable().Where(x => !x.IsDeleted);
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        }
        if (filter.ProductIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.ProductIds.Contains(x.Id));
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();

        // Order alanına göre özel sıralama
        if (!string.IsNullOrWhiteSpace(filter.SortProperty) && filter.SortProperty.Equals("Order", StringComparison.OrdinalIgnoreCase))
        {
            bool isAscending = string.IsNullOrWhiteSpace(filter.SortType) || filter.SortType.Equals("asc", StringComparison.OrdinalIgnoreCase);

            if (isAscending)
            {
                // Order değeri olan ürünleri önce Order'a göre sırala, sonra Order değeri null olanları Name'e göre sırala
                filteredData = filteredData
                    .OrderBy(x => x.Order.HasValue ? 0 : 1) // Order değeri olanlar önce
                    .ThenBy(x => x.Order) // Order değerine göre sırala
                    .ThenBy(x => x.Name); // Order değeri aynı olanları Name'e göre sırala
            }
            else
            {
                // Descending sıralama
                filteredData = filteredData
                    .OrderBy(x => x.Order.HasValue ? 0 : 1) // Order değeri olanlar önce
                    .ThenByDescending(x => x.Order) // Order değerine göre ters sırala
                    .ThenBy(x => x.Name); // Order değeri aynı olanları Name'e göre sırala
            }
        }
        else
        {
            // Diğer alanlar için normal sıralama
            filteredData = filteredData.Sorting(filter.SortProperty, filter.SortType);
        }

        var list = await filteredData
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<ProductDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    //Delete
    public override async Task DeleteAsync(Guid Id)
    {
        var entity = await _dbContext.Product.FindAsync(Id);
        if (entity == null)
            throw new Exception("Ürün bulunamadı.");

        entity.IsDeleted = true;
        _dbContext.Product.Update(entity);
        await _dbContext.SaveChangesAsync();
    }
}