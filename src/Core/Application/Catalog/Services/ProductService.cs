using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Domain.Shared;
using Domain.Catalog;
using Application.Catalog.DTOs;
using Application.Catalog.FilterModel;
using Application.Shared.Wrappers;
using Application.Shared.Data;

namespace Application.Catalog.Services;

public class ProductService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<Product, ProductDTO, Guid>(mapper, dbContext), IProductService
{
    public async Task<PagedResponse<ProductDTO>> FilterAsync(ProductFilterModel filter)
    {
        var allData = _dbContext.Set<Product>().AsQueryable().Where(x => !x.IsDeleted);
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        }
        if (filter.ProductIds?.Length > 0)
        {
            filteredData = filteredData.Where(x => filter.ProductIds.Contains(x.Id));
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<ProductDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    //Delete
    public override async Task DeleteAsync(Guid Id)
    {
        var entity = await _dbContext.Product.FindAsync(Id);
        if (entity == null)
            throw new Exception("Ürün bulunamadı.");

        entity.IsDeleted = true;
        _dbContext.Product.Update(entity);
        await _dbContext.SaveChangesAsync();
    }
}