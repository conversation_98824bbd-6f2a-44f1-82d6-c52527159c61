using Application.Catalog.DTOs;
using Application.Shared.Data;
using Application.Shared.Generics;
using AutoMapper;
using Domain.Catalog;
using Domain.Shared;

namespace Application.Catalog.Commands;

public record AddProductCommand(ProductDTO EntityDTO) : AddCommand<ProductDTO, Product>(EntityDTO);
public class AddProductCommandHandler(IMapper mapper, IApplicationDbContext dbContext)
    : AddCommandHandler<AddProductCommand, ProductDTO, Product>(mapper, dbContext);

public record AddProductListCommand(List<ProductDTO> ListEntityDTO) : AddListCommand<ProductDTO, Product>(ListEntityDTO);
public class AddProductListCommandHandler(IMapper mapper, IApplicationDbContext dbContext)
    : AddListCommandHandler<AddProductListCommand, ProductDTO, Product>(mapper, dbContext);

public record EditProductCommand(ProductDTO EntityDTO) : EditCommand<ProductDTO, Product>(EntityDTO);
public class EditProductCommandHandler(IMapper mapper, IApplicationDbContext dbContext)
    : EditCommandHandler<EditProductCommand, ProductDTO, Product>(mapper, dbContext);

public record EditProductListCommand(List<ProductDTO> ListEntityDTO) : EditListCommand<ProductDTO, Product>(ListEntityDTO);
public class EditProductListCommandHandler(IMapper mapper, IApplicationDbContext dbContext)
    : EditListCommandHandler<EditProductListCommand, ProductDTO, Product>(mapper, dbContext);

public record DeleteProductCommand(ProductDTO EntityDTO) : DeleteCommand<ProductDTO, Product>(EntityDTO);
public class DeleteProductCommandHandler(IMapper mapper, IApplicationDbContext dbContext)
    : DeleteCommandHandler<DeleteProductCommand, ProductDTO, Product>(mapper, dbContext);

public record DeleteProductListCommand(List<ProductDTO> ListEntityDTO) : DeleteListCommand<ProductDTO, Product>(ListEntityDTO);
public class DeleteProductListCommandHandler(IMapper mapper, IApplicationDbContext dbContext)
    : DeleteListCommandHandler<DeleteProductListCommand, ProductDTO, Product>(mapper, dbContext);

public record DeleteByIdProductCommand(Guid Id) : DeleteByIdCommand<Guid, Product>(Id);
public class DeleteByIdProductCommandHandler(IApplicationDbContext dbContext)
    : DeleteByIdCommandHandler<DeleteByIdProductCommand, Guid, Product>(dbContext);

public record DeleteByIdProductListCommand(List<Guid> ListEntityDTO) : DeleteByIdListCommand<Guid, Product>(ListEntityDTO);
public class DeleteByIdProductListCommandHandler(IApplicationDbContext dbContext)
    : DeleteByIdListCommandHandler<DeleteByIdProductListCommand, Guid, Product>(dbContext);

public record PatchProductCommand(PatchDTO PatchDTO) : PatchCommand<Product>(PatchDTO);
public class PatchProductCommandHandler(IApplicationDbContext dbContext)
    : PatchCommandHandler<PatchProductCommand, Product>(dbContext);

public record PatchProductListCommand(List<PatchDTO> ListPatchDTO) : PatchListCommand<Product>(ListPatchDTO);
public class PatchProductListCommandHandler(IApplicationDbContext dbContext)
    : PatchListCommandHandler<PatchProductListCommand, Product>(dbContext);

// public class AddEntityCommandValidator<T> : AbstractValidator<AddEntityCommand<T>>
// {
//     public AddEntityCommandValidator()
//     {
//         RuleFor(x => x.Id).NotEmpty();
//     }
// }