using MediatR;
using Application.Shared.Wrappers;
using Domain.External;
using Domain.External.DTOs;
using Application.Shared.Extensions;

namespace Application.Accounting.Queries;

public record GetDocumentDetailQuery(
    string IrsaliyeNo
) : IRequest<Response<List<DocumentDTO>>>;

public class GetDocumentDetailQueryHandler(
    IERPManager erpService)
    : IRequestHandler<GetDocumentDetailQuery, Response<List<DocumentDTO>>>
{
    private readonly IERPManager _erpService = erpService;

    public async Task<Response<List<DocumentDTO>>> Handle(GetDocumentDetailQuery request, CancellationToken cancellationToken)
    {
        var result = await _erpService.GetDocumentDetail(request.IrsaliyeNo);
        return result.ToResponse();
    }
}