using MediatR;
using Application.Shared.Wrappers;
using Domain.External;
using Application.Shared.Extensions;

namespace Application.Accounting.Queries;

public record GetDocumentCheckQuery(
    string IrsaliyeNo
) : IRequest<Response<bool>>;

public class GetDocumentCheckQueryHandler(
    IERPManager erpService)
    : IRequestHandler<GetDocumentCheckQuery, Response<bool>>
{
    private readonly IERPManager _erpService = erpService;

    public async Task<Response<bool>> Handle(GetDocumentCheckQuery request, CancellationToken cancellationToken)
    {
        var result = await _erpService.GetDocumentDetail(request.IrsaliyeNo);
        if (result.Count != 0)
        {
            return true.ToResponse();
        }
        return false.ToResponse();
    }
}