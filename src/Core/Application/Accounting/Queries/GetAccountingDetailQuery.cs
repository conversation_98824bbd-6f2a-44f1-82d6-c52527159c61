using MediatR;
using Application.Shared.Wrappers;
using Domain.External;
using Domain.External.DTOs;
using Application.Shared.Extensions;

namespace Application.Accounting.Queries;

public record GetAccountingDetailQuery(
    Guid CariRecNo
) : IRequest<Response<List<AccountingDetailDTO>>>;

public class GetAccountingDetailQueryHandler(
    IERPManager erpService)
    : IRequestHandler<GetAccountingDetailQuery, Response<List<AccountingDetailDTO>>>
{
    private readonly IERPManager _erpService = erpService;

    public async Task<Response<List<AccountingDetailDTO>>> Handle(GetAccountingDetailQuery request, CancellationToken cancellationToken)
    {
        var result = await _erpService.GetAccountingDetail(request.CariRecNo);
        return result.ToResponse();
    }
}