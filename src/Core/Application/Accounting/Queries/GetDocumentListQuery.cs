using MediatR;
using Application.Shared.Wrappers;
using Domain.External;
using Domain.External.DTOs;
using Application.Shared.Extensions;
using Application.Shared.Data;

namespace Application.Accounting.Queries;

public record GetDocumentListQuery(
    string AccountingCode,
    DateTime StartDate,
    DateTime EndDate
) : IRequest<Response<List<DocumentDTO>>>;

public class GetDocumentListQueryHandler(
    IERPManager erpService,
    IApplicationDbContext dbContext)
    : IRequestHandler<GetDocumentListQuery, Response<List<DocumentDTO>>>
{
    private readonly IERPManager _erpService = erpService;
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<DocumentDTO>>> Handle(GetDocumentListQuery request, CancellationToken cancellationToken)
    {
        var result = await _erpService.GetDocumentList(request.AccountingCode, request.StartDate, request.EndDate);

        // ContractProductTransaction tablosundaki DocumentNo'ları al
        var contractProductDocumentNos = _dbContext.ContractProductTransaction
            .Select(x => x.DocumentNo)
            .ToHashSet();

        // result içinden contractProductDocumentNos içinde olmayanları filtrele
        var filteredResult = result
            .Where(x => !contractProductDocumentNos.Contains(x.IrsaliyeNo))
            .ToList();

        return filteredResult.ToResponse();
    }
}