using MediatR;
using Application.Shared.Wrappers;
using Domain.External;
using Domain.External.DTOs;
using Application.Shared.Extensions;
using Application.Shared.Data;

namespace Application.Accounting.Queries;

public record GetAccountingListQuery(
    string AccountingCode,
    DateTime StartDate,
    DateTime EndDate
) : IRequest<Response<List<AccountingDTO>>>;

public class GetAccountingListQueryHandler(
    IERPManager erpService)
    : IRequestHandler<GetAccountingListQuery, Response<List<AccountingDTO>>>
{
    private readonly IERPManager _erpService = erpService;

    public async Task<Response<List<AccountingDTO>>> Handle(GetAccountingListQuery request, CancellationToken cancellationToken)
    {
        var result = await _erpService.GetAccountingList(request.AccountingCode, request.StartDate, request.EndDate);
        return result.ToResponse();
    }
}