using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Dashboard.DTOs;
using Domain.Transactions;
using Application.Shared.Data;
using Microsoft.EntityFrameworkCore;

namespace Application.Dashboard.Query;

public record GetTotalContractQuery(
    Guid? CompanyId
) : IRequest<Response<TotalContractDTO>>;

public class GetTotalContractQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetTotalContractQuery, Response<TotalContractDTO>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<TotalContractDTO>> Handle(GetTotalContractQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.Contract.AsQueryable();
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.CompanyId == request.CompanyId);
        }
        var contractProducts = await data
        .SelectMany(x => x.ContractProduct)
        .ToListAsync(cancellationToken);

        var totalAmount = contractProducts.Sum(x => x.Amount ?? 0);
        var leftAmount = contractProducts.Sum(x => x.LeftAmount ?? 0);
        var completedAmount = totalAmount - leftAmount;

        var result = new TotalContractDTO
        {
            TotalAmount = totalAmount,
            CompletedAmount = completedAmount,
            RemainingAmount = leftAmount,
        };
        
        return result.ToResponse();
    }
}