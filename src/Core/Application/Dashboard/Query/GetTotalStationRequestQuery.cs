using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Dashboard.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Dashboard.Query;

public record GetTotalStationRequestQuery(
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate
) : IRequest<Response<List<TotalStationRequestDTO>>>;

public class GetTotalStationRequestQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetTotalStationRequestQuery, Response<List<TotalStationRequestDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<TotalStationRequestDTO>>> Handle(GetTotalStationRequestQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.TransactionRequest.AsQueryable();
        if (request.ApprovedStartDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.AddDays(1).Date);
        }
        var result = await data
            .GroupBy(x => x.StationId)
            .Select(x => new TotalStationRequestDTO
            {
                Station = x.FirstOrDefault().Station.Name,
                StationColor = x.FirstOrDefault().Station.Color,
                TotalRequest = x.Count(),
                TotalConcrete = x.Sum(x => x.DesiredTotalConcrete)
            }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}