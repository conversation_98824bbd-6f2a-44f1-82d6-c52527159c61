using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Dashboard.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Dashboard.Query;

public record GetTotalProductQuery(
    Guid? BuildingId,
    Guid? CompanyId,
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate
) : IRequest<Response<List<TotalProductDTO>>>;

public class GetTotalProductQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetTotalProductQuery, Response<List<TotalProductDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<TotalProductDTO>>> Handle(GetTotalProductQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.TransactionRequest.AsQueryable();
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.BuildingId == request.BuildingId);
        }
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.Building.CompanyId == request.CompanyId);
        }
        if (request.ApprovedStartDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.Date);
        }
        var result = await data
        .GroupBy(x => x.ProductId)
        .Select(x => new TotalProductDTO
        {
            ProductName = x.FirstOrDefault().Product.Name,
            ProductId = x.FirstOrDefault().Product.Id,
            ProductColor = x.FirstOrDefault().Product.Color,
            Total = x.Sum(x => x.DesiredTotalConcrete)
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}