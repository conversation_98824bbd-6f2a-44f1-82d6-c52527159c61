using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Dashboard.DTOs;
using Domain.Transactions;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Dashboard.Query;

public record GetDailyTotalRequestQuery(
    Guid? CompanyId,
    Guid? BuildingId,
    DateTime? StartDate,
    DateTime? EndDate
) : IRequest<Response<List<DailyTotalRequestDTO>>>;

public class GetDailyTotalRequestQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetDailyTotalRequestQuery, Response<List<DailyTotalRequestDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<DailyTotalRequestDTO>>> Handle(GetDailyTotalRequestQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.TransactionRequest.AsQueryable();
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.Building.CompanyId == request.CompanyId);
        }
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.BuildingId == request.BuildingId);
        }
        if (request.StartDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date >= request.StartDate.Value.Date);
        }
        if (request.EndDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date <= request.EndDate.Value.Date);
        }
        var result = await data
            .OrderBy(x => x.ApprovedDateTime)
            .GroupBy(x => x.ApprovedDateTime.Value.Date)
            .Select(x => new DailyTotalRequestDTO
            {
                Date = x.Key.Date,
                Total = x.Sum(y => y.TotalConcreteSent)
            }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}