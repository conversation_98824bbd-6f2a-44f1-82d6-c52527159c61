using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Application.Dashboard.DTOs;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Data;

namespace Application.Dashboard.Query;

public record GetTotalCompanyRequestQuery(
    Guid? BuildingId,
    Guid? CompanyId,
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate
) : IRequest<Response<List<TotalCompanyRequestDTO>>>;

public class GetTotalCompanyRequestQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetTotalCompanyRequestQuery, Response<List<TotalCompanyRequestDTO>>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<TotalCompanyRequestDTO>>> Handle(GetTotalCompanyRequestQuery request, CancellationToken cancellationToken)
    {
        var data = _dbContext.TransactionRequest.AsQueryable();
        if (request.BuildingId.HasValue)
        {
            data = data.Where(x => x.BuildingId == request.BuildingId);
        }
        if (request.CompanyId.HasValue)
        {
            data = data.Where(x => x.Building.CompanyId == request.CompanyId);
        }
        if (request.ApprovedStartDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            data = data.Where(x => x.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.Date);
        }
        var result = await data
        .GroupBy(x => x.Building.CompanyId)
        .Select(x => new TotalCompanyRequestDTO
        {
            Company = x.FirstOrDefault().Building.Company.Name,
            CompanyColor = x.FirstOrDefault().Building.Company.Color,
            Total = x.Sum(x => x.DesiredTotalConcrete)
        }).ToListAsync(cancellationToken: cancellationToken);
        return result.ToResponse();
    }
}