using MediatR;
using FluentValidation;
using Application.Shared.Wrappers;
using Application.Shared.Extensions;
using Domain.Transactions;
using Application.Dashboard.DTOs;
using Application.Shared.Data;
using Microsoft.EntityFrameworkCore;

namespace Application.Dashboard.Query;

public record GetTotalRequestQuery(
    Guid? BuildingId,
    Guid? CompanyId,
    DateTime? ApprovedStartDate,
    DateTime? ApprovedEndDate
) : IRequest<Response<TotalRequestDTO>>;

public class GetTotalRequestQueryHandler(
    IApplicationDbContext dbContext)
    : IRequestHandler<GetTotalRequestQuery, Response<TotalRequestDTO>>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<TotalRequestDTO>> Handle(GetTotalRequestQuery request, CancellationToken cancellationToken)
    {
        var dataApproved = _dbContext.TransactionRequest.AsQueryable();
        var dataDesired = _dbContext.TransactionRequest.AsQueryable();
        if (request.BuildingId.HasValue)
        {
            dataApproved = dataApproved.Where(x => x.BuildingId == request.BuildingId);
            dataDesired = dataDesired.Where(x => x.BuildingId == request.BuildingId);
        }
        if (request.CompanyId.HasValue)
        {
            dataApproved = dataApproved.Where(x => x.Building.CompanyId == request.CompanyId);
            dataDesired = dataDesired.Where(x => x.Building.CompanyId == request.CompanyId);
        }
        if (request.ApprovedStartDate.HasValue)
        {
            dataApproved = dataApproved.Where(x => x.ApprovedDateTime.Value.Date >= request.ApprovedStartDate.Value.Date);
            dataDesired = dataDesired.Where(x => x.DesiredDateTime.Date >= request.ApprovedStartDate.Value.Date);
        }
        if (request.ApprovedEndDate.HasValue)
        {
            dataApproved = dataApproved.Where(x => x.ApprovedDateTime.Value.Date <= request.ApprovedEndDate.Value.Date);
            dataDesired = dataDesired.Where(x => x.DesiredDateTime.Date <= request.ApprovedEndDate.Value.Date);
        }
        var approvedIds = await dataApproved.Select(x => x.Id).ToListAsync(cancellationToken: cancellationToken);
        var totalDesired = dataDesired.Where(x => !x.ApprovedDateTime.HasValue && x.StatusId != 8 && !approvedIds.Contains(x.Id)).Sum(x => x.DesiredTotalConcrete);
        var totalPlanned = dataApproved.Sum(x => x.DesiredTotalConcrete);
        var totalCompleted = dataApproved.Sum(x => x.TotalConcreteSent) ?? 0;
        var result = new TotalRequestDTO
        {
            Total = totalDesired + totalPlanned,
            TotalPlanned = totalPlanned,
            TotalRemaining = totalPlanned - totalCompleted,
            TotalCompleted = totalCompleted,
        };
        return result.ToResponse();
    }
}