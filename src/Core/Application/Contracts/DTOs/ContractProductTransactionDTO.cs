using Application.Account.DTOs;
using Application.Stations.DTOs;
using Application.Transactions.DTOs;
using Domain.Shared;

namespace Application.Contracts.DTOs;

public class ContractProductTransactionDTO : BaseDTO
{
    public Guid Id { get; set; }
    public Guid? ContractId { get; set; }
    public ContractDTO? Contract { get; set; }
    public Guid TransactionRequestId { get; set; }
    //public TransactionRequestDTO? TransactionRequest { get; set; }
    public Guid VehicleId { get; set; }
    public VehicleDTO? Vehicle { get; set; }
    public Guid DriverId { get; set; }
    public UserDTO? Driver { get; set; }
    public DateTime InsertDate { get; set; }
    public Guid? InsertUserId { get; set; }
    public int StatusId { get; set; }
    public ContractProductTransactionStatusDTO? Status { get; set; }
    public decimal SendingAmount { get; set; }
    public string? DocumentNo { get; set; }
    public Guid? StationId { get; set; }
    public StationDTO? Station { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int TypeId { get; set; }
    public TransactionTypeDTO? Type { get; set; }
    public Guid? CanceledUserId { get; set; }
    public UserDTO? CanceledUser { get; set; }
    public string? CanceledNote { get; set; }
    public decimal? RefundAmount { get; set; }
    public int? PompTypeId { get; set; }
    public PompTypeDTO? PompType { get; set; }
    public TimeSpan? TotalTime { get; set; }
}