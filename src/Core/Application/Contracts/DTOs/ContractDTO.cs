using Application.Account.DTOs;
using Application.Companies.DTOs;
using Domain.Shared;
using Microsoft.AspNetCore.Http;

namespace Application.Contracts.DTOs;

public class ContractDTO : BaseDTO
{
    public Guid Id { get; set; }
    public Guid CompanyId { get; set; }
    public CompanyDTO? Company { get; set; }
    public bool Active { get; set; }
    public required string Title { get; set; }
    public string? Description { get; set; }
    public DateTime InsertDate { get; set; }
    public Guid InsertUserId { get; set; }
    public DateTime? UpdateDate { get; set; }
    public Guid? UpdatedUserId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? PaymentPlanId { get; set; }
    public PaymentPlanDTO? PaymentPlan { get; set; }
    public bool? IsApproved { get; set; } = true;
    public DateTime? ApprovedDateTime { get; set; }
    public Guid? ApprovedUserId { get; set; }
    public UserDTO? ApprovedUser { get; set; }
    public string? ApprovedNote { get; set; }
    public decimal? TotalWorth { get; set; }
    public bool? IncludeTax { get; set; }
    public decimal? LeftWorth { get; set; }
    public decimal? OldWorth { get; set; }
    public decimal? TotalAmount { get; set; }
    public decimal? LeftAmount { get; set; }
    public decimal? OldAmount { get; set; }
    public List<ContractProductDTO>? ContractProduct { get; set; }
    public List<ContractFileDTO>? ContractFile { get; set; }
}

public class AddContractDTO : BaseDTO
{
    public Guid Id { get; set; }
    public Guid CompanyId { get; set; }
    public bool Active { get; set; }
    public required string Title { get; set; }
    public required string Description { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public IFormFile? File { get; set; }
    public int PaymentPlanId { get; set; }
    public PaymentPlanDTO? PaymentPlan { get; set; }
    public List<ContractProductDTO>? ContractProduct { get; set; }
}