using Application.Catalog.DTOs;
using Application.Transactions.DTOs;
using Domain.Shared;

namespace Application.Contracts.DTOs;

public class ContractProductDTO : BaseDTO
{
    public Guid Id { get; set; }
    public Guid ContractId { get; set; }
    public Guid ProductId { get; set; }
    public ProductDTO? Product { get; set; }
    public decimal? Amount { get; set; }
    public decimal? OldAmount { get; set; }
    public decimal? LeftAmount { get; set; }
    public decimal? TotalPrice { get; set; } 
    public decimal? OldPrice { get; set; }
    public decimal? LeftPrice { get; set; } 
    public decimal Price { get; set; }
    public decimal? PriceWithoutPlug { get; set; }
    public decimal? PriceUnderPowerPlant { get; set; }
    public bool? IncludeTax { get; set; }
    public DateTime? PriceGuaranteeDate { get; set; }
    public List<ContractProductConsistencyClassDTO>? ContractProductConsistencyClass { get; set; }
    public List<ContractProductConcreteOptionDTO>? ContractProductConcreteOption { get; set; }
    public List<ContractProductBuildingDTO>? ContractProductBuilding { get; set; }
}