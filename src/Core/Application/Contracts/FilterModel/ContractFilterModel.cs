using Domain.Shared;

namespace Application.Contracts.FilterModel;

public class ContractFilterModel : BaseFilterModel
{
    public Guid? CompanyId { get; set; }
    public bool? Active { get; set; }
    public string? Title { get; set; }
    public string? Description { get; set; }
    public int? IsApproved { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
}