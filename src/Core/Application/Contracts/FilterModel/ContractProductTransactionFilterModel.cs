using Domain.Shared;

namespace Application.Contracts.FilterModel;

public class ContractProductTransactionFilterModel : BaseFilterModel
{
    public Guid? ContractId { get; set; }
    public Guid? TransactionRequestId { get; set; }
    public Guid? VehicleId { get; set; }
    public Guid? DriverId { get; set; }
    public Guid? CompanyId { get; set; }
    public Guid? StationId { get; set; }
    public Guid? BuildingId { get; set; }
    public Guid? ProductId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? StatusId { get; set; }
    public int? TypeId { get; set; }
    public string? DocumentNo { get; set; }
}