using Application.Shared.Data;
using Application.Shared.Extensions;
using Domain.Contracts;
using Domain.Transactions;
using MediatR;
using Microsoft.EntityFrameworkCore;

namespace Application.Contracts.Notifications;

public class ContractProductTransactionAddOrUpdateNotification(
    IApplicationDbContext dbContext)
    : INotificationHandler<ContractProductTransactionAddOrUpdateEvent>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task Handle(ContractProductTransactionAddOrUpdateEvent notification, CancellationToken cancellationToken)
    {
        var contractProductTransaction = await _dbContext
            .ContractProductTransaction
            .Including("TransactionRequest.ContractProductTransaction")
            .FirstOrDefaultAsync(x => x.Id == notification.ContractProductTransactionId, cancellationToken: cancellationToken);
        if (contractProductTransaction?.TransactionRequest == null)
        {
            return;
        }
        else
        {
            var transactionRequest = contractProductTransaction.TransactionRequest;
            transactionRequest.TotalConcreteRefundable = transactionRequest
                ?.ContractProductTransaction
                ?.Where(x => x.TypeId == (int)TransactionTypeEnum.Return &&
                x.StatusId == (int)ContractProductTransactionStatusEnum.Finished)
                .Sum(x => x.SendingAmount);
            transactionRequest.TotalConcreteSent = transactionRequest
                ?.ContractProductTransaction
                ?.Where(x => x.TypeId == (int)TransactionTypeEnum.Normal &&
                x.StatusId == (int)ContractProductTransactionStatusEnum.Finished)
                .Sum(x => x.SendingAmount);
            transactionRequest.TotalConcreteRemaining = transactionRequest.DesiredTotalConcrete - transactionRequest.TotalConcreteSent;
            if (transactionRequest.TotalConcreteSent >= transactionRequest.DesiredTotalConcrete)
            {
                transactionRequest.StatusId = (int)TransactionStatusEnum.Finished;
                transactionRequest.TransactionEndDateTime = DateTime.Now;
            }
            _dbContext.TransactionRequest.Update(transactionRequest);
        }
        var contract = await _dbContext
            .Contract
            .Including("TransactionRequest.ContractProductTransaction", "ContractProduct")
            .FirstOrDefaultAsync(x => x.Id == contractProductTransaction.TransactionRequest.ContractId, cancellationToken: cancellationToken);
        if (contract?.TransactionRequest?.Count > 0)
        {
            foreach (var transactionRequest in contract.TransactionRequest)
            {
                if (transactionRequest != null)
                {
                    transactionRequest.TotalConcreteRefundable = transactionRequest
                        ?.ContractProductTransaction
                        ?.Where(x => x.TypeId == (int)TransactionTypeEnum.Return &&
                        x.StatusId == (int)ContractProductTransactionStatusEnum.Finished)
                        .Sum(x => x.SendingAmount);
                    transactionRequest.TotalConcreteSent = transactionRequest
                        ?.ContractProductTransaction
                        ?.Where(x => x.TypeId == (int)TransactionTypeEnum.Normal &&
                        x.StatusId == (int)ContractProductTransactionStatusEnum.Finished)
                        .Sum(x => x.SendingAmount);
                    transactionRequest.TotalConcreteRemaining = transactionRequest.DesiredTotalConcrete - transactionRequest.TotalConcreteSent;
                    if (transactionRequest.TotalConcreteSent >= transactionRequest.DesiredTotalConcrete)
                    {
                        transactionRequest.StatusId = (int)ContractProductTransactionStatusEnum.Finished;
                        transactionRequest.TransactionEndDateTime = DateTime.Now;
                    }
                    _dbContext.TransactionRequest.Update(transactionRequest);
                }
            }
        }
        if (contract?.ContractProduct?.Count > 0)
        {
            foreach (var contractProduct in contract.ContractProduct)
            {
                // Fiyat tipine göre uygun fiyatı seç, yoksa varsayılan Price'ı kullan
                var unitPrice = contractProductTransaction?.TransactionRequest.PriceTypeId switch
                {
                    1 => contractProduct.Price, // Fişli
                    2 => contractProduct.PriceWithoutPlug ?? contractProduct.Price, // Fişsiz (yoksa Price)
                    3 => contractProduct.PriceUnderPowerPlant ?? contractProduct.Price, // Bantaltı (yoksa Price)
                    _ => contractProduct.Price // Varsayılan
                };

                var usageAmount = contract
                    ?.TransactionRequest
                    ?.Where(x => x.ProductId == contractProduct.ProductId)
                    .Sum(x => x.TotalConcreteSent) ?? 0;

                //Miktar
                contractProduct.LeftAmount = contractProduct.Amount - usageAmount;
                contractProduct.OldAmount = contractProduct.Amount - contractProduct.LeftAmount;

                //Tutar
                contractProduct.LeftPrice = contractProduct.LeftAmount * unitPrice;
                contractProduct.OldPrice = contractProduct.OldAmount * unitPrice;
                _dbContext.ContractProduct.Update(contractProduct);

                // Contract'ın kalan ve kullanılan tutarını güncelle
                var findContract = await _dbContext
                    .Contract
                    .FirstOrDefaultAsync(x => x.Id == contractProduct.ContractId, cancellationToken: cancellationToken);
                
                if (findContract != null)
                {
                    //Miktar
                    findContract.OldWorth += usageAmount * unitPrice;
                    findContract.LeftWorth = findContract.TotalWorth - findContract.OldWorth;

                    //Tutar
                    findContract.OldAmount += usageAmount;
                    findContract.LeftAmount = findContract.TotalAmount - findContract.OldAmount;
                    _dbContext.Contract.Update(findContract);
                }
            }
        }
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}