using Application.Contracts.DTOs;
using AutoMapper;
using Domain.Contracts;

namespace Application.Contracts.Mapping;

public class ContractMapping : Profile
{
    public ContractMapping()
    {
        CreateMap<Contract, ContractDTO>().ReverseMap();
        CreateMap<AddContractDTO, ContractDTO>();
        CreateMap<ContractProduct, ContractProductDTO>().ReverseMap();
        CreateMap<ContractProductBuilding, ContractProductBuildingDTO>().ReverseMap();
        CreateMap<ContractProductConsistencyClass, ContractProductConsistencyClassDTO>().ReverseMap();
        CreateMap<ContractProductConcreteOption, ContractProductConcreteOptionDTO>().ReverseMap();
        CreateMap<ContractFile, ContractFileDTO>().ReverseMap();
        CreateMap<ContractProductTransaction, ContractProductTransactionDTO>().ReverseMap();
        CreateMap<ContractProductTransactionStatus, ContractProductTransactionStatusDTO>().ReverseMap();
        CreateMap<ContractProductTransactionLog, ContractProductTransactionLogDTO>().ReverseMap();
        CreateMap<TransactionType, TransactionTypeDTO>().ReverseMap();
        CreateMap<PaymentPlan, PaymentPlanDTO>().ReverseMap();
    }
}