using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Contracts;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;
using Application.Shared.Data;
using Domain.Shared;
using Domain.External;
using Domain.Account;
using Application.General.Services;
using Application.General.DTOs;
using Application.Shared;
using Application.Companies.DTOs;
using Domain.Companies;
using Application.Catalog.DTOs;
using Domain.Catalog;
using Application.Account.DTOs;
using Domain.Transactions;
using Application.Transactions.DTOs;
using Application.Account.Services;

namespace Application.Contracts.Services;

public class ContractService(
    IMapper mapper,
    AppSettings appSettings,
    IEmailManager emailManager,
    ISMSManager smsManager,
    INotificationService notificationService,
    IUserService userService,
    IWorkContext workContext,
    IApplicationDbContext dbContext)
    : BaseService<Contract, ContractDTO, Guid>(mapper, dbContext), IContractService
{
    private readonly AppSettings _appSettings = appSettings;
    private readonly IEmailManager _emailManager = emailManager;
    private readonly ISMSManager _smsManager = smsManager;
    private readonly INotificationService _notificationService = notificationService;
    private readonly IUserService _userService = userService;
    private readonly IWorkContext _workContext = workContext;

    public async Task<PagedResponse<ContractDTO>> FilterAsync(ContractFilterModel filter)
    {
        var allData = _dbContext.Set<Contract>().AsQueryable().Where(x => !x.IsDeleted);
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Title))
        {
            filteredData = filteredData.Where(x => x.Title.Contains(filter.Title));
        }
        if (!string.IsNullOrWhiteSpace(filter.Description))
        {
            filteredData = filteredData.Where(x => x.Description.Contains(filter.Description));
        }
        if (filter.Active.HasValue)
        {
            filteredData = filteredData.Where(x => x.Active == filter.Active);
        }
        if (filter.CompanyId.HasValue)
        {
            filteredData = filteredData.Where(x => x.CompanyId == filter.CompanyId);
        }
        if (filter.IsApproved.HasValue)
        {
            if (filter.IsApproved == 0)
            {
                filteredData = filteredData.Where(x => x.IsApproved == null);
            }
            else if (filter.IsApproved == 1)
            {
                filteredData = filteredData.Where(x => x.IsApproved == true);
            }
            else if (filter.IsApproved == 2)
            {
                filteredData = filteredData.Where(x => x.IsApproved == false);
            }
        }
        if (filter.StartDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.StartDate >= filter.StartDate);
        }

        if (filter.EndDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.EndDate <= filter.EndDate);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<ContractDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public Task<List<PaymentPlanDTO>> GetPaymentPlanAsync()
    {
        return _dbContext
            .PaymentPlan
            .Select(x => new PaymentPlanDTO
            {
                Id = x.Id,
                Name = x.Name
            }).ToListAsync();
    }

    public async Task SendWithEmail(Guid contractId)
    {
        var contract = await _dbContext
            .Contract
            .Including("ContractProduct.Product", "Company", "ContractFile", "PaymentPlan")
            .FirstOrDefaultAsync(x => x.Id == contractId)
            ?? throw new AppException("Sözleme bulunamadı");
        var emails = await _dbContext
            .Users
            .Where(x => x.CompanyId == contract.CompanyId && x.IsMaster && x.Email != null)
            .Select(x => x.Email)
            .ToListAsync();
        var files = new List<string>();
        if (contract.ContractFile?.Count > 0)
        {
            files = contract
                .ContractFile
                .Select(x => x.FileName)
                .Where(x => !string.IsNullOrWhiteSpace(x))
                .Select(x => Directory.GetCurrentDirectory() + "/Uploads/ContractFile/" + x)
                .ToList();
        }
        foreach (var email in emails)
        {
            if (string.IsNullOrWhiteSpace(email))
            {
                continue;
            }
            var message = GetContractDetailString(contract);
            await _emailManager.SendEmailAsync(email, contract.Title, message, files, true);
        }
    }

    public override async Task<Response<ContractDTO>> InsertAsync(ContractDTO entityVM)
    {
        var user = await _workContext.GetUserAsync();
        entityVM.IsApproved = null;
        entityVM.ApprovedDateTime = null;
        entityVM.ApprovedUserId = null;
        entityVM.ApprovedNote = null;
        entityVM.InsertDate = DateTime.Now;
        entityVM.InsertUserId = user.Id;
        entityVM.UpdateDate = DateTime.Now;
        entityVM.UpdatedUserId = user.Id;
        entityVM.Description = _appSettings.ContractBaseDescription;
        return await base.InsertAsync(entityVM);
    }

    public override async Task<PagedResponse<ContractDTO>> InsertListAysnc(List<ContractDTO> entityListVM)
    {
        var user = await _workContext.GetUserAsync();
        foreach (var entityVM in entityListVM)
        {
            entityVM.IsApproved = null;
            entityVM.ApprovedDateTime = null;
            entityVM.ApprovedUserId = null;
            entityVM.ApprovedNote = null;
            entityVM.InsertDate = DateTime.Now;
            entityVM.InsertUserId = user.Id;
            entityVM.UpdateDate = DateTime.Now;
            entityVM.UpdatedUserId = user.Id;
        }
        return await base.InsertListAysnc(entityListVM);
    }

    public override async Task<Response<ContractDTO>> UpdateAsync(ContractDTO entityVM)
    {
        var user = await _workContext.GetUserAsync();
        var entity = _dbSet
            .AsQueryable()
            .Including("ContractProduct.Product", "Company", "ContractFile", "PaymentPlan")
            .FirstOrDefault(x => x.Id == entityVM.Id)
            ?? throw new Exception("Kayıt bulunamadı");
        entity.Active = entityVM.Active;
        entity.CompanyId = entityVM.CompanyId;
        entity.Title = entityVM.Title;
        entity.Description = entityVM.Description;
        entity.StartDate = entityVM.StartDate;
        entity.EndDate = entityVM.EndDate;
        entity.PaymentPlanId = entityVM.PaymentPlanId;
        entity.UpdateDate = DateTime.Now;
        entity.UpdatedUserId = user.Id;

        var contractProductPriceSum = entity.ContractProduct?.Sum(x => x.TotalPrice);
        var contractProductLeftPriceSum = entity.ContractProduct?.Sum(x => x.LeftPrice);
        var contractProductOldPriceSum = entity.ContractProduct?.Sum(x => x.OldPrice);

        var contractProductAmountSum = entity.ContractProduct?.Sum(x => x.Amount);
        var contractProductLeftAmountSum = entity.ContractProduct?.Sum(x => x.LeftAmount);
        var contractProductOldAmountSum = entity.ContractProduct?.Sum(x => x.OldAmount);

        if (entityVM.TotalWorth.HasValue)
        {
            entity.TotalWorth = entityVM.TotalWorth;
            entity.OldWorth = contractProductOldPriceSum;
            entity.LeftWorth = entityVM.TotalWorth - contractProductOldPriceSum;
        }

        if (entityVM.TotalAmount.HasValue)
        {
            entity.TotalAmount = entityVM.TotalAmount;
            entity.OldAmount = contractProductOldAmountSum;
            entity.LeftAmount = entityVM.TotalAmount - contractProductOldAmountSum;
        }

        entity.IncludeTax = entityVM.IncludeTax;


        // Sözleşmeye bağlı ürün kontrolü - ürün yoksa mail gönderimi yapılmaz
        var hasProducts = entity.ContractProduct?.Count > 0;

        if (entity.PaymentPlanId == (int)PaymentPlanEnum.Month30Days && contractProductPriceSum >= _appSettings.ContractApproveLimit)
        {
            entity.IsApproved = null;
            entity.ApprovedNote = "Seçilen ödeme planı 30 gün olduğu ve sözleşme tutarı limiti aştığı için onay bekleniyor.";
            var title = "Sözleşme Onayı";
            var note = "Sözleşme Onay bekliyor: " + entity.Title;
            if (hasProducts)
            {
                await SendNotificationAndEmail(entity, title, note);    
            }
        }
        else
        {
            if (!entity.IsApproved.HasValue)
            {
                entity.IsApproved = true;
                entity.ApprovedNote = "Otomatik onaylandı";
                var title = "Yeni Sözleşme";
                var note = "Yeni bir sözleşme oluşturuldu: " + entity.Title;
                if (hasProducts)
                {
                    await SendNotificationAndEmail(entity, title, note);
                }
            }
        }
        
        var local = _dbSet.Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<ContractDTO>(_mapper);
        return data.ToResponse();
    }

    private async Task SendNotificationAndEmail(Contract entity, string title, string note)
    {
        //Süper adminler
        var superadmins = await _dbContext.Users.AsQueryable().Where(x => x.UserRole.Any(x => x.RoleId == Role.SUPERADMIN)).ToListAsync();
        //Firma yetkilileri
        var companyUserIds = await _dbContext.Users.AsQueryable().Where(x => x.CompanyId == entity.CompanyId && x.IsMaster).ToListAsync();
        
        var users = companyUserIds.Concat(superadmins).Distinct();
        foreach (var user in users)
        {
            // Notification gönderimi
            try
            {
                await _notificationService.InsertAsync(new NotificationDTO
                {
                    Title = title,
                    Note = note,
                    Type = "Contract",
                    Data = entity.Id.ToString(),
                    UserId = user.Id
                });
            }
            catch (Exception)
            {
            }

            // Email gönderimi
            try
            {
                var emailnote = note + "<br><br>" + GetContractDetailString(entity);
                if (string.IsNullOrWhiteSpace(user.Email))
                {
                    continue;
                }
                await _emailManager.SendEmailAsync(user.Email, title, emailnote, null, true);
            }
            catch (Exception)
            {
            }
        }
    }

    private string GetContractDetailString(Contract entity)
    {
        var body = string.Empty;
        body += "<b>Başlık</b> : " + entity.Title + "</br>";
        body += "<b>Firma</b> : " + entity.Company?.Name + "</br>";
        body += "<b>Sözleşme Tarih</b> : "
        + entity.StartDate?.ToString("dd.MM.yyyy") + " / "
        + entity.EndDate?.ToString("dd.MM.yyyy") + "</br>";
        body += "<b>Açıklama</b> : " + entity.Description + "</br>";
        body += "<b>Ödeme Metodu</b> : " + entity.PaymentPlan?.Name + "</br>";
        body += "<br><b>Ürünler</b> : </br>";
        body += "<table style=\"width:100%;border:1px solid #ddd\">";
        body += "<thead><tr>";
        body += "<td><b>Ürün Adı</b></td><td><b>Birim Fiyat</b></td><td><b>Miktar</b></td><td><b>Toplam</b></td><td><b>Garanti Tarihi</b></td>";
        body += "</tr></thead>";
        foreach (var product in entity.ContractProduct)
        {
            body += "<tr>";
            body += "<td>" + product?.Product?.Name + "</td>";
            body += "<td>" + product?.Price.ToString("0.##") + "</td>";
            body += "<td>" + product?.Amount.Value.ToString("0.##") + "</td>";
            body += "<td>" + (product?.Amount ?? 0 * product?.Price)?.ToString("0.##") + "</td>";
            if (product?.PriceGuaranteeDate.HasValue == true)
            {
                body += "<td>" + product.PriceGuaranteeDate.Value.ToString("dd.MM.yyyy") + "</td>";
            }
            else
            {
                body += "<td></td>";
            }
            body += "<tr>";
        }
        body += "</table>";
        return body;
    }

    public override async Task UpdateListAsync(List<ContractDTO> entityListVM)
    {
        foreach (var entityVM in entityListVM)
        {
            await UpdateAsync(entityVM);
        }
    }

    public async Task Approve(Guid ContractId, string Note)
    {
        var user = await _workContext.GetUserAsync();
        var contract = await _dbContext.Contract.FindAsync(ContractId) ?? throw new Exception("Sözleşme bulunamadı");
        contract.IsApproved = true;
        contract.ApprovedDateTime = DateTime.Now;
        contract.ApprovedUserId = user.Id;
        contract.ApprovedNote = Note;
        await _dbContext.SaveChangesAsync();
    }

    public async Task Reject(Guid ContractId, string Note)
    {
        var user = await _workContext.GetUserAsync();
        var contract = await _dbContext.Contract.FindAsync(ContractId) ?? throw new Exception("Sözleşme bulunamadı");
        contract.IsApproved = false;
        contract.ApprovedDateTime = DateTime.Now;
        contract.ApprovedUserId = user.Id;
        contract.ApprovedNote = Note;
        await _dbContext.SaveChangesAsync();
    }

    public override async Task DeleteAsync(Guid id)
    {
        var entity = await _dbSet.FindAsync(id)
            ?? throw new Exception("Kayıt bulunamadı");
        entity.IsDeleted = true;
        _ = _dbSet.Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public override async Task DeleteAsync(ContractDTO entityVM)
    {
        await DeleteAsync(entityVM.Id);
    }

    public override async Task DeleteListAsync(List<ContractDTO> entityListVM)
    {
        foreach (var entityVM in entityListVM)
        {
            await DeleteAsync(entityVM.Id);
        }
    }

    public override async Task DeleteListAsync(List<Guid> ids)
    {
        foreach (var id in ids)
        {
            await DeleteAsync(id);
        }
    }

    public async Task SendRemindMessageAsync()
    {
        var today = DateTime.UtcNow;
        var sevenDaysLater = today.AddDays(7);

        var contracts = await _dbContext.Contract
            .Where(c => !c.IsDeleted && c.Active && c.EndDate >= today) // Tarihi dolmamış aktif sözleşmeler
            .Include(c => c.Company)
            .Include(c => c.ContractProduct!)
                .ThenInclude(cp => cp.Product)
            .ToListAsync();
            
        // Sistemdeki tüm SUPERADMIN kullanıcıları
        var superAdmins = await _dbContext.Users
            .Include(u => u.UserRole)
            .Where(u => u.Active &&
                        !u.IsDeleted &&
                        u.UserRole.Any(ur => ur.RoleId == Role.SUPERADMIN))
            .ToListAsync();

        foreach (var contract in contracts)
        {
            var expiringProducts = contract.ContractProduct?
                .Where(cp => cp.PriceGuaranteeDate.HasValue &&
                            cp.PriceGuaranteeDate.Value > today &&
                            cp.PriceGuaranteeDate.Value <= sevenDaysLater)
                .ToList();

            if (expiringProducts == null || !expiringProducts.Any())
                continue;

            // Firma kullanıcılarını alalım
            var companySalesUsers = await _dbContext.Users
                 .Include(u => u.UserRole)
                 .Where(u => u.CompanyId == contract.CompanyId &&
                             u.Active &&
                             !u.IsDeleted &&
                             u.UserRole.Any(ur => ur.RoleId == Role.SALES))
                 .ToListAsync();

            // Kullanıcı listesini birleştir (aynı kullanıcı iki listede varsa tekrar etmesin)
            var users = companySalesUsers
                .Concat(superAdmins)
                .GroupBy(u => u.Id)
                .Select(g => g.First())
                .ToList();

            if (!users.Any())
                continue;

            // Ürün ve fiyat detaylarını mesaj haline getir
            var productDetails = string.Join("\n", expiringProducts.Select(p =>
                $"{p.Product?.Name} - {p.Price} ₺ (Geçerlilik Tarihi : {p.PriceGuaranteeDate:dd.MM.yyyy})"));

            var message =
                $@"Sayın {contract.Company?.Name},
                {contract.Title} adlı sözleşmede aşağıdaki sözleşme ürünlerinin fiyat geçerlilik süreleri dolmak üzere:
                Sözleşme Tarihi : {contract.InsertDate:dd.MM.yyyy}
                {productDetails}";

            foreach (var user in users)
            {
                if (string.IsNullOrWhiteSpace(user.PhoneNumber))
                    continue;

                var cleanedPhone = Clean(user.PhoneNumber);
                if (!string.IsNullOrWhiteSpace(cleanedPhone))
                {
                    // SMS gönderimi
                    try
                    {
                        await _smsManager.SendSmsAsync(cleanedPhone, message);
                    }
                    catch (Exception ex)
                    {}

                    // Notification gönderimi
                    try
                    {
                        await _notificationService.BulkSendAsync(new BulkSendNotificationDTO
                        {
                            UserIds = [user.Id],
                            Title = "Sözleşme Hatırlatma",
                            Note = message,
                            Type = "Contract",
                            Data = contract.Id.ToString()
                        });
                    }
                    catch (Exception ex)
                    {}
                }
            }
        }
    }

    public async Task<Response<string>> RequestByRetailUserAsync(RequestByRetailUserDTO entityVM)
    {
        try
        {
            // Giriş yapmış kullanıcı kontrolü
            var loginUser = await _workContext.GetUserAsync();
            if (loginUser == null)
            {
                throw new Exception("Kullanıcı oturumu bulunamadı.");
            }

            // Talebin alınması
            var transactionRequest = await _dbContext
                .TransactionRequest
                .Include(tr => tr.Contract!)
                    .ThenInclude(c => c.ContractProduct)
                .FirstOrDefaultAsync(tr => tr.Id == entityVM.TransactionRequestId);

            if (transactionRequest == null)
            {
                throw new Exception("İşlem talebi bulunamadı.");
            }

            // Contract kontrolü ve ContractProduct eklenmesi
            if (transactionRequest.Contract != null)
            {
                // Eğer Contract'a bağlı ContractProduct yoksa yeni bir tane oluştur
                if (transactionRequest.Contract.ContractProduct == null || !transactionRequest.Contract.ContractProduct.Any())
                {
                    // Transaction request'ten product ve amount bilgilerini al
                    var productId = transactionRequest.ProductId ?? Guid.Empty;
                    var desiredAmount = transactionRequest.DesiredTotalConcrete;

                    if (productId == Guid.Empty)
                    {
                        throw new Exception("İşlem talebinde ürün bilgisi bulunamadı.");
                    }

                    // Yeni ContractProduct oluştur
                    var newContractProduct = new ContractProduct
                    {
                        Id = Guid.NewGuid(),
                        ContractId = transactionRequest.Contract.Id,
                        ProductId = productId,
                        Price = entityVM.Price,
                        Amount = desiredAmount,
                        TotalPrice = entityVM.Price * desiredAmount,
                        LeftPrice = entityVM.Price * desiredAmount,
                        LeftAmount = desiredAmount,
                        PriceGuaranteeDate = DateTime.UtcNow.AddDays(30), // 30 gün garanti
                        IncludeTax = true
                    };

                    _dbContext.ContractProduct.Add(newContractProduct);
                }

                // Ödeme planını güncelle
                transactionRequest.Contract.PaymentPlanId = entityVM.PaymentPlanId;
                _dbContext.Contract.Update(transactionRequest.Contract);

                // TransactionRequest Onaylandı statüsüne alınır
                transactionRequest.StatusId = 2;
                transactionRequest.ApprovedUserId = loginUser.Id;
                transactionRequest.ApprovedDateTime = DateTime.Now;
                transactionRequest.ApprovedNote = "Onaylandı";
                _dbContext.TransactionRequest.Update(transactionRequest);

                // Değişiklikleri kaydet
                await _dbContext.SaveChangesAsync();

                return "Perakende kullanıcı talebi başarıyla oluşturuldu.".ToResponse();
            }

            return "Talep ile ilişkili Sözleşme bulunamadı.".ToResponse();

        }
        catch (Exception ex)
        {
            throw new Exception("Perakende kullanıcı talebi oluşturma sırasında bir hata oluştu: " + ex.Message);
        }
    }
   
    public static string Clean(string phone)
    {
        phone = phone
            .Replace(" ", "")
            .TrimStart('+')
            .TrimStart('9')
            .TrimStart('0');
        return phone;
    }
}