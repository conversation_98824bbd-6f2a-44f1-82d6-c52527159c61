using Application.Shared.Services;
using Application.Shared.Wrappers;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;

namespace Application.Contracts.Services;

public interface IContractProductTransactionLogService : IBaseService<ContractProductTransactionLogDTO, Guid>
{
    Task<PagedResponse<ContractProductTransactionLogDTO>> FilterAsync(ContractProductTransactionLogFilterModel filter);
}