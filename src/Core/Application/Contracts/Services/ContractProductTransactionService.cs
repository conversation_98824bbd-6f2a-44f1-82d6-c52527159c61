using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Contracts;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;
using Application.Shared;
using Domain.External;
using Application.General.Services;
using Application.General.DTOs;
using Application.Shared.Data;
using Domain.Shared;
using Domain.Account;

namespace Application.Contracts.Services;

public class ContractProductTransactionService(
    IMapper mapper,
    IWorkContext workContext,
    IERPManager erpManager,
    INotificationService notificationService,
    IApplicationDbContext dbContext)
    : BaseService<ContractProductTransaction, ContractProductTransactionDTO, Guid>(mapper, dbContext), IContractProductTransactionService
{
    private readonly IWorkContext _workContext = workContext;
    private readonly IERPManager _erpManager = erpManager;
    private readonly INotificationService _notificationService = notificationService;

    public async Task<PagedResponse<ContractProductTransactionDTO>> FilterAsync(ContractProductTransactionFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        var user = await _workContext.GetUserAsync();
        if (user != null)
        {
            var fullUser = await _dbContext.Users.AsQueryable().Including("Vehicle").FirstOrDefaultAsync(x => x.Id == user.Id);
            if (fullUser?.Vehicle?.Count > 0)
            {
                var vehicleIds = fullUser.Vehicle.Select(x => x.Id);
                filteredData = filteredData.Where(x => vehicleIds.Contains(x.VehicleId));
            }
        }
        if (filter.ContractId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ContractId == filter.ContractId.Value);
        }
        if (filter.CompanyId.HasValue)
        {
            filteredData = filteredData.Where(x => x.TransactionRequest.Building.CompanyId == filter.CompanyId.Value);
        }
        if (filter.StationId.HasValue)
        {
            filteredData = filteredData.Where(x => x.StationId == filter.StationId.Value);
        }
        if (filter.BuildingId.HasValue)
        {
            filteredData = filteredData.Where(x => x.TransactionRequest.BuildingId == filter.BuildingId.Value);
        }
        if (filter.ProductId.HasValue)
        {
            filteredData = filteredData.Where(x => x.TransactionRequest.ProductId == filter.ProductId.Value);
        }
        if (filter.StartDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.InsertDate >= filter.StartDate.Value);
        }
        if (filter.EndDate.HasValue)
        {
            filteredData = filteredData.Where(x => x.InsertDate <= filter.EndDate.Value);
        }
        if (filter.TransactionRequestId.HasValue)
        {
            filteredData = filteredData.Where(x => x.TransactionRequestId == filter.TransactionRequestId.Value);
        }
        if (filter.VehicleId.HasValue)
        {
            filteredData = filteredData.Where(x => x.VehicleId == filter.VehicleId.Value);
        }
        if (filter.DriverId.HasValue)
        {
            filteredData = filteredData.Where(x => x.Vehicle.DriverId == filter.DriverId.Value);
        }

        //İptal ve iade işlemleri için filtreleme
        if (filter.StatusId.HasValue && filter.StatusId.Value == 5 && filter.TypeId.HasValue && filter.TypeId.Value == 2)
        {
            // İptal ve iade işlemleri için sadece iptal ve iade olanları getir
            filteredData = filteredData.Where(x => x.StatusId == 5 || x.TypeId == 2);
        }
        else
        {
            if (filter.StatusId.HasValue)
            {
                filteredData = filteredData.Where(x => x.StatusId == filter.StatusId.Value);
            }
            if (filter.TypeId.HasValue)
            {
                filteredData = filteredData.Where(x => x.TypeId == filter.TypeId.Value);
            }
        }

        if (!string.IsNullOrWhiteSpace(filter.DocumentNo))
        {
            filteredData = filteredData.Where(x => x.DocumentNo.Contains(filter.DocumentNo));
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<ContractProductTransactionDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public override async Task<Response<ContractProductTransactionDTO>> FindAsync(Guid Id, string[] includeProperties = null)
    {
        var entity = await _dbSet
            .AsQueryable()
            .Including(BaseIncludes, includeProperties)
            .FirstOrDefaultAsync(x => x.Id == Id)
            .ConfigureAwait(false);
        if (entity == null)
        {
            throw new AppException("Transaction not found");
        }
        var data = _mapper.Map<ContractProductTransactionDTO>(entity);
        return data.ToResponse();
    }

    public Task<List<ContractProductTransactionStatusDTO>> GetContractProductTransactionStatusAsync()
    {
        return _dbContext.ContractProductTransactionStatus
            .AsQueryable()
            .Select(x => new ContractProductTransactionStatusDTO
            {
                Id = x.Id,
                Name = x.Name
            })
            .ToListAsync();
    }

    public virtual async Task<Response<ContractProductTransactionDTO>> InsertWithDocumentNoAsync(Guid TransactionRequestId, string DocumentNo)
    {
        var transactionRequest = _dbContext.TransactionRequest.AsQueryable().FirstOrDefault(x => x.Id == TransactionRequestId)
            ?? throw new Exception("Talep bulunamadı");
        var document = (await _erpManager.GetDocumentDetail(DocumentNo))[0];
        var vehicle = _dbContext.Vehicle.AsQueryable().FirstOrDefault(x => x.Plate.Replace(" ", "") == document.Plaka.Replace(" ", ""))
            ?? throw new Exception("Irsaliyeye ait araç sistemde kayıtlı değil");
        var station = _dbContext.Station.AsQueryable().FirstOrDefault(x => x.Name == document.TesisAdi);
        transactionRequest.StatusId = 5;
        _dbContext.TransactionRequest.Update(transactionRequest);
        await _dbContext.SaveChangesAsync().ConfigureAwait(false);
        return await InsertAsync(new ContractProductTransactionDTO
        {
            ContractId = transactionRequest.ContractId,
            TransactionRequestId = transactionRequest.Id,
            VehicleId = vehicle.Id,
            DriverId = vehicle.DriverId,
            StatusId = 1,
            DocumentNo = DocumentNo,
            SendingAmount = document.IrsaliyeMiktar ?? 0,
            StationId = station?.Id ?? transactionRequest.StationId,
            TypeId = 1,
        });
    }

    public override async Task<Response<ContractProductTransactionDTO>> InsertAsync(ContractProductTransactionDTO entityVM)
    {
        var transactionRequest = await _dbContext
            .TransactionRequest
            .AsQueryable()
            .Including("Product", "ContractProductTransaction", "Building")
            .FirstOrDefaultAsync(x => x.Id == entityVM.TransactionRequestId)
            ?? throw new AppException("Talep bulunamadı");
        if (entityVM.TypeId == 2)
        {
            if (transactionRequest.Product != null && !transactionRequest.Product.Refundable)
            {
                throw new AppException("Bu ürün iade edilemez");
            }
            var totalRefund = transactionRequest.ContractProductTransaction
                .Where(x => x.TypeId == 2)
                .Sum(x => x.SendingAmount);
            if (totalRefund + entityVM.SendingAmount > transactionRequest.Product?.RefundableAmount)
            {
                throw new AppException("Bu ürün için toplam iade miktarı aşıldı. Ürün iade miktarı: " + transactionRequest.Product.RefundableAmount);
            }
        }
        if (transactionRequest.StatusId < 5)
        {
            transactionRequest.StatusId = 5;
            transactionRequest.TransactionStartDateTime = DateTime.Now;
        }
        _dbContext.TransactionRequest.Update(transactionRequest);
        var entity = entityVM
            .MapTo<ContractProductTransaction>(_mapper);
        entity.Id = Guid.NewGuid();
        entity.AddDomainEvent(new ContractProductTransactionAddOrUpdateEvent
        {
            ContractProductTransactionId = entity.Id,
        });
        var local = _dbSet.Add(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
            
        var superadmins = await _dbContext.Users.AsQueryable().Where(x => x.UserRole.Any(x => x.RoleId == Role.SUPERADMIN)).Select(x => x.Id).ToListAsync();
        var buildingUserIds = await _dbContext.BuildingUser.AsQueryable().Where(x => x.BuildingId == transactionRequest.BuildingId).Select(x => x.UserId).ToListAsync();
        var companyUserIds = await _dbContext.Users.AsQueryable().Where(x => x.CompanyId == transactionRequest.Building.CompanyId && x.IsMaster).Select(x => x.Id).ToListAsync();
        
        var userIds = buildingUserIds.Concat(companyUserIds).Concat(superadmins).Distinct();
        try
        {
            var vehicle = await _dbContext.Vehicle.FindAsync(entity.VehicleId);
            entity.Vehicle = vehicle;
            await _notificationService.BulkSendAsync(new BulkSendNotificationDTO
            {
                UserIds = [.. userIds],
                Title = "Araç Durumu",
                Note = entity.StatusMessage + " Miktar: " + entity.SendingAmount + " (" + transactionRequest.Product?.Name + "), Şantiye: " + transactionRequest.Building?.Name,
                Type = "TransactionRequest",
                Data = transactionRequest.Id.ToString()
            });
        }
        catch (Exception)
        {
        }
        await AddLog(entity.Id, (ContractProductTransactionStatusEnum)entity.StatusId);
        var data = local.Entity.MapTo<ContractProductTransactionDTO>(_mapper);
        return data.ToResponse();
    }

    public override async Task<Response<ContractProductTransactionDTO>> UpdateAsync(ContractProductTransactionDTO entityVM)
    {
        var oldStatusId = await _dbSet
            .Where(x => x.Id == entityVM.Id)
            .Select(x => x.StatusId)
            .FirstOrDefaultAsync()
            .ConfigureAwait(false);

        // Eğer entity zaten track ediliyorsa, onu detach et
        if (_dbContext is DbContext context)
        {
            var localEntity = context.Set<ContractProductTransaction>()
                .Local.FirstOrDefault(e => e.Id == entityVM.Id);
            if (localEntity != null)
            {
                var trackedEntity = context.Entry(localEntity);
                trackedEntity.State = EntityState.Detached;
            }
        }

        var entity = entityVM
            .MapTo<ContractProductTransaction>(_mapper);
        entity.AddDomainEvent(new ContractProductTransactionAddOrUpdateEvent
        {
            ContractProductTransactionId = entity.Id,
        });

        if (entityVM.StatusId == 5) // İptal işlemi yapan kullanıcı bilgisi ve açıklaması alınır
        {
            entity.StatusId = 5;
            entity.CanceledNote = entityVM.CanceledNote;
            var loginUser = await _workContext.GetUserAsync();
            entity.CanceledUserId = loginUser.Id;
        }

        if (entityVM.TypeId == 2) // İade işlemi yapıldığında
        {
            if (entityVM.RefundAmount != null)
            {
                var findTransactionRequest = _dbContext.TransactionRequest
                    .AsQueryable()
                    .Including("Product")
                    .Including("Contract")
                    .Including("Contract.ContractProduct")
                    .Including("ContractProductTransaction")
                    .FirstOrDefault(x => x.Id == entityVM.TransactionRequestId)
                    ?? throw new Exception("Talep bulunamadı");

                if (findTransactionRequest.Product != null && !findTransactionRequest.Product.Refundable)
                {
                    throw new Exception("Bu ürün iade edilemez");
                }

                var totalRefund = findTransactionRequest.ContractProductTransaction?
                    .Where(x => x.TypeId == 2)
                    .Sum(x => x.SendingAmount) ?? 0;
                if (totalRefund + entityVM.SendingAmount > findTransactionRequest.Product?.RefundableAmount)
                {
                    throw new AppException("Bu ürün için toplam iade miktarı aşıldı. Ürün iade miktarı: " + findTransactionRequest.Product.RefundableAmount);
                }

                if (findTransactionRequest.Contract != null && findTransactionRequest.Contract.ContractProduct != null)
                {
                    var findContractProduct = findTransactionRequest.Contract.ContractProduct
                        .FirstOrDefault(x => x.ProductId == findTransactionRequest.ProductId)
                        ?? throw new Exception("Ürün sözleşmede bulunamadı");

                    // Fiyat tipine göre uygun fiyatı seç, yoksa varsayılan Price'ı kullan
                    var unitPrice = findTransactionRequest.PriceTypeId switch
                    {
                        1 => findContractProduct.Price, // Fişli
                        2 => findContractProduct.PriceWithoutPlug ?? findContractProduct.Price, // Fişsiz (yoksa Price)
                        3 => findContractProduct.PriceUnderPowerPlant ?? findContractProduct.Price, // Bantaltı (yoksa Price)
                        _ => findContractProduct.Price // Varsayılan
                    };

                    //Miktar
                    findContractProduct.LeftAmount = findContractProduct.LeftAmount + entityVM.RefundAmount;
                    findContractProduct.OldAmount = findContractProduct.OldAmount - entityVM.RefundAmount;

                    //Tutar
                    findContractProduct.LeftPrice = findContractProduct.LeftAmount * unitPrice;
                    findContractProduct.OldPrice = findContractProduct.OldAmount * unitPrice;

                    _dbContext.ContractProduct.Update(findContractProduct);
                    await _dbContext.SaveChangesAsync()
                        .ConfigureAwait(false);

                    // Contract'ın kalan ve kullanılan tutarını güncelle
                    var findContract = await _dbContext
                        .Contract
                        .FirstOrDefaultAsync(x => x.Id == findContractProduct.ContractId);

                    if (findContract != null)
                    {
                        //Miktar
                        findContract.OldWorth -= entityVM.RefundAmount * unitPrice;
                        findContract.LeftWorth = findContract.TotalWorth - findContract.OldWorth;

                        //Tutar
                        findContract.OldAmount -= entityVM.RefundAmount;
                        findContract.LeftAmount = findContract.TotalAmount - findContract.OldAmount;

                        _dbContext.Contract.Update(findContract);
                        await _dbContext.SaveChangesAsync()
                        .ConfigureAwait(false);
                    }
                }

                // İade işlemi için gerekli log eklenir
                var log = new ContractProductTransactionLog
                {
                    Description = "İade Edildi, İade Miktarı: " + entityVM.RefundAmount  + " m3",
                    InsertDate = DateTime.Now,
                    ContractProductTransactionId = entityVM.Id,
                };
                _dbContext.ContractProductTransactionLog.Add(log);
                await _dbContext.SaveChangesAsync().ConfigureAwait(false);

                return entityVM.ToResponse();
            }
        }

        var local = _dbSet
            .Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<ContractProductTransactionDTO>(_mapper);
        var result = data.ToResponse();
        var transactionRequest = await _dbContext
            .TransactionRequest
            .AsQueryable()
            .Including("Product", "ContractProductTransaction", "Building")
            .FirstOrDefaultAsync(x => x.Id == entityVM.TransactionRequestId)
            ?? throw new Exception("Talep bulunamadı");
        var buildingUserIds = await _dbContext.BuildingUser.AsQueryable().Where(x => x.BuildingId == transactionRequest.BuildingId).Select(x => x.UserId).ToListAsync();
        var companyUserIds = await _dbContext.Users.AsQueryable().Where(x => transactionRequest.Building != null && x.CompanyId == transactionRequest.Building.CompanyId && x.IsMaster).Select(x => x.Id).ToListAsync();
        var userIds = buildingUserIds.Concat(companyUserIds);
        try
        {
            var vehicle = await _dbContext.Vehicle.FindAsync(entity.VehicleId);

            if (vehicle != null)
            {
                var vehicleUpdate = await _dbContext.Vehicle
                    .Where(x => x.Id == vehicle.Id)
                    .ExecuteUpdateAsync(x => x
                        .SetProperty(v => v.DriverId, entity.DriverId)
                    );
            }

            entity.Vehicle = vehicle;

            // Notification gönderimi - hata durumunda sistem patlamasın
            try
            {
                await _notificationService.BulkSendAsync(new BulkSendNotificationDTO
                {
                    UserIds = [.. userIds],
                    Title = "Araç Durumu",
                    Note = entity.StatusMessage + " Miktar: " + entity.SendingAmount + " (" + transactionRequest.Product?.Name + "), Şantiye: " + transactionRequest.Building?.Name,
                    Type = "TransactionRequest",
                    Data = transactionRequest.Id.ToString()
                });
            }
            catch (Exception ex)
            {
                // Notification gönderim hatası loglanabilir ama sistem devam etsin
                Console.WriteLine($"Notification gönderim hatası - Transaction: {entity.Id}, Hata: {ex.Message}");
            }
        }
        catch (Exception ex)
        {
            // Araç güncelleme hatası loglanabilir ama sistem devam etsin
            Console.WriteLine($"Araç güncelleme hatası - VehicleId: {entity.VehicleId}, DriverId: {entity.DriverId}, Hata: {ex.Message}");
        }

        if (oldStatusId != entity.StatusId)
        {
            await AddLog(entity.Id, (ContractProductTransactionStatusEnum)entity.StatusId);
        }
        return result;
    }

    public override async Task PatchAsync(PatchDTO patchDTO)
    {
        var entity = await _dbSet
            .FindAsync(patchDTO.Id)
            .ConfigureAwait(false);

        var oldStatusId = entity?.StatusId;

        if (entity != null)
        {
            // Patch işlemi sırasında özel alanlar kontrol edilir
            bool shouldCallUpdate = false;

            // TypeId 2 ise ve RefundAmount güncellenmişse
            if (patchDTO.Patch.Any(x => x.Path.Contains("TypeId")) &&
                patchDTO.Patch.Any(x => x.Path.Contains("RefundAmount")))
            {
                var refundAmount = patchDTO.Patch.FirstOrDefault(x => x.Path.Contains("RefundAmount"))?.Value;
                if (refundAmount != null && decimal.TryParse(refundAmount.ToString(), out var parsedRefundAmount))
                {
                    entity.RefundAmount = parsedRefundAmount;
                }
                entity.TypeId = 2; // İade işlemi yapıldığında TypeId 2 olarak ayarlanır
                shouldCallUpdate = true;
            }

            // VehicleId ve DriverId alanları güncellenmişse
            if (patchDTO.Patch.Any(x => x.Path.Contains("VehicleId")) ||
                patchDTO.Patch.Any(x => x.Path.Contains("DriverId")))
            {
                var vehicleId = patchDTO.Patch.FirstOrDefault(x => x.Path.Contains("VehicleId"))?.Value;
                var driverId = patchDTO.Patch.FirstOrDefault(x => x.Path.Contains("DriverId"))?.Value;

                if (vehicleId != null && Guid.TryParse(vehicleId.ToString(), out var parsedVehicleId))
                {
                    entity.VehicleId = parsedVehicleId;
                }

                if (driverId != null && Guid.TryParse(driverId.ToString(), out var parsedDriverId))
                {
                    entity.DriverId = parsedDriverId;
                }

                shouldCallUpdate = true;
            }

            // Eğer özel alanlardan herhangi biri güncellenmişse UpdateAsync metodunu çağır
            if (shouldCallUpdate)
            {
                var entityVM = entity.MapTo<ContractProductTransactionDTO>(_mapper);
                await UpdateAsync(entityVM)
                    .ConfigureAwait(false);
            }

            // Eğer özel alanlar güncellenmemişse normal patch işlemi yap
            else
            {
                _ = entity.GetType();
                patchDTO.PatchModel.ApplyTo(entity);

                entity.AddDomainEvent(new ContractProductTransactionAddOrUpdateEvent
                {
                    ContractProductTransactionId = entity.Id,
                });

                _dbSet.Update(entity);
                await _dbContext
                    .SaveChangesAsync()
                    .ConfigureAwait(false);
                if (patchDTO.Patch.Any(x => x.Path.Contains("StatusId")) && oldStatusId != entity.StatusId)
                {
                    await AddLog(entity.Id, (ContractProductTransactionStatusEnum)entity.StatusId);
                }
            }
        }
    }

    public override async Task PatchListAsync(List<PatchDTO> patchListVM)
    {
        foreach (var item in patchListVM)
        {
            await PatchAsync(item);
        }
    }

    private async Task AddLog(Guid ContractProductTransactionId, ContractProductTransactionStatusEnum StatusId)
    {
        var description = StatusId switch
        {
            ContractProductTransactionStatusEnum.Pending => "Araç Beklemede",
            ContractProductTransactionStatusEnum.OnRoad => "Araç Yola Çıktı",
            ContractProductTransactionStatusEnum.InProgress => "Döküm Başladı",
            ContractProductTransactionStatusEnum.Finished => "Döküm Tamamlandı",
            ContractProductTransactionStatusEnum.Cancel => "İptal Edildi",
            _ => ""
        };
        var log = new ContractProductTransactionLog
        {
            Description = description,
            InsertDate = DateTime.Now,
            ContractProductTransactionId = ContractProductTransactionId,
        };
        _dbContext.ContractProductTransactionLog.Add(log);
        await _dbContext.SaveChangesAsync().ConfigureAwait(false);
    }
}