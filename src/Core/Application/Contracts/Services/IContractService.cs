using Application.Shared.Services;
using Application.Shared.Wrappers;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;

namespace Application.Contracts.Services;

public interface IContractService : IBaseService<ContractDTO, Guid>
{
    Task<PagedResponse<ContractDTO>> FilterAsync(ContractFilterModel filter);
    Task<List<PaymentPlanDTO>> GetPaymentPlanAsync();
    Task SendWithEmail(Guid contractId);
    Task Approve(Guid ContractId, string Note);
    Task Reject(Guid ContractId, string Note);
    Task SendRemindMessageAsync();
    Task<Response<string>> RequestByRetailUserAsync(RequestByRetailUserDTO entityVM);
}