using Application.Shared.Services;
using Application.Shared.Wrappers;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;

namespace Application.Contracts.Services;

public interface IContractProductTransactionService : IBaseService<ContractProductTransactionDTO, Guid>
{
    Task<PagedResponse<ContractProductTransactionDTO>> FilterAsync(ContractProductTransactionFilterModel filter);
    Task<List<ContractProductTransactionStatusDTO>> GetContractProductTransactionStatusAsync();
    Task<Response<ContractProductTransactionDTO>> InsertWithDocumentNoAsync(Guid TransactionRequestId, string DocumentNo);
}