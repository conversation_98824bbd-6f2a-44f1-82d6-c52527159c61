using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Contracts;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;
using Application.Shared.Data;

namespace Application.Contracts.Services;

public class ContractProductTransactionLogService(
    IMapper mapper,
    IApplicationDbContext dbContext
) : BaseService<ContractProductTransactionLog, ContractProductTransactionLogDTO, Guid>(mapper, dbContext), IContractProductTransactionLogService
{

    public async Task<PagedResponse<ContractProductTransactionLogDTO>> FilterAsync(ContractProductTransactionLogFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (filter.ContractProductTransactionId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ContractProductTransactionId == filter.ContractProductTransactionId.Value);
        }
        if (filter.TransactionRequestId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ContractProductTransaction.TransactionRequestId == filter.TransactionRequestId.Value);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<ContractProductTransactionLogDTO>>(list);
        return data.ToPagedResponse(filter);
    }
}