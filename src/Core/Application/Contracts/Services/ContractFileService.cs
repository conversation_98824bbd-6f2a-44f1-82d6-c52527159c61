using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Contracts;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;
using Application.Shared.Data;

namespace Application.Contracts.Services;

public class ContractFileService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<ContractFile, ContractFileDTO, Guid>(mapper, dbContext), IContractFileService
{
    public async Task<PagedResponse<ContractFileDTO>> FilterAsync(ContractFileFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (filter.ContractId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ContractId == filter.ContractId.Value);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<ContractFileDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public override async Task<Response<ContractFileDTO>> InsertAsync(ContractFileDTO entityVM)
    {
        if (entityVM.File != null)
        {
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "ContractFile");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }
            var extension = Path.GetExtension(entityVM.File.FileName);
            var contractFileId = Guid.NewGuid();
            var fileName = entityVM.File.FileName + "_" + contractFileId + extension;
            var filePath = Path.Combine(uploadsPath, fileName);
            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                entityVM.File.CopyTo(stream);
            }
            entityVM.FileName = fileName;
        }
        return await base.InsertAsync(entityVM);
    }

    public override async Task<PagedResponse<ContractFileDTO>> InsertListAysnc(List<ContractFileDTO> entityListVM)
    {
        if (entityListVM?.Any(x => x.File != null) == true)
        {
            var uploadsPath = Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "ContractFile");
            if (!Directory.Exists(uploadsPath))
            {
                Directory.CreateDirectory(uploadsPath);
            }
            foreach (var file in entityListVM.Where(x => x.File != null))
            {
                var extension = Path.GetExtension(file.File.FileName);
                var contractFileId = Guid.NewGuid();
                var fileName = file.File.FileName + "_" + contractFileId + extension;
                var filePath = Path.Combine(uploadsPath, fileName);
                using (var stream = new FileStream(filePath, FileMode.Create))
                {
                    file.File.CopyTo(stream);
                }
                file.FileName = fileName;
            }
        }
        return await base.InsertListAysnc(entityListVM);
    }

    public override async Task DeleteAsync(Guid id)
    {
        var entity = await _dbSet
            .FindAsync(id)
            .ConfigureAwait(false);
        if (!string.IsNullOrWhiteSpace(entity.FileName) && File.Exists(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "ContractFile", entity.FileName)))
        {
            File.Delete(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "ContractFile", entity.FileName));
        }
        _dbSet.Remove(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public override async Task DeleteListAsync(List<Guid> ids)
    {
        var entityList = new List<ContractFile>();
        foreach (var id in ids)
        {
            var entity = await _dbSet
                .FindAsync(id)
                .ConfigureAwait(false);
            if (entity != null)
            {
                if (!string.IsNullOrWhiteSpace(entity.FileName) && File.Exists(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "ContractFile", entity.FileName)))
                {
                    File.Delete(Path.Combine(Directory.GetCurrentDirectory(), "Uploads", "ContractFile", entity.FileName));
                }
                entityList.Add(entity);
            }
        }
        _dbSet.RemoveRange(entityList);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }
}