using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Contracts;
using Application.Contracts.DTOs;
using Application.Contracts.FilterModel;
using Application.Shared.Data;
using Domain.Shared;

namespace Application.Contracts.Services;

public class ContractProductService(
    IMapper mapper,
    AppSettings appSettings,
    IApplicationDbContext dbContext)
    : BaseService<ContractProduct, ContractProductDTO, Guid>(mapper, dbContext), IContractProductService
{
    private readonly AppSettings _appSettings = appSettings;

    public async Task<PagedResponse<ContractProductDTO>> FilterAsync(ContractProductFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (filter.ContractId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ContractId == filter.ContractId.Value);
        }
        if (filter.ProductId.HasValue)
        {
            filteredData = filteredData.Where(x => x.ProductId == filter.ProductId.Value);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<ContractProductDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public override async Task<Response<ContractProductDTO>> InsertAsync(ContractProductDTO entityVM)
    {
        var entity = entityVM.MapTo<ContractProduct>(_mapper);
        entity.LeftAmount = entity.Amount;
        var local = _dbSet.Add(entity);
        if (entity.ContractProductBuilding?.Count > 0)
        {
            foreach (var item in entity.ContractProductBuilding)
            {
                _dbContext.ContractProductBuilding.Add(item);
            }
        }
        if (entity.ContractProductConsistencyClass?.Count > 0)
        {
            foreach (var item in entity.ContractProductConsistencyClass)
            {
                _dbContext.ContractProductConsistencyClass.Add(item);
            }
        }

        if (entity.ContractProductConcreteOption?.Count > 0)
        {
            foreach (var item in entity.ContractProductConcreteOption)
            {
                _dbContext.ContractProductConcreteOption.Add(item);
            }
        }
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        
        var data = local.Entity.MapTo<ContractProductDTO>(_mapper);
        return data.ToResponse();
    }

    public override async Task<PagedResponse<ContractProductDTO>> InsertListAysnc(List<ContractProductDTO> entityListVM)
    {
        var list = new List<ContractProductDTO>();
        foreach (var entityVM in entityListVM)
        {
            var result = await InsertAsync(entityVM);
            list.Add(result.Value);
        }
        return list.ToPagedResponse();
    }

    public override async Task<Response<ContractProductDTO>> UpdateAsync(ContractProductDTO entityVM)
    {
        try
        {
            var entity = await _dbSet
                .AsQueryable()
                .Including("ContractProductBuilding")
                .Including("ContractProductConsistencyClass")
                .Including("ContractProductConcreteOption")
                .FirstOrDefaultAsync(x => x.ContractId == entityVM.ContractId && x.ProductId == entityVM.ProductId)
                ?? throw new Exception("Kayıt bulunamadı");
        entity.Amount = entityVM.Amount;
        entity.OldAmount = entityVM.OldAmount;
        entity.Price = entityVM.Price;
        entity.PriceWithoutPlug = entityVM.PriceWithoutPlug;
        entity.PriceUnderPowerPlant = entityVM.PriceUnderPowerPlant;
        entity.PriceGuaranteeDate = entityVM.PriceGuaranteeDate;
        entity.IncludeTax = entityVM.IncludeTax;
        entity.ContractProductBuilding ??= [];
        entityVM.ContractProductBuilding ??= [];
        entity.ContractProductConsistencyClass ??= [];
        entityVM.ContractProductConsistencyClass ??= [];
        entity.ContractProductConcreteOption ??= [];
        entityVM.ContractProductConcreteOption ??= [];
        foreach (var item in entity.ContractProductBuilding.Where(k => !entityVM.ContractProductBuilding.Any(l => l.BuildingId == k.BuildingId)).ToList())
        {
            _dbContext.ContractProductBuilding.Remove(item);
        }
        foreach (var item in entityVM.ContractProductBuilding.Where(k => !entity.ContractProductBuilding.Any(l => l.BuildingId == k.BuildingId)).ToList())
        {
            _dbContext.ContractProductBuilding.Add(item.MapTo<ContractProductBuilding>(_mapper));
        }

        foreach (var item in entity.ContractProductConsistencyClass.Where(k => !entityVM.ContractProductConsistencyClass.Any(l => l.ConsistencyClassId == k.ConsistencyClassId)).ToList())
        {
            _dbContext.ContractProductConsistencyClass.Remove(item);
        }
        foreach (var item in entityVM.ContractProductConsistencyClass.Where(k => !entity.ContractProductConsistencyClass.Any(l => l.ConsistencyClassId == k.ConsistencyClassId)).ToList())
        {
            _dbContext.ContractProductConsistencyClass.Add(item.MapTo<ContractProductConsistencyClass>(_mapper));
        }

        foreach (var item in entity.ContractProductConcreteOption.Where(k => !entityVM.ContractProductConcreteOption.Any(l => l.ConcreteOptionId == k.ConcreteOptionId)).ToList())
        {
            _dbContext.ContractProductConcreteOption.Remove(item);
        }
        foreach (var item in entityVM.ContractProductConcreteOption.Where(k => !entity.ContractProductConcreteOption.Any(l => l.ConcreteOptionId == k.ConcreteOptionId)).ToList())
        {
            _dbContext.ContractProductConcreteOption.Add(item.MapTo<ContractProductConcreteOption>(_mapper));
        }
            var local = _dbSet.Update(entity);
            await _dbContext
                .SaveChangesAsync()
                .ConfigureAwait(false);
            var data = local.Entity.MapTo<ContractProductDTO>(_mapper);
            return data.ToResponse();
        }
        catch (Exception ex)
        {
            // Hata durumunda daha detaylı bilgi ver
            throw new Exception($"ContractProduct güncelleme işlemi başarısız: {ex.Message}", ex);
        }
    }

    public override async Task UpdateListAsync(List<ContractProductDTO> entityListVM)
    {
        foreach (var entityVM in entityListVM)
        {
            await UpdateAsync(entityVM);
        }
    }

    public override async Task<Response<ContractProductDTO>> DeleteAsync(Guid id)
    {
        try
        {
            var entity = await _dbSet
                .AsQueryable()
                .Including("ContractProductBuilding")
                .Including("ContractProductConsistencyClass")
                .Including("ContractProductConcreteOption")
                .FirstOrDefaultAsync(x => x.Id == id)
                ?? throw new Exception("Kayıt bulunamadı");

            // Bağlı verileri manuel olarak sil - null kontrolü ekle
            if (entity.ContractProductBuilding?.Count > 0)
            {
                _dbContext.ContractProductBuilding.RemoveRange(entity.ContractProductBuilding);
            }

            if (entity.ContractProductConsistencyClass?.Count > 0)
            {
                _dbContext.ContractProductConsistencyClass.RemoveRange(entity.ContractProductConsistencyClass);
            }

            if (entity.ContractProductConcreteOption?.Count > 0)
            {
                _dbContext.ContractProductConcreteOption.RemoveRange(entity.ContractProductConcreteOption);
            }

            // Önce bağlı verileri kaydet
            await _dbContext
                .SaveChangesAsync()
                .ConfigureAwait(false);

            // Sonra ana contract product'ı sil
            _dbSet.Remove(entity);
            await _dbContext
                .SaveChangesAsync()
                .ConfigureAwait(false);

            return entity.MapTo<ContractProductDTO>(_mapper).ToResponse();
        }
        catch (Exception ex)
        {
            // Hata durumunda daha detaylı bilgi ver
            throw new Exception($"ContractProduct silme işlemi başarısız: {ex.Message}", ex);
        }
    }
}