using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq.Expressions;
using System.Reflection;
using System.Transactions;

namespace Application.Shared.Extensions;

public static class IQueryableExtensions
{
    public static IQueryable<TSource> Sorting<TSource>(this IQueryable<TSource> source, string propertyName, string sortType = "asc")
    {
        //https://stackoverflow.com/questions/2728340/how-can-i-do-an-orderby-with-a-dynamic-string-parameter
        IOrderedQueryable<TSource> dataSource = source.OrderBy(x => (true));
        if (!string.IsNullOrWhiteSpace(propertyName))
        {
            var propertyList = propertyName.Split(',');
            var sortTypeList = sortType.Split(',');
            foreach (var (property, i) in propertyList.Select((v, i) => (v, i)))
            {
                bool ascending = (sortTypeList.Length > i ? sortTypeList[i] : sortTypeList[0]) == "asc";
                if (!string.IsNullOrWhiteSpace(property))
                {
                    if (typeof(TSource).GetProperty(property) == null)
                    {
                        continue;
                    }
                    if (typeof(TSource).GetProperty(property)?.GetCustomAttribute<NotMappedAttribute>() != null)
                    {
                        continue;
                    }
                    if (i == 0)
                    {
                        var lambda = (dynamic)CreateExpression(typeof(TSource), property);
                        dataSource = ascending
                            ? Queryable.OrderBy(dataSource, lambda)
                            : Queryable.OrderByDescending(dataSource, lambda);
                    }
                    else
                    {
                        var lambda = (dynamic)CreateExpression(typeof(TSource), property);
                        var orderedDataSource = dataSource;
                        dataSource = ascending
                            ? Queryable.ThenBy(orderedDataSource, lambda)
                            : Queryable.ThenByDescending(orderedDataSource, lambda);
                    }
                }
            }
        }
        var type = typeof(TSource);
        PropertyInfo? propertyInfo = type.GetProperty("Id");
        if (propertyInfo != null)
        {
            var notMappedAttribute = propertyInfo.GetCustomAttribute<NotMappedAttribute>();
            if (notMappedAttribute == null)
            {
                var Idlambda = (dynamic)CreateExpression(typeof(TSource), "Id");
                dataSource = Queryable.ThenBy(dataSource, Idlambda);
            }
        }
        return dataSource;
    }

    private static LambdaExpression CreateExpression(Type type, string propertyName)
    {
        var param = Expression.Parameter(type, "x");

        Expression body = param;
        foreach (var member in propertyName.Split('.'))
        {
            body = Expression.PropertyOrField(body, member);
        }

        return Expression.Lambda(body, param);
    }

    public static IQueryable<T> Paging<T>(this IQueryable<T> source, int pageIndex, int pageSize)
    {
        if (pageSize < 0)
        {
            return source;
        }
        return source.Skip((pageIndex - 1) * pageSize).Take(pageSize);
    }

    public static IQueryable<T> Including<T>(this IQueryable<T> source, params string[] includeProperties) where T : class
    {
        if (includeProperties != null)
        {
            foreach (var includeProperty in includeProperties)
            {
                source = source.Include(includeProperty);
            }
        }
        return source;
    }

    public static IQueryable<T> Including<T>(this IQueryable<T> source, string[] baseIncludes, params string[] includeProperties) where T : class
    {
        if (baseIncludes == null || baseIncludes?.Length <= 0)
        {
            return Including(source, includeProperties);
        }
        if (includeProperties != null)
        {
            var allIncludes = baseIncludes.Concat(includeProperties);
            foreach (var includeProperty in allIncludes)
            {
                source = source.Include(includeProperty);
            }
        }
        return source;
    }

    public static IQueryable<T> Including<T>(this IQueryable<T> source, params Expression<Func<T, object>>[] includeProperties) where T : class
    {
        if (includeProperties != null)
        {
            foreach (var includeProperty in includeProperties)
            {
                source = source.Include(includeProperty);
            }
        }

        return source;
    }

    public static IQueryable<T> Select<T>(this IQueryable<T> source, string? fields)
    {
        if (string.IsNullOrWhiteSpace(fields))
        {
            return source;
        }
        return source.Select(fields.BuildSelector<T, T>());
    }

    public static Expression<Func<TSource, TTarget>> BuildSelector<TSource, TTarget>(this string members)
    {
        // https://stackoverflow.com/questions/51753165/c-sharp-dynamically-generate-linq-select-with-nested-properties/51764873
        return members.Split(',').Select(m => m.Trim()).BuildSelector<TSource, TTarget>();
    }

    public static Expression<Func<TSource, TTarget>> BuildSelector<TSource, TTarget>(this IEnumerable<string> members)
    {
        var parameter = Expression.Parameter(typeof(TSource), "e");
        var body = NewObject(typeof(TTarget), parameter, members.Select(m => m.Split('.')));
        return Expression.Lambda<Func<TSource, TTarget>>(body, parameter);
    }

    static Expression NewObject(Type targetType, Expression source, IEnumerable<string[]> memberPaths, int depth = 0)
    {
        var bindings = new List<MemberBinding>();
        var target = Expression.Constant(null, targetType);
        foreach (var memberGroup in memberPaths.GroupBy(path => path[depth]))
        {
            var memberName = memberGroup.Key;
            var targetMember = Expression.PropertyOrField(target, memberName);
            var sourceMember = Expression.PropertyOrField(source, memberName);
            var childMembers = memberGroup.Where(path => depth + 1 < path.Length);
            var targetValue = !childMembers.Any() ? sourceMember :
                NewObject(targetMember.Type, sourceMember, childMembers, depth + 1);
            bindings.Add(Expression.Bind(targetMember.Member, targetValue));
        }
        return Expression.MemberInit(Expression.New(targetType), bindings);
    }

    // https://www.brianwalls.org/brian-walls/using-nolock-in-entity-framework-core
    public static async Task<List<T>> ToListWithNoLockAsync<T>(this IQueryable<T> query, Expression<Func<T, bool>>? expression = null, CancellationToken cancellationToken = default)
    {
        List<T>? result = default;
        using (var scope = CreateTransaction())
        {
            if (expression is not null)
            {
                query = query.Where(expression);
            }
            result = await query.ToListAsync(cancellationToken);
            scope.Complete();
        }
        return result;
    }

    public static async Task<int> CountWithNoLockAsync<T>(this IQueryable<T> query, Expression<Func<T, bool>>? expression = null, CancellationToken cancellationToken = default)
    {
        using var scope = CreateTransaction();
        if (expression is not null)
        {
            query = query.Where(expression);
        }
        int toReturn = await query.CountAsync(cancellationToken);
        scope.Complete();
        return toReturn;
    }

    public static async Task<T?> FirstOrDefaultWithNoLockAsync<T>(this IQueryable<T> query, Expression<Func<T, bool>>? expression = null, CancellationToken cancellationToken = default)
    {
        using var scope = CreateTransaction();
        if (expression is not null)
        {
            query = query.Where(expression);
        }
        T? result = await query.FirstOrDefaultAsync(cancellationToken);
        scope.Complete();
        return result;
    }

    private static TransactionScope CreateTransaction()
    {
        return new TransactionScope(TransactionScopeOption.Required,
            new TransactionOptions()
            {
                IsolationLevel = IsolationLevel.ReadUncommitted
            },
            TransactionScopeAsyncFlowOption.Enabled);
    }
}

public static class PredicateBuilder
{
    public static Expression<Func<T, bool>> True<T>() { return f => true; }
    public static Expression<Func<T, bool>> False<T>() { return f => false; }

    public static Expression<Func<T, bool>> CustomOr<T>(this Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
    {
        var invokedExpr = Expression.Invoke(expr2, expr1.Parameters.Cast<Expression>());
        return Expression.Lambda<Func<T, bool>>(Expression.OrElse(expr1.Body, invokedExpr), expr1.Parameters);
    }

    public static Expression<Func<T, bool>> CustomAnd<T>(this Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
    {
        var invokedExpr = Expression.Invoke(expr2, expr1.Parameters.Cast<Expression>());
        return Expression.Lambda<Func<T, bool>>(Expression.And(expr1.Body, invokedExpr), expr1.Parameters);
    }
}