using Application.Shared.Wrappers;
using Domain.Shared;

namespace Application.Shared.Extensions;

public static class ResponseExtensions
{
    public static Response<T> ToResponse<T>(this T entity)
    {
        return new Response<T>(entity);
    }

    public static PagedResponse<T> ToPagedResponse<T>(this List<T> entity, BaseFilterModel? filter = null)
    {
        return new PagedResponse<T>(entity)
        {
            Count = filter?.Count ?? entity.Count,
            FilteredCount = filter?.FilteredCount ?? entity.Count,
            PageIndex = filter?.PageIndex ?? 1,
            PageSize = filter?.PageSize ?? 1,
            SortProperty = filter?.SortProperty ?? "Id",
            SortType = filter?.SortType ?? "asc",
        };
    }
}