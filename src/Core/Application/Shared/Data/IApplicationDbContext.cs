using Domain.Account;
using Domain.Catalog;
using Domain.Companies;
using Domain.Contracts;
using Domain.General;
using Domain.Stations;
using Domain.Transactions;
using Microsoft.EntityFrameworkCore;

namespace Application.Shared.Data;

public interface IApplicationDbContext
{
    DbSet<User> Users { get; set; }
    DbSet<UserClaim> UserClaims { get; set; }
    DbSet<UserLogin> UserLogins { get; set; }
    DbSet<UserToken> UserTokens { get; set; }
    DbSet<UserRole> UserRoles { get; set; }
    DbSet<Role> Roles { get; set; }
    DbSet<RoleClaim> RoleClaims { get; set; }

    DbSet<UserDepartment> UserDepartment { get; set; }
    DbSet<Page> Page { get; set; }
    DbSet<PageRule> PageRule { get; set; }

    DbSet<Product> Product { get; set; }

    DbSet<Building> Building { get; set; }
    DbSet<BuildingUser> BuildingUser { get; set; }
    DbSet<Company> Company { get; set; }

    DbSet<Contract> Contract { get; set; }
    DbSet<PaymentPlan> PaymentPlan { get; set; }
    DbSet<ContractProduct> ContractProduct { get; set; }
    DbSet<ContractProductBuilding> ContractProductBuilding { get; set; }
    DbSet<ContractProductConsistencyClass> ContractProductConsistencyClass { get; set; }
    DbSet<ContractProductConcreteOption> ContractProductConcreteOption { get; set; }
    DbSet<ContractFile> ContractFile { get; set; }
    DbSet<ContractProductTransaction> ContractProductTransaction { get; set; }
    DbSet<ContractProductTransactionLog> ContractProductTransactionLog { get; set; }
    DbSet<ContractProductTransactionStatus> ContractProductTransactionStatus { get; set; }
    DbSet<TransactionType> TransactionType { get; set; }

    DbSet<City> City { get; set; }
    DbSet<District> District { get; set; }
    DbSet<NotAvailableDate> NotAvailableDate { get; set; }
    DbSet<Notification> Notification { get; set; }
    DbSet<StateProvince> StateProvince { get; set; }
    DbSet<Gallery> Gallery { get; set; }

    DbSet<Department> Department { get; set; }
    DbSet<PompType> PompType { get; set; }
    DbSet<Station> Station { get; set; }
    DbSet<StationDepartment> StationDepartment { get; set; }
    DbSet<StationStatus> StationStatus { get; set; }
    DbSet<Vehicle> Vehicle { get; set; }
    DbSet<VehicleStatus> VehicleStatus { get; set; }
    DbSet<VehicleType> VehicleType { get; set; }

    DbSet<TransactionRequest> TransactionRequest { get; set; }
    DbSet<TransactionRequestConcreteOption> TransactionRequestConcreteOption { get; set; }
    DbSet<TransactionRequestPompType> TransactionRequestPompType { get; set; }
    DbSet<TransactionRequestType> TransactionRequestType { get; set; }
    DbSet<LabResult> LabResult { get; set; }
    DbSet<TransactionStatus> TransactionStatus { get; set; }
    DbSet<ConcreteLocation> ConcreteLocation { get; set; }
    DbSet<ConcreteOption> ConcreteOption { get; set; }
    DbSet<ConsistencyClass> ConsistencyClass { get; set; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    DbSet<TEntity> Set<TEntity>() where TEntity : class;
}