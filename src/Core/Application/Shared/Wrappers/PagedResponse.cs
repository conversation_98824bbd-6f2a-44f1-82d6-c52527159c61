namespace Application.Shared.Wrappers;

public class PagedResponse<T>(List<T> value, int pageSize = 1, int pageIndex = 20) : BaseResponse
{
    public List<T> Data { get; set; } = value;
    public int PageSize { get; set; } = pageSize;
    public int PageIndex { get; set; } = pageIndex;
    public int Count { get; set; }
    public int FilteredCount { get; set; }
    public string SortProperty { get; set; } = "Id";
    public string SortType { get; set; } = "asc";
    public int TotalPageSize
    {
        get { return (int)Math.Ceiling((double)Count / PageSize); }
    }
}