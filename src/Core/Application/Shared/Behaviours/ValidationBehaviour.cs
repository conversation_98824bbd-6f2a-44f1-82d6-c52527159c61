using FluentValidation;
using FluentValidation.Results;
using Domain.Shared;
using MediatR;
using Application.Shared.Wrappers;

namespace Application.Shared.Behaviours;

public class ValidationBehaviour<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
    where TRequest : IRequest<TResponse>
    where TResponse : BaseResponse
{
    private readonly IEnumerable<IValidator<TRequest>> _validators;

    public ValidationBehaviour(IEnumerable<IValidator<TRequest>> validators)
    {
        _validators = validators;
    }

    public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
    {
        if (_validators.Any())
        {
            var context = new ValidationContext<TRequest>(request);

            var validationResults = await Task.WhenAll(
                _validators.Select(v =>
                    v.ValidateAsync(context, cancellationToken)));

            var failures = validationResults
                .Where(r => r.Errors.Any())
                .SelectMany(r => r.Errors)
                .ToList();

            if (failures.Any())
            {
                throw new AppException("Validation Error")
                {
                    Validations = AddErrors(failures)
                };
            }
        }
        return await next();
    }

    private List<ValidationError> AddErrors(List<ValidationFailure> failures)
    {
        return failures.Select(x => new ValidationError
        {
            Key = x.PropertyName,
            Value = x.ErrorMessage
        }).ToList();
    }
}