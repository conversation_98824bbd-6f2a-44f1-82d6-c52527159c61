using Application.Shared.Data;
using Application.Shared.Extensions;
using Application.Shared.Wrappers;
using AutoMapper;
using AutoMapper.Extensions.ExpressionMapping;
using Domain.Shared;
using FluentValidation;
using FluentValidation.Results;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;

namespace Application.Shared.Services;

public abstract class BaseService<TEntity, TDTO, TKey>(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : IBaseService<TDTO, TKey> where TEntity : class, IBaseEntity where TDTO : BaseDTO
{
    protected readonly IMapper _mapper = mapper;
    protected readonly IApplicationDbContext _dbContext = dbContext;
    protected readonly DbSet<TEntity> _dbSet = dbContext.Set<TEntity>();
    protected string[] BaseIncludes { get; set; } = [];

    public virtual async Task<PagedResponse<TDTO>> FilterAsync(BaseFilterModel filter, params string[] includeProperties)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties ?? includeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<TDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public virtual async Task<Response<TDTO>> FirstOrDefaultAsync(params string[] includeProperties)
    {
        if (BaseIncludes?.Length > 0)
        {
            includeProperties = includeProperties.Concat(BaseIncludes).ToArray();
        }
        var data = await _dbSet.AsQueryable()
            .Including(includeProperties)
            .UseAsDataSource(_mapper)
            .For<TDTO>()
            .FirstOrDefaultAsync();
        return data.ToResponse();
    }

    public virtual async Task<Response<TDTO>> FirstOrDefaultAsync(Expression<Func<TDTO, bool>> query, params string[] includeProperties)
    {
        if (BaseIncludes?.Length > 0)
        {
            includeProperties = includeProperties.Concat(BaseIncludes).ToArray();
        }
        var data = await _dbSet.AsQueryable()
            .Including(includeProperties)
            .UseAsDataSource(_mapper)
            .For<TDTO>()
            .FirstOrDefaultAsync(query);
        return data.ToResponse();
    }

    public virtual async Task<Response<TDTO>> FindAsync(Guid id, params string[] includeProperties)
    {
        if (BaseIncludes?.Length > 0)
        {
            includeProperties = [.. includeProperties, .. BaseIncludes];
        }
        var entity = await _dbSet
            .FindAsync(id)
            .ConfigureAwait(false);
        var data = entity.MapTo<TDTO>(_mapper);
        return data.ToResponse();
    }

    public virtual async Task<Response<TDTO>> InsertAsync(TDTO entityVM)
    {
        var entity = entityVM
            .MapTo<TEntity>(_mapper);
        var local = _dbSet.Add(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<TDTO>(_mapper);
        return data.ToResponse();
    }

    public virtual async Task<PagedResponse<TDTO>> InsertListAysnc(List<TDTO> entityListVM)
    {
        var mapped = entityListVM.MapTo<List<TEntity>>(_mapper);
        _dbSet.AddRange(mapped);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        return entityListVM.ToPagedResponse();
    }

    public virtual async Task<Response<TDTO>> UpdateAsync(TDTO entityVM)
    {
        var entity = entityVM
            .MapTo<TEntity>(_mapper);
        var local = _dbSet
            .Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<TDTO>(_mapper);
        return data.ToResponse();
    }

    public virtual async Task UpdateListAsync(List<TDTO> entityListVM)
    {
        var entityList = entityListVM
            .MapTo<List<TEntity>>(_mapper);
        _dbSet.UpdateRange(entityList);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public virtual async Task DeleteAsync(TKey id)
    {
        var entity = await _dbSet
            .FindAsync(id)
            .ConfigureAwait(false);
        _dbSet.Remove(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public virtual async Task DeleteListAsync(List<TKey> ids)
    {
        var entityList = new List<TEntity>();
        foreach (var id in ids)
        {
            var entity = await _dbSet
                .FindAsync(id)
                .ConfigureAwait(false);
            if (entity != null)
            {
                entityList.Add(entity);
            }
        }
        _dbSet.RemoveRange(entityList);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public virtual async Task DeleteAsync(TDTO entityVM)
    {
        var entity = entityVM
            .MapTo<TEntity>(_mapper);
        _dbSet.Remove(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public virtual async Task DeleteListAsync(List<TDTO> entityListVM)
    {
        var entityList = entityListVM
            .MapTo<List<TEntity>>(_mapper);
        _dbSet.RemoveRange(entityList);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    public virtual async Task PatchAsync(PatchDTO patchDTO)
    {
        var entity = await _dbSet
            .FindAsync(patchDTO.Id)
            .ConfigureAwait(false);
        if (entity != null)
        {
            var a = entity.GetType();
            patchDTO.PatchModel.ApplyTo(entity);
            _dbSet.Update(entity);
            await _dbContext
                .SaveChangesAsync()
                .ConfigureAwait(false);
        }
    }

    public virtual async Task PatchListAsync(List<PatchDTO> patchListVM)
    {
        foreach (var item in patchListVM)
        {
            var entity = await _dbSet
                .FindAsync(item.Id)
                .ConfigureAwait(false);
            if (entity != null)
            {
                item.PatchModel.ApplyTo(entity);
                _dbSet.Update(entity);
            }
        }
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }

    protected void Validate(TDTO viewModel, IValidator<TDTO> _validator)
    {
        var result = _validator.Validate(viewModel);
        if (!result.IsValid)
        {
            throw new AppException("VLDERR", "Validation Error")
            {
                Validations = AddErrors(result.Errors)
            };
        }
    }

    private List<ValidationError> AddErrors(List<ValidationFailure> failures)
    {
        return failures.Select(x => new ValidationError
        {
            Key = x.PropertyName,
            Value = x.ErrorMessage
        }).ToList();
    }
}