using System.Linq.Expressions;
using Application.Shared.Wrappers;
using Domain.Shared;

namespace Application.Shared.Services;

public interface IBaseService<TDTO, TKey> where TDTO : BaseDTO
{
    Task<PagedResponse<TDTO>> FilterAsync(BaseFilterModel filter, params string[] includeProperties);

    Task<Response<TDTO>> FirstOrDefaultAsync(params string[] includeProperties);

    Task<Response<TDTO>> FirstOrDefaultAsync(Expression<Func<TDTO, bool>> query, params string[] includeProperties);

    Task<Response<TDTO>> FindAsync(Guid id, params string[] includeProperties);

    Task<Response<TDTO>> InsertAsync(TDTO entityVM);

    Task<PagedResponse<TDTO>> InsertListAysnc(List<TDTO> entityListVM);

    Task<Response<TDTO>> UpdateAsync(TDTO entityVM);

    Task UpdateListAsync(List<TDTO> entityListVM);

    Task DeleteAsync(TKey id);

    Task DeleteAsync(TDTO entityVM);

    Task DeleteListAsync(List<TKey> id);

    Task DeleteListAsync(List<TDTO> entityListVM);

    Task PatchAsync(PatchDTO patchVM);

    Task PatchListAsync(List<PatchDTO> patchListVM);
}