using Application.Account.DTOs;
using Application.Shared.Data;
using Application.Shared.Extensions;
using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;

namespace Application.Shared;

public class WorkContext(
    IHttpContextAccessor httpContextAccessor,
    IMapper mapper,
    IApplicationDbContext dbContext)
    : IWorkContext
{
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
    private readonly IMapper _mapper = mapper;
    private readonly IApplicationDbContext _dbContext = dbContext;

    public bool IsAuthenticated => _httpContextAccessor?.HttpContext?.User?.Identity?.IsAuthenticated == true;

    public async Task<UserDTO> GetUserAsync()
    {
        if (_httpContextAccessor?.HttpContext?.User?.Identity?.IsAuthenticated == true)
        {
            var userName = _httpContextAccessor.HttpContext.User.Identity.Name;
            if (_httpContextAccessor.HttpContext.Items["users." + userName] != null)
            {
                return _httpContextAccessor.HttpContext.Items["users." + userName] as UserDTO;
            }
            var user = await _dbContext.Users.AsQueryable().Including("UserRole.Role").FirstOrDefaultAsync(x => x.UserName == userName);
            if (user != null)
            {
                var userDTO = _mapper.Map<UserDTO>(user);
                _httpContextAccessor.HttpContext.Items["users." + userName] = user;
                return userDTO;
            }
            return new UserDTO();
        }
        return new UserDTO();
    }
}