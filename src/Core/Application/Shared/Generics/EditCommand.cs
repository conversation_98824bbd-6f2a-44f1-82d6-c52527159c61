using Application.Shared.Data;
using Application.Shared.Extensions;
using Application.Shared.Wrappers;
using AutoMapper;
using Domain.Shared;
using MediatR;

namespace Application.Shared.Generics;

public record EditCommand<TEntityDTO, TEntity>(TEntityDTO EntityDTO) : IRequest<Response<TEntityDTO>>;

public class EditCommandHandler<TCommand, TEntityDTO, TEntity>(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : IRequestHandler<TCommand, Response<TEntityDTO>> where TEntity : class, IBaseEntity where TCommand : EditCommand<TEntityDTO, TEntity>
{
    private readonly IMapper _mapper = mapper;
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<TEntityDTO>> Handle(TCommand request, CancellationToken cancellationToken)
    {
        var entity = _mapper.Map<TEntity>(request.EntityDTO);
        _dbContext.Set<TEntity>().Update(entity);
        await _dbContext.SaveChangesAsync(cancellationToken);
        var result = _mapper.Map<TEntityDTO>(entity);
        return result.ToResponse();
    }
}