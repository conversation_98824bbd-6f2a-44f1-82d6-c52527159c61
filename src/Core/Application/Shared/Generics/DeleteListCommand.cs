using Application.Shared.Data;
using AutoMapper;
using Domain.Shared;
using MediatR;

namespace Application.Shared.Generics;

public record DeleteListCommand<TEntityDTO, TEntity>(List<TEntityDTO> ListEntityDTO) : IRequest;

public class DeleteListCommandHandler<TCommand, TEntityDTO, TEntity>(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : IRequestHandler<TCommand> where TEntity : class, IBaseEntity where TCommand : DeleteListCommand<TEntityDTO, TEntity>
{
    private readonly IMapper _mapper = mapper;
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task Handle(TCommand request, CancellationToken cancellationToken)
    {
        var entityList = _mapper.Map<List<TEntity>>(request.ListEntityDTO);
        _dbContext.Set<TEntity>().RemoveRange(entityList);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}

public record DeleteByIdListCommand<Object, TEntity>(List<Object> ListEntityDTO) : IRequest;

public class DeleteByIdListCommandHandler<TCommand, Object, TEntity>(
    IApplicationDbContext dbContext)
    : IRequestHandler<TCommand> where TEntity : class, IBaseEntity where TCommand : DeleteByIdListCommand<Object, TEntity>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task Handle(TCommand request, CancellationToken cancellationToken)
    {
        var entityList = new List<TEntity>();
        foreach (var id in request.ListEntityDTO)
        {
            var entity = await _dbContext.Set<TEntity>()
                .FindAsync(id)
                .ConfigureAwait(false);
            if (entity != null)
            {
                entityList.Add(entity);
            }
        }
        _dbContext.Set<TEntity>().RemoveRange(entityList);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}