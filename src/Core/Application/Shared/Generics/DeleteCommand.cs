using Application.Shared.Data;
using AutoMapper;
using Domain.Shared;
using MediatR;

namespace Application.Shared.Generics;

public record DeleteCommand<TEntityDTO, TEntity>(TEntityDTO EntityDTO) : IRequest;

public class DeleteCommandHandler<TCommand, TEntityDTO, TEntity>(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : IRequestHandler<TCommand> where TEntity : class, IBaseEntity where TCommand : DeleteCommand<TEntityDTO, TEntity>
{
    private readonly IMapper _mapper = mapper;
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task Handle(TCommand request, CancellationToken cancellationToken)
    {
        var entity = _mapper.Map<TEntity>(request.EntityDTO);
        _dbContext.Set<TEntity>().Remove(entity);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}

public record DeleteByIdCommand<Object, TEntity>(Object Id) : IRequest;

public class DeleteByIdCommandHandler<TCommand, Object, TEntity>(
    IApplicationDbContext dbContext)
    : IRequestHandler<TCommand> where TEntity : class, IBaseEntity where TCommand : DeleteByIdCommand<Object, TEntity>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task Handle(TCommand request, CancellationToken cancellationToken)
    {
        var entity = await _dbContext.Set<TEntity>().FindAsync([request.Id], cancellationToken: cancellationToken);
        _dbContext.Set<TEntity>().Remove(entity);
        await _dbContext.SaveChangesAsync(cancellationToken);
    }
}