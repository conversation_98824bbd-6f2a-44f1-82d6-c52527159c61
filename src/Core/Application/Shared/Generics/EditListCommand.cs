using Application.Shared.Data;
using Application.Shared.Extensions;
using Application.Shared.Wrappers;
using AutoMapper;
using Domain.Shared;
using MediatR;

namespace Application.Shared.Generics;

public record EditListCommand<TEntityDTO, TEntity>(List<TEntityDTO> ListEntityDTO) : IRequest<Response<List<TEntityDTO>>>;

public class EditListCommandHandler<TCommand, TEntityDTO, TEntity>(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : IRequestHandler<TCommand, Response<List<TEntityDTO>>> where TEntity : class, IBaseEntity where TCommand : EditListCommand<TEntityDTO, TEntity>
{
    private readonly IMapper _mapper = mapper;
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task<Response<List<TEntityDTO>>> Handle(TCommand request, CancellationToken cancellationToken)
    {
        var entityList = _mapper.Map<List<TEntity>>(request.ListEntityDTO);
        _dbContext.Set<TEntity>().UpdateRange(entityList);
        await _dbContext.SaveChangesAsync(cancellationToken);
        var result = _mapper.Map<List<TEntityDTO>>(entityList);
        return result.ToResponse();
    }
}