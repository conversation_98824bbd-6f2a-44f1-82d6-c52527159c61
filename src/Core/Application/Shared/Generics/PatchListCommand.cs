using Application.Shared.Data;
using Domain.Shared;
using MediatR;

namespace Application.Shared.Generics;

public record PatchListCommand<TEntity>(List<PatchDTO> ListPatchDTO) : IRequest;

public class PatchListCommandHandler<TCommand, TEntity>(
    IApplicationDbContext dbContext)
    : IRequestHandler<TCommand> where TEntity : class, IBaseEntity where TCommand : PatchListCommand<TEntity>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task Handle(TCommand request, CancellationToken cancellationToken)
    {
        foreach (var item in request.ListPatchDTO)
        {
            var entity = await _dbContext
                .Set<TEntity>()
                .FindAsync([item.Id], cancellationToken: cancellationToken)
                .ConfigureAwait(false);
            if (entity != null)
            {
                item.PatchModel.ApplyTo(entity);
                _dbContext.Set<TEntity>().Update(entity);
            }
        }
        await _dbContext
            .SaveChangesAsync(cancellationToken)
            .ConfigureAwait(false);
    }
}