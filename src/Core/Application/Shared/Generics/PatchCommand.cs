using Application.Shared.Data;
using Domain.Shared;
using MediatR;

namespace Application.Shared.Generics;

public record PatchCommand<TEntity>(PatchDTO PatchEntityDTO) : IRequest;

public class PatchCommandHandler<TCommand, TEntity>(
    IApplicationDbContext dbContext)
    : IRequestHandler<TCommand> where TEntity : class, IBaseEntity where TCommand : PatchCommand<TEntity>
{
    private readonly IApplicationDbContext _dbContext = dbContext;

    public async Task Handle(TCommand request, CancellationToken cancellationToken)
    {
        var entity = await _dbContext
            .Set<TEntity>()
            .FindAsync([request.PatchEntityDTO.Id], cancellationToken: cancellationToken)
            .ConfigureAwait(false);
        if (entity != null)
        {
            var a = entity.GetType();
            request.PatchEntityDTO.PatchModel.ApplyTo(entity);
            _dbContext.Set<TEntity>().Update(entity);
            await _dbContext
                .SaveChangesAsync(cancellationToken)
                .ConfigureAwait(false);
        }
    }
}