using Domain.Shared;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Application.Shared;

public sealed class IntegrationEventProcessorJob(
    InMemoryMessageQueue queue,
    IServiceScopeFactory serviceScopeFactory
    //ILogger<IntegrationEventProcessorJob> logger
    ) : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        await foreach (IIntegrationEvent integrationEvent in
            queue.Reader.ReadAllAsync(stoppingToken))
        {
            try
            {
                using IServiceScope scope = serviceScopeFactory.CreateScope();
                IPublisher publisher = scope.ServiceProvider
                    .GetRequiredService<IPublisher>();
                await publisher.Publish(integrationEvent, stoppingToken);
            }
            catch (Exception ex)
            {
                // logger.LogError(
                //     ex,
                //     "Something went wrong! {IntegrationEventId}",
                //     integrationEvent.Id);
            }
        }
    }
}