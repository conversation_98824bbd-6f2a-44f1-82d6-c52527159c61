using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Stations;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;
using Application.Shared.Data;

namespace Application.Stations.Services;

public class VehicleService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<Vehicle, VehicleDTO, Guid>(mapper, dbContext), IVehicleService
{
    public async Task<PagedResponse<VehicleDTO>> FilterAsync(VehicleFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        }
        if (filter.DriverId.HasValue)
        {
            filteredData = filteredData.Where(x => x.DriverId == filter.DriverId);
        }
        if (filter.PompTypeId.HasValue)
        {
            filteredData = filteredData.Where(x => x.PompTypeId == filter.PompTypeId);
        }
        if (filter.VehicleId.HasValue)
        {
            filteredData = filteredData.Where(x => x.Id == filter.VehicleId);
        }
        if (filter.VehicleTypeId.HasValue)
        {
            filteredData = filteredData.Where(x => x.VehicleTypeId == filter.VehicleTypeId);
        }
        if (!string.IsNullOrWhiteSpace(filter.Plate))
        {
            filteredData = filteredData.Where(x => x.Plate.Contains(filter.Plate));
        }
        if (filter.Status.HasValue)
        {
            filteredData = filteredData.Where(x => x.StatusId == filter.Status);
        }
        
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<VehicleDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public Task<List<VehicleStatusDTO>> GetVehicleStatusListAsync()
    {
        return _dbContext.VehicleStatus
            .AsQueryable()
            .Select(x => new VehicleStatusDTO
            {
                Id = x.Id,
                Name = x.Name
            })
            .ToListAsync();
    }
}