using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Stations;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;
using Application.Shared.Data;

namespace Application.Stations.Services;

public class StationService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<Station, StationDTO, Guid>(mapper, dbContext), IStationService
{
    public async Task<PagedResponse<StationDTO>> FilterAsync(StationFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        }
        if (filter.Status.HasValue)
        {
            filteredData = filteredData.Where(x => x.StatusId == filter.Status);
        }

        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<StationDTO>>(list);
        return data.ToPagedResponse(filter);
    }

    public Task<List<StationStatusDTO>> GetStationStatusListAsync()
    {
        return _dbContext.StationStatus
            .AsQueryable()
            .Select(x => new StationStatusDTO
            {
                Id = x.Id,
                Name = x.Name,
                DisplayName = x.DisplayName
            })
            .ToListAsync();
    }

    public override async Task<Response<StationDTO>> UpdateAsync(StationDTO entityVM)
    {
        var entity = await _dbSet
            .AsQueryable()
            .Include("StationDepartment")
            .FirstOrDefaultAsync(x => x.Id == entityVM.Id)
            ?? throw new Exception("Kayıt bulunamadı");
        entity.Name = entityVM.Name;
        entity.Description = entityVM.Description;
        entity.Address = entityVM.Address;
        entity.Latitude = entityVM.Latitude;
        entity.Longitude = entityVM.Longitude;
        entity.Capacity = entityVM.Capacity;
        entity.StatusId = entityVM.StatusId;
        foreach (var item in entity.StationDepartment.Where(k => !entityVM.StationDepartment.Any(l => l.DepartmentId == k.DepartmentId)).ToList())
        {
            _dbContext.StationDepartment.Remove(item);
        }
        entityVM.StationDepartment ??= [];
        foreach (var item in entityVM.StationDepartment.Where(k => !entity.StationDepartment.Any(l => l.DepartmentId == k.DepartmentId)).ToList())
        {
            _dbContext.StationDepartment.Add(item.MapTo<StationDepartment>(_mapper));
        }
        var local = _dbSet.Update(entity);
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
        var data = local.Entity.MapTo<StationDTO>(_mapper);
        return data.ToResponse();
    }

    public override async Task UpdateListAsync(List<StationDTO> entityListVM)
    {
        var idList = entityListVM.Select(x => x.Id).ToList();
        var entityList = await _dbSet
            .AsQueryable()
            .Include("StationDepartment")
            .Where(x => idList.Contains(x.Id))
            .ToListAsync();
        foreach (var entity in entityList)
        {
            var entityVM = entityListVM.First(x => x.Id == entity.Id);
            entity.Name = entityVM.Name;
            entity.Description = entityVM.Description;
            entity.Address = entityVM.Address;
            entity.Latitude = entityVM.Latitude;
            entity.Longitude = entityVM.Longitude;
            entity.Capacity = entityVM.Capacity;
            entity.StatusId = entityVM.StatusId;
            foreach (var item in entity.StationDepartment.Where(k => !entityVM.StationDepartment.Any(l => l.DepartmentId == k.DepartmentId)).ToList())
            {
                entity.StationDepartment.Remove(item);
            }
            entityVM.StationDepartment ??= [];
            foreach (var item in entityVM.StationDepartment.Where(k => !entity.StationDepartment.Any(l => l.DepartmentId == k.DepartmentId)).ToList())
            {
                entity.StationDepartment.Add(item.MapTo<StationDepartment>(_mapper));
            }
            var local = _dbSet.Update(entity);
        }
        await _dbContext
            .SaveChangesAsync()
            .ConfigureAwait(false);
    }
}