using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Domain.Shared;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Stations;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;
using Application.Shared.Data;

namespace Application.Stations.Services;

public class DepartmentService(
    IMapper mapper,
    IApplicationDbContext dbContext
) : BaseService<Department, DepartmentDTO, Guid>(mapper, dbContext), IDepartmentService
{

    public async Task<PagedResponse<DepartmentDTO>> FilterAsync(DepartmentFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        // if (!string.IsNullOrWhiteSpace(filter.Name))
        // {
        //     filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        // }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<DepartmentDTO>>(list);
        return data.ToPagedResponse(filter);
    }
}