using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Stations;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;
using Application.Shared.Data;

namespace Application.Stations.Services;

public class VehicleTypeService(
    IMapper mapper,
    IApplicationDbContext dbContext
) : BaseService<VehicleType, VehicleTypeDTO, int>(mapper, dbContext), IVehicleTypeService
{

    public async Task<PagedResponse<VehicleTypeDTO>> FilterAsync(VehicleTypeFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (!string.IsNullOrWhiteSpace(filter.Name))
        {
            filteredData = filteredData.Where(x => x.Name.Contains(filter.Name));
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<VehicleTypeDTO>>(list);
        return data.ToPagedResponse(filter);
    }
}