using AutoMapper;
using Microsoft.EntityFrameworkCore;
using Domain.Shared;
using Application.Shared.Extensions;
using Application.Shared.Services;
using Application.Shared.Wrappers;
using Domain.Stations;
using Application.Stations.DTOs;
using Application.Stations.FilterModel;
using Application.Shared.Data;

namespace Application.Stations.Services;

public class StationDepartmentService(
    IMapper mapper,
    IApplicationDbContext dbContext)
    : BaseService<StationDepartment, StationDepartmentDTO, Guid>(mapper, dbContext), IStationDepartmentService
{
    public async Task<PagedResponse<StationDepartmentDTO>> FilterAsync(StationDepartmentFilterModel filter)
    {
        var allData = _dbSet.AsQueryable();
        var filteredData = allData;
        if (filter.DepartmentId.HasValue)
        {
            filteredData = filteredData.Where(x => x.DepartmentId == filter.DepartmentId.Value);
        }
        if (filter.StationId.HasValue)
        {
            filteredData = filteredData.Where(x => x.StationId == filter.StationId.Value);
        }
        filter.Count = allData.Count();
        filter.FilteredCount = filteredData.Count();
        var list = await filteredData
            .Sorting(filter.SortProperty, filter.SortType)
            .Paging(filter.PageIndex, filter.PageSize)
            .Including(BaseIncludes, filter.IncludeProperties)
            .Select(filter.SelectFields)
            .ToListAsync()
            .ConfigureAwait(false);
        var data = _mapper.Map<List<StationDepartmentDTO>>(list);
        return data.ToPagedResponse(filter);
    }
}