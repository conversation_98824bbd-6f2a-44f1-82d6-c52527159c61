using Domain.Shared;

namespace Application.Stations.DTOs;

public class StationDTO : BaseDTO
{
    public Guid Id { get; set; }
    public required string Name { get; set; }
    public string? Description { get; set; }
    public required string Address { get; set; }
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public int Capacity { get; set; }
    public int StatusId { get; set; }
    public StationStatusDTO? Status { get; set; }
    public DateTime InsertDate { get; set; }
    public Guid? InsertUserId { get; set; }
    public string? Color { get; set; }
    public List<StationDepartmentDTO>? StationDepartment { get; set; }
}