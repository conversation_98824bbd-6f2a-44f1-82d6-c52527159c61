using Domain.Shared;

namespace Application.Stations.DTOs;

public class VehicleDTO : BaseDTO
{
    public Guid Id { get; set; }
    public required string Name { get; set; }
    public required string Plate { get; set; }
    public string? Description { get; set; }
    public decimal? Capacity { get; set; }
    public int StatusId { get; set; }
    public VehicleStatusDTO? Status { get; set; }
    public DateTime InsertDate { get; set; }
    public Guid? InsertUserId { get; set; }
    public bool IsDeleted { get; set; }
    public Guid DriverId { get; set; }
    public string? DriverName { get; set; }
    public int? PompTypeId { get; set; }
    public PompTypeDTO? PompType { get; set; }
    public int VehicleTypeId { get; set; }
    public VehicleTypeDTO? VehicleType { get; set; }
}