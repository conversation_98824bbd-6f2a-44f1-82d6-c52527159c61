using Domain.Shared;

namespace Application.Stations.FilterModel;

public class VehicleFilterModel : BaseFilterModel
{
    public string? Name { get; set; }
    public Guid? DriverId { get; set; }
    public int? PompTypeId { get; set; }
    public int? VehicleTypeId { get; set; }
    public Guid? VehicleId { get; set; }
    public string? Plate { get; set; }
    public int? Status { get; set; } // 0 - Pasif, 1 - Aktif
}