using Application.Stations.DTOs;
using AutoMapper;
using Domain.Stations;

namespace Application.General.Mapping;

public class StationMapping : Profile
{
    public StationMapping()
    {
        CreateMap<Department, DepartmentDTO>().ReverseMap();
        CreateMap<Station, StationDTO>().ReverseMap();
        CreateMap<StationStatus, StationStatusDTO>().ReverseMap();
        CreateMap<Vehicle, VehicleDTO>().ReverseMap();
        CreateMap<VehicleStatus, VehicleStatusDTO>().ReverseMap();
        CreateMap<PompType, PompTypeDTO>().ReverseMap();
        CreateMap<VehicleType, VehicleTypeDTO>().ReverseMap();
        CreateMap<StationDepartment, StationDepartmentDTO>().ReverseMap();
    }
}