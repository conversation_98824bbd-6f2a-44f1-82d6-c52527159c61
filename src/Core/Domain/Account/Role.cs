using Domain.Shared;
using Microsoft.AspNetCore.Identity;

namespace Domain.Account;

public class Role : IdentityRole<Guid>, IBaseEntity
{
    public List<UserRole>? UserRole { get; set; }
    public static Guid ADMIN => new("439aeada-02d0-4962-9dfd-bc41363461a3");
    public static Guid SUPERADMIN => new("0f2a72f5-7dcf-4301-9240-ab806d804007");
    public static Guid SALES => new("f6bcd1b9-328e-4008-9da2-2ce37f35940e");
    public static Guid STATION => new("5351cc0b-20d7-4dcc-8fba-90c797b3f5b8");
    public static Guid DRIVER => new("98b3e5cc-cf74-4ee9-bc6a-6871c51a6469");
    public static Guid COMPANY => new("aaaa544b-b5f7-4bad-8c68-22c87005bfac");
    public static Guid BUILDING => new("44a832a5-fbe2-43eb-8c27-0e5050abd9ea");
    public static Guid CONTROL => new("734354f3-c622-4750-a11c-04ab6f0fa497");
    public static Guid MOULDER => new("d9530b95-6cc4-4e13-8225-6d65d5b1e617");
}