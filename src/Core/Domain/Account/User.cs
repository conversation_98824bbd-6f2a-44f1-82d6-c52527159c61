using Domain.Companies;
using Domain.Shared;
using Domain.Stations;
using Domain.Transactions;
using Microsoft.AspNetCore.Identity;

namespace Domain.Account;

public class User : IdentityUser<Guid>, IBaseEntity
{
    public string? Name { get; set; }
    public string? Surname { get; set; }
    public bool Active { get; set; }
    public bool IsMaster { get; set; }
    public bool IsDeleted { get; set; }
    public Guid? CompanyId { get; set; }
    public Company? Company { get; set; }
    public string? CompanyName { get; set; }
    public string? TaxNumber { get; set; }
    public bool Kvkk { get; set; }
    public string? FCMDeviceId { get; set; }
    public DateTime? InsertDate { get; set; }
    public Guid? RoleId => UserRole?.FirstOrDefault()?.RoleId;
    public Role? Role => UserRole?.FirstOrDefault()?.Role;
    public List<UserRole>? UserRole { get; set; }
    public List<UserDepartment>? UserDepartment { get; set; }
    public List<TransactionRequest>? TransactionRequest { get; set; }
    public List<BuildingUser>? BuildingUser { get; set; }
    public List<Vehicle>? Vehicle { get; set; }
}