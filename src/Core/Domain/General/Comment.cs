using Domain.Account;
using Domain.Contracts;
using Domain.Shared;
using Domain.Stations;

namespace Domain.General;

public class Comment : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public DateTime InsertDate { get; set; }
    public string? Text { get; set; }
    public decimal Rate { get; set; }
    public Guid CommenterId { get; set; }
    public User? Commenter { get; set; }
    public Guid ContractProductTransactionId { get; set; }
    public ContractProductTransaction? ContractProductTransaction { get; set; }
    public Guid VehicleId { get; set; }
    public Vehicle? Vehicle { get; set; }
    public Guid DriverId { get; set; }
    public User? Driver { get; set; }
    public virtual List<CommentFile>? CommentFile { get; set; }
}