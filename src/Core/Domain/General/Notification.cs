using Domain.Account;
using Domain.Shared;

namespace Domain.General;

public class Notification : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public string? Title { get; set; }
    public string? Note { get; set; }
    public string? Type { get; set; }
    public string? Data { get; set; }
    public Guid UserId { get; set; }
    public User? User { get; set; }
    public bool IsSend { get; set; }
    public bool IsRead { get; set; }
    public Guid? InsertUserId { get; set; }
    public DateTime InsertDate { get; set; }
}