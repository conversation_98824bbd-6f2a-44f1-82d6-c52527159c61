using System.ComponentModel.DataAnnotations.Schema;
using Domain.Shared;

namespace Domain.General;

public class Media : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public string? Name { get; set; }

    public string? FileName { get; set; }

    public int Line { get; set; }

    public string? ContentType { get; set; }

    public long Size { get; set; }

    public string? Path { get; set; }
    
    [NotMapped]
    public string FullPath
    {
        get
        {
            return (string.IsNullOrWhiteSpace(Path) || Path == "/" ? "/" : Path + "/").TrimStart('/') + FileName;
        }
    }

    public int? Width { get; set; }

    public int? Height { get; set; }
    
    public int? MediaType { get; set; } // 0: Image, 1: Video
}