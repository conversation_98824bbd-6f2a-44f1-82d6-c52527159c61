using Domain.Shared;

namespace Domain.Companies;

public class Company : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public bool Active { get; set; }
    public string? Name { get; set; }
    public string? Email { get; set; }
    public string? Phone { get; set; }
    public string? TaxOffice { get; set; }
    public string? TaxNumber { get; set; }
    public string? Address { get; set; }
    public string? Description { get; set; }
    public string? AccountingCode { get; set; }
    public string? Color
    {
        get
        {
            return string.Concat("#", Id.ToString().AsSpan(0, 6));
        }
    }
    public bool IsDeleted { get; set; }
    public List<BuildingUser>? CompanyUser { get; set; }
}