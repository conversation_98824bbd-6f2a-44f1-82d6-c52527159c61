using Domain.Account;
using Domain.Contracts;
using Domain.General;
using Domain.Shared;
using Domain.Stations;
using Domain.Transactions;

namespace Domain.Companies;

public class Building : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public bool Active { get; set; }
    public required string Name { get; set; }
    public Guid CompanyId { get; set; }
    public Company? Company { get; set; }
    public string? Address { get; set; }
    public Guid? StateProvinceId { get; set; }
    public StateProvince? StateProvince { get; set; }
    public Guid? DistrictId { get; set; }
    public District? District { get; set; }
    public Guid? CityId { get; set; }
    public City? City { get; set; }
    public bool IsDeleted { get; set; }
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public Guid? StationId { get; set; }
    public Station? Station { get; set; }
    public string? Color
    {
        get
        {
            return string.Concat("#", Id.ToString().AsSpan(0, 6));
        }
    }
    public List<TransactionRequest>? TransactionRequest { get; set; }
    public List<BuildingUser>? BuildingUser { get; set; }
    public List<ContractProductBuilding>? ContractProductBuilding { get; set; }
}