using Domain.Shared;
using Domain.Companies;
using Domain.Transactions;
using Domain.Account;

namespace Domain.Contracts;

public class Contract : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public Guid CompanyId { get; set; }
    public Company? Company { get; set; }
    public bool Active { get; set; }
    public required string Title { get; set; }
    public string? Description { get; set; }
    public DateTime InsertDate { get; set; }
    public Guid InsertUserId { get; set; }
    public DateTime? UpdateDate { get; set; }
    public Guid? UpdatedUserId { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int? PaymentPlanId { get; set; }
    public PaymentPlan? PaymentPlan { get; set; }
    public bool? IsApproved { get; set; }
    public bool IsDeleted { get; set; }
    public DateTime? ApprovedDateTime { get; set; }
    public Guid? ApprovedUserId { get; set; }
    public User? ApprovedUser { get; set; }
    public string? ApprovedNote { get; set; }
    public decimal? TotalWorth { get; set; }
    public bool? IncludeTax { get; set; }
    public decimal? LeftWorth { get; set; }
    public decimal? OldWorth { get; set; }
    public decimal? TotalAmount { get; set; }
    public decimal? LeftAmount { get; set; }
    public decimal? OldAmount { get; set; }
    public List<TransactionRequest>? TransactionRequest { get; set; }
    public List<ContractProduct>? ContractProduct { get; set; }
    public List<ContractFile>? ContractFile { get; set; }
}