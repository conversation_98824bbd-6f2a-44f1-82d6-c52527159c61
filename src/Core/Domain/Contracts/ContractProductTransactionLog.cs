using System.ComponentModel.DataAnnotations.Schema;
using Domain.Shared;

namespace Domain.Contracts;

public class ContractProductTransactionLog : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public DateTime InsertDate { get; set; }
    public Guid ContractProductTransactionId { get; set; }
    public ContractProductTransaction? ContractProductTransaction { get; set; }
    public required string Description { get; set; }
}