using System.ComponentModel.DataAnnotations.Schema;
using Domain.Account;
using Domain.Shared;
using Domain.Stations;
using Domain.Transactions;

namespace Domain.Contracts;

public class ContractProductTransaction : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public Guid? ContractId { get; set; }
    public Contract? Contract { get; set; }
    public Guid TransactionRequestId { get; set; }
    public TransactionRequest? TransactionRequest { get; set; }
    public Guid VehicleId { get; set; }
    public Vehicle? Vehicle { get; set; }
    public Guid DriverId { get; set; }
    public User? Driver { get; set; }
    public DateTime InsertDate { get; set; }
    public Guid? InsertUserId { get; set; }
    public int StatusId { get; set; }
    public ContractProductTransactionStatus? Status { get; set; }
    public decimal SendingAmount { get; set; }
    public string? DocumentNo { get; set; }
    public Guid? StationId { get; set; }
    public Station? Station { get; set; }
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public int TypeId { get; set; }
    public TransactionType? Type { get; set; }
    public bool IsRedirect { get; set; }
    public Guid? CanceledUserId { get; set; }
    public User? CanceledUser { get; set; }
    public string? CanceledNote { get; set; }
    public decimal? RefundAmount { get; set; }
    public int? PompTypeId { get; set; }
    public PompType? PompType { get; set; }
    [NotMapped]
    public TimeSpan? TotalTime
    {
        get
        {
            return EndDate - StartDate;
        }
    }
    [NotMapped]
    public string StatusMessage
    {
        get
        {
            var description = (ContractProductTransactionStatusEnum)StatusId switch
            {
                ContractProductTransactionStatusEnum.Pending => "Araç Doluyor",
                ContractProductTransactionStatusEnum.OnRoad => "Araç Yola Çıktı",
                ContractProductTransactionStatusEnum.InProgress => "Döküm Başladı",
                ContractProductTransactionStatusEnum.Finished => "Döküm Tamamlandı",
                ContractProductTransactionStatusEnum.Cancel => "İptal Edildi",
                _ => ""
            };
            return description + ", Plaka: " + Vehicle?.Plate;
        }
    }
}