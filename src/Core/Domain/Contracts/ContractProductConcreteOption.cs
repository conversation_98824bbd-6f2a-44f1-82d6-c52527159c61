using Domain.Companies;
using Domain.Shared;
using Domain.Transactions;

namespace Domain.Contracts;

public class ContractProductConcreteOption : BaseEntity
{
    public Guid ContractProductId { get; set; }
    public ContractProduct? ContractProduct { get; set; }
    public int ConcreteOptionId { get; set; }
    public ConcreteOption? ConcreteOption { get; set; }
    public decimal Price { get; set; }
}