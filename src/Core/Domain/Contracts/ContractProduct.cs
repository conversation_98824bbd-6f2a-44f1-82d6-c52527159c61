using Domain.Catalog;
using Domain.Shared;
using Domain.Transactions;

namespace Domain.Contracts;

public class ContractProduct : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public Guid ContractId { get; set; }
    public Contract? Contract { get; set; }
    public Guid ProductId { get; set; }
    public Product? Product { get; set; }
    public decimal? Amount { get; set; } 
    public decimal? OldAmount { get; set; }
    public decimal? LeftAmount { get; set; } 
    public decimal? TotalPrice { get; set; } 
    public decimal? OldPrice { get; set; }
    public decimal? LeftPrice { get; set; } 
    public decimal Price { get; set; }
    public decimal? PriceWithoutPlug { get; set; }
    public decimal? PriceUnderPowerPlant { get; set; }
    public bool? IncludeTax { get; set; }
    public DateTime? PriceGuaranteeDate { get; set; }
    public List<ContractProductConsistencyClass>? ContractProductConsistencyClass { get; set; }
    public List<ContractProductConcreteOption>? ContractProductConcreteOption { get; set; }
    public List<ContractProductBuilding>? ContractProductBuilding { get; set; }
}