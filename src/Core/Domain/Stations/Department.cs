using Domain.Account;
using Domain.Shared;

namespace Domain.Stations;

public class Department : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public required string Name { get; set; }
    public required string PhoneNumber { get; set; }
    public bool IsDeleted { get; set; }
    public bool IsBase { get; set; }
    public List<UserDepartment>? UserDepartment { get; set; }
    public List<StationDepartment>? StationDepartment { get; set; }
}