using Domain.Shared;

namespace Domain.Stations;

public class Station : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public required string Name { get; set; }
    public string? Description { get; set; }
    public required string Address { get; set; }
    public decimal Latitude { get; set; }
    public decimal Longitude { get; set; }
    public int Capacity { get; set; }
    public int StatusId { get; set; }
    public StationStatus? Status { get; set; }
    public DateTime InsertDate { get; set; }
    public Guid? InsertUserId { get; set; }
    public bool IsDeleted { get; set; }
    public string? Color
    {
        get
        {
            return string.Concat("#", Id.ToString().AsSpan(0, 6));
        }
    }
    public List<StationDepartment>? StationDepartment { get; set; }
}