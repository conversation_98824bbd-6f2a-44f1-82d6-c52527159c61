using Domain.Account;
using Domain.Shared;

namespace Domain.Stations;

public class Vehicle : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public required string Name { get; set; }
    public required string Plate { get; set; }
    public string? Description { get; set; }
    public decimal? Capacity { get; set; }
    public int StatusId { get; set; }
    public VehicleStatus? Status { get; set; }
    public DateTime InsertDate { get; set; }
    public Guid? InsertUserId { get; set; }
    public bool IsDeleted { get; set; }
    public Guid DriverId { get; set; }
    public User? Driver { get; set; }
    public string? DriverName
    {
        get
        {
            return Driver?.Name + " " + Driver?.Surname;
        }
    }
    public int? PompTypeId { get; set; }
    public PompType? PompType { get; set; }
    public int VehicleTypeId { get; set; }
    public VehicleType? VehicleType { get; set; }
}