using Domain.Account;
using Domain.Catalog;
using Domain.Companies;
using Domain.Contracts;
using Domain.Shared;
using Domain.Stations;

namespace Domain.Transactions;

public class TransactionRequest : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public Guid BuildingId { get; set; }
    public Building? Building { get; set; }
    public Guid? ContractId { get; set; }
    public Contract? Contract { get; set; }
    public Guid? ProductId { get; set; }
    public Product? Product { get; set; }
    public List<TransactionRequestConcreteOption>? TransactionRequestConcreteOption { get; set; }
    public List<TransactionRequestPompType>? TransactionRequestPompType { get; set; }
    
    // public int? ConcreteOptionId { get; set; }
    // public ConcreteOption? ConcreteOption { get; set; }
    public int? ConcreteLocationId { get; set; }
    public ConcreteLocation? ConcreteLocation { get; set; }
    public int? ConsistencyClassId { get; set; }
    public ConsistencyClass? ConsistencyClass { get; set; }
    public int StatusId { get; set; }
    public TransactionStatus? Status { get; set; }
    public Guid RequestedPersonId { get; set; }
    public User? RequestedPerson { get; set; }
    public DateTime DesiredDateTime { get; set; }
    public int? CarTypeId { get; set; }
    public VehicleType? CarType { get; set; }
    public DateTime? TransactionStartDateTime { get; set; }
    public DateTime? TransactionEndDateTime { get; set; }
    public DateTime? ApprovedDateTime { get; set; }
    public Guid? ApprovedUserId { get; set; }
    public User? ApprovedUser { get; set; }
    public string? ApprovedNote { get; set; }
    public string? Note { get; set; }
    public string? RejectNote { get; set; }
    public DateTime InsertDate { get; set; }
    public bool IsDeleted { get; set; }
    public decimal? DistanceInDestination { get; set; }
    public decimal DesiredTotalConcrete { get; set; }
    public decimal? TotalConcreteRemaining { get; set; }
    public decimal? TotalConcreteSent { get; set; }
    public decimal? TotalConcreteRefundable { get; set; }
    public Guid? CanceledUserId { get; set; }
    public User? CanceledUser { get; set; }
    public string? CanceledNote { get; set; }
    public string? ErpOrderNo { get; set; }
    public int PriceTypeId { get; set; } // 1: Fişli, 2: Fişsiz, 3: Bantaltı
    public int TransactionrequestTypeId { get; set; }
    public TransactionRequestType? TransactionrequestType { get; set; }
    public Guid? StationId { get; set; }
    public Station? Station { get; set; }
    public List<LabResult>? LabResult { get; set; }
    public List<ContractProductTransaction>? ContractProductTransaction { get; set; }
}