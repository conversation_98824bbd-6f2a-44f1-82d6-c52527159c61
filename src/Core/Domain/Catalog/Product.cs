using Domain.Shared;

namespace Domain.Catalog;

public class Product : BaseEntity, IBaseEntity
{
    public Guid Id { get; set; }
    public required string Name { get; set; }
    public required string Description { get; set; }
    public bool Refundable { get; set; }
    public decimal RefundableAmount { get; set; }
    public bool IsDeleted { get; set; }
    public int? Order { get; set; }
    public string? Color
    {
        get
        {
            return string.Concat("#", Id.ToString().AsSpan(0, 6));
        }
    }
}