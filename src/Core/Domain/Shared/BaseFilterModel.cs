namespace Domain.Shared
{
    public class BaseFilterModel
    {
        public int Count { get; set; }

        public int FilteredCount { get; set; }

        public int PageIndex { get; set; } = 1;

        public int PageSize { get; set; } = 20;

        public string SortProperty { get; set; } = "Id";

        public string SortType { get; set; } = "asc";

        public string? SelectFields { get; set; }

        public string[] IncludeProperties { get; set; } = [];
    }
}