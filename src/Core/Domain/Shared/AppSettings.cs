namespace Domain.Shared;

public class AppSettings
{
    public Guid Id { get; set; }
    public string? Title { get; set; }
    public int MailPort { get; set; }
    public string? MailServer { get; set; }
    public string? MailUser { get; set; }
    public string? MailPassword { get; set; }
    public bool SslEnabled { get; set; }
    public string? SmsCompanyCode { get; set; }
    public string? SmsUserName { get; set; }
    public string? SmsUserCode { get; set; }
    public string? SmsUserBaslik { get; set; }
    public string? SmsFastLoginMessagePrefix { get; set; }
    public string? SmsFastLoginMessageSuffix { get; set; }
    public string? SmsPhoneConfirmMessagePrefix { get; set; }
    public string? SmsPhoneConfirmMessageSuffix { get; set; }
    public string? WeatherApiKey { get; set; }
    public string? ConnectionStringERP { get; set; }
    public decimal? ContractApproveLimit { get; set; }
    public string? ContractBaseDescription { get; set; }
    public string? LiveTrackingBaseUrl { get; set; }
    public string? LiveTrackingUsername { get; set; }
    public string? LiveTrackingPassword { get; set; }
}