using System.Text.Json.Serialization;
using Microsoft.AspNetCore.JsonPatch;

namespace Domain.Shared;

public class PatchDTO : BaseDTO
{
    public Guid Id { get; set; }
    public List<OperationDTO> Patch { get; set; }
    [JsonIgnore]
    public JsonPatchDocument PatchModel
    {
        get
        {
            var model = new JsonPatchDocument();
            foreach (var item in Patch)
            {
                model.Add(item.Path, item.Value);
            }
            return model;
        }
    }
}

public class OperationDTO
{
    public required string Path { get; set; }
    public required string Value { get; set; }
}