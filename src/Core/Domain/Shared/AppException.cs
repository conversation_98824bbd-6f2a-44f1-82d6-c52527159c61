namespace Domain.Shared;

public class AppException : Exception
{
    public AppException(string? message) : base(message)
    {
    }

    public AppException(string? code, string? message) : base(message)
    {
        Code = code;
    }

    public string? Code { get; set; }
    public List<ValidationError>? Validations { get; set; }
}

public class ValidationError
{
    public required string Key { get; set; }
    public required string Value { get; set; }
}