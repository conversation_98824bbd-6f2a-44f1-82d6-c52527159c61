using Domain.Shared;

namespace Domain.External.DTOs;

public class DocumentDTO : BaseDTO
{
    public string? SubeNo { get; set; }
    public string? SubeAdi { get; set; }
    public string? TesisAdi { get; set; }
    public DateTime? Tarih { get; set; }
    public string? IrsaliyeNo { get; set; }
    public string? CariKodu { get; set; }
    public string? CariAciklama { get; set; }
    public string? ReceteSinifi { get; set; }
    public string? StokKodu { get; set; }
    public string? StokAciklama { get; set; }
    public decimal? IrsaliyeMiktar { get; set; }
    public string? SantiyeAdi { get; set; }
    public string? Plaka { get; set; }
    public string? Surucu { get; set; }
    public string? PompaPlaka { get; set; }
    public string? Pompaci { get; set; }
}