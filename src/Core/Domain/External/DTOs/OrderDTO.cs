using Domain.Shared;

namespace Domain.External.DTOs;

public class OrderDTO : BaseDTO
{
    public string? Santral<PERSON>di { get; set; }
    public string? SiparisNo { get; set; }
    public DateTime SiparisTarihi { get; set; }
    public string? FirmaAdi { get; set; }
    public string? CariKodu { get; set; }
    public string? <PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; }
    public string? Urun<PERSON>di { get; set; }
    public bool HasPomp { get; set; }
    public decimal Istenen { get; set; }
    public decimal? V<PERSON>len { get; set; }
    public decimal? <PERSON><PERSON> { get; set; }
    public string? <PERSON>ciklama { get; set; }
}