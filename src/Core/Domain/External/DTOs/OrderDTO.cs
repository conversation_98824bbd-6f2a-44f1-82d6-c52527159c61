using Domain.Shared;

namespace Domain.External.DTOs;

public class OrderDTO : BaseDTO
{
    public string? <PERSON>ral<PERSON>di { get; set; }
    public string? SiparisNo { get; set; }
    public DateTime SiparisTarihi { get; set; }
    public string? FirmaAdi { get; set; }
    public string? CariKodu { get; set; }
    public string? <PERSON>i<PERSON><PERSON><PERSON> { get; set; }
    public string? UrunAdi { get; set; }
    public bool HasPomp { get; set; }
    public bool HasPowerPlant { get; set; }
    public decimal Istenen { get; set; }
    public decimal? Verilen { get; set; }
    public decimal? <PERSON><PERSON> { get; set; }
    public bool? Belgeli { get; set; }
    public string? Aciklama { get; set; }
}