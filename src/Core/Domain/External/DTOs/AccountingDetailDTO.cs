using Domain.Shared;

namespace Domain.External.DTOs;

public class AccountingDetailDTO : BaseDTO
{
    public int? Miktar { get; set; }
    public float? Tutar { get; set; }
    public float? Vergi { get; set; }
    public string? Aciklama { get; set; }
    public float? Iskonto { get; set; }
    public DateTime? Tarih { get; set; }
    public string? FaturaNo { get; set; }
    public string? Urun<PERSON>di { get; set; }
}