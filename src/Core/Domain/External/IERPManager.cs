using Domain.External.DTOs;

namespace Domain.External;

public interface IERPManager
{
    Task<List<AccountingDTO>> GetAccountingList(string accountingCode, DateTime startDate, DateTime endDate);
    Task<List<AccountingDetailDTO>> GetAccountingDetail(Guid? CariRecNo);
    Task<List<DocumentDTO>> GetDocumentList(string accountingCode, DateTime startDate, DateTime endDate);
    Task<List<DocumentDTO>> GetDocumentDetail(string IrsaliyeNo);
    Task<List<OrderDTO>> GetOrderList();
}