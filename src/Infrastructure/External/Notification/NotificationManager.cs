using System.Text;
using Domain.External;
using Domain.External.DTOs;
using Domain.Shared;
using Google.Apis.Auth.OAuth2;
using Newtonsoft.Json;

namespace External.Notification;

public class NotificationManager()
    : INotificationManager
{
    private static readonly string ServiceAccountFilePath = "./mazakacore-297612-firebase-adminsdk-qao3x-f25d76a483.json";
    private static readonly string FCMUrl = "https://fcm.googleapis.com/v1/projects/mazakacore-297612/messages:send";

    public async Task<bool> SendNotificationAsync(SendNotificationDTO notification)
    {
        var googleCredential = GoogleCredential.FromFile(ServiceAccountFilePath).CreateScoped("https://www.googleapis.com/auth/firebase.messaging");
        var accessToken = await googleCredential.UnderlyingCredential.GetAccessTokenForRequestAsync();
        var client = new HttpClient();
        client.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", $"Bearer {accessToken}");
        var messageInformation = new
        {
            message = new
            {
                token = notification.FCMDeviceId,
                notification = new
                {
                    title = notification.Subject,
                    body = notification.Message
                },
                data = new
                {
                    type = notification.Type,
                    data = notification.Data
                }
            }
        };
        var jsonMessage = JsonConvert.SerializeObject(messageInformation);
        var httpContent = new StringContent(jsonMessage, Encoding.UTF8, "application/json");
        var result = await client.PostAsync(FCMUrl, httpContent);
        string resultContent = await result.Content.ReadAsStringAsync();
        if (result.IsSuccessStatusCode)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
}