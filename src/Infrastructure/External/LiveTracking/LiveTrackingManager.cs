using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using System.Web;
using Domain.External;
using Domain.External.DTOs;
using Domain.Shared;
using Microsoft.Extensions.Caching.Memory;

namespace External.LiveTracking;

public class LiveTrackingManager(
    AppSettings settings,
    IMemoryCache cache)
    : ILiveTrackingManager
{
    private readonly AppSettings _settings = settings;

    private readonly IMemoryCache _cache = cache;

    private readonly HttpClient _client = new();

    private string BaseUrl => _settings.LiveTrackingBaseUrl.TrimEnd('/');

    public async Task<string> GetTokenAsync()
    {
        const string cacheKey = "LiveTrackingToken";

        if (_cache.TryGetValue(cacheKey, out string token))
        {
            return token;
        }

        var requestBody = new
        {
            username = _settings.LiveTrackingUsername,
            password = _settings.LiveTrackingPassword
        };

        var content = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
        var response = await _client.PostAsync($"{BaseUrl}/auth", content);
        var body = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
            throw new Exception("Token alınamadı: " + body);

        var result = JsonSerializer.Deserialize<TokenResponse>(body, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        token = result?.Token ?? throw new Exception("Token null döndü");

        var cacheEntryOptions = new MemoryCacheEntryOptions()
            .SetAbsoluteExpiration(TimeSpan.FromHours(48));

        _cache.Set(cacheKey, token, cacheEntryOptions);

        return token;
    }

    public async Task<LiveTrackingVehicleDTO?> GetVehicleByPlateAsync(string plate)
    {
        var token = await GetTokenAsync();

        _client.DefaultRequestHeaders.Remove("Authorization");
        _client.DefaultRequestHeaders.Add("Authorization", token);

        _client.DefaultRequestHeaders.Accept.Clear();
        _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

        // Plaka formatını düzelt
        plate = FormatPlaka(plate);

        // Plaka URL encode et
        var encodedPlate = HttpUtility.UrlEncode(plate);
        var url = $"{BaseUrl}/vehicles?licensePlate={encodedPlate}&_sortOrder=ASC&_perPage=20&_page=1";

        var response = await _client.GetAsync(url);
        var body = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
            throw new Exception("Araç listesi alınamadı: " + body);

        var vehicles = JsonSerializer.Deserialize<List<LiveTrackingVehicleDTO>>(body, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        var vehicle = vehicles?.FirstOrDefault(v => v.LicensePlate?.Replace(" ", "") == plate.Replace(" ", ""));
        if (vehicle != null)
        {
            vehicle.Token = token;
        }

        return vehicle;
    }

    public async Task<LiveTrackingLatestLocationDTO?> GetLatestLocationAsync(string token, string vehicleId)
    {
        _client.DefaultRequestHeaders.Remove("Authorization");
        _client.DefaultRequestHeaders.Add("Authorization", token);

        _client.DefaultRequestHeaders.Accept.Clear();
        _client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

        var url = $"{BaseUrl}/reports/vehicle/locations/latest?vehicleId={vehicleId}&_perPage=1";

        var response = await _client.GetAsync(url);
        var body = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
            throw new Exception("Son konum alınamadı: " + body);

        var locations = JsonSerializer.Deserialize<List<LiveTrackingLatestLocationDTO>>(body, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        return locations?.FirstOrDefault();
    }
    
    private string FormatPlaka(string plaka)
    {
        // Plaka zaten boşluk içeriyorsa dokunma
        if (plaka.Contains(" "))
            return plaka;

        // Harf grubu: ortada bulunan, sadece harflerden oluşan kısım
        var harfGrubu = new string(plaka.Where(char.IsLetter).ToArray());
        if (string.IsNullOrEmpty(harfGrubu))
            return plaka; // Harf grubu yoksa anlamlı bir plaka değildir, dokunma

        int harfIndex = plaka.IndexOf(harfGrubu);
        if (harfIndex <= 0 || harfIndex + harfGrubu.Length >= plaka.Length)
            return plaka; // Güvenlik kontrolü: plaka doğru yapıda değilse dokunma

        string ilKodu = plaka.Substring(0, harfIndex);
        string harfler = harfGrubu;
        string numara = plaka.Substring(harfIndex + harfler.Length);

        return $"{ilKodu} {harfler} {numara}";
    }


}

public class TokenResponse
{
    public string Token { get; set; }
}

