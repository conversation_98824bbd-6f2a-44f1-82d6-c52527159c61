using Domain.External;
using Domain.Shared;
using MimeKit;
using MailKit.Net.Smtp;

namespace External.Email;

public class EmailManager(AppSettings appSettings) : IEmailManager
{
    private readonly AppSettings _appSettings = appSettings;

    public async Task SendEmailAsync(string email, string subject, string message, List<string>? attachments = null, bool isBodyHtml = false)
    {
        try
        {
            var email_Message = new MimeMessage();
            var email_From = new MailboxAddress(_appSettings.MailUser, _appSettings.MailUser);
            email_Message.From.Add(email_From);
            var email_To = new MailboxAddress(email, email);
            email_Message.To.Add(email_To);
            email_Message.Subject = subject;
            var emailBodyBuilder = new BodyBuilder();
            if (isBodyHtml)
            {
                emailBodyBuilder.HtmlBody = message;
            }
            else
            {
                emailBodyBuilder.TextBody = message;
            }
            email_Message.Body = emailBodyBuilder.ToMessageBody();
            var MailClient = new SmtpClient();
            await MailClient.ConnectAsync(_appSettings.MailServer, _appSettings.MailPort, _appSettings.SslEnabled);
            await MailClient.AuthenticateAsync(_appSettings.MailUser, _appSettings.MailPassword);
            await MailClient.SendAsync(email_Message);
            await MailClient.DisconnectAsync(true);
            MailClient.Dispose();
        }
        catch (Exception)
        {
        }

    }
}