using Domain.External;
using Microsoft.AspNetCore.Identity;

namespace External.Email;

public class CustomIdentityEmailSender<TUser>(IEmailManager emailManager) : IEmailSender<TUser> where TUser : class
{
    public Task SendConfirmationLinkAsync(TUser user, string email, string confirmationLink) =>
        emailManager.SendEmailAsync(email, "Email Doğrulama", $"Lütfen email adresiniz <a href='{confirmationLink}'>buraya</a> tıklayarak doğrulayın.");

    public Task SendPasswordResetLinkAsync(TUser user, string email, string resetLink) =>
        emailManager.SendEmailAsync(email, "Şifre Sıfırlama", $"Lütfen şifrenizi <a href='{resetLink}'>buraya</a> tıklayarak doğrulayın.");

    public Task SendPasswordResetCodeAsync(TUser user, string email, string resetCode) =>
        emailManager.SendEmailAsync(email, "<PERSON><PERSON><PERSON> Sıfırlama", $"Lütfen şu kodu kullanarak şifrenizi sıfırlayın: {resetCode}");
}