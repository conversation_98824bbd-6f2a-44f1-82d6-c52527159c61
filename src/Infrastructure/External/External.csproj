﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.44" />
    <PackageReference Include="Google.Apis.Auth" Version="1.68.0" />
    <PackageReference Include="MailKit" Version="4.8.0" />
    <PackageReference Include="Mimekit" Version="4.8.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Core\Domain\Domain.csproj" />
    <ProjectReference Include="..\..\Core\Application\Application.csproj" />
  </ItemGroup>
</Project>
