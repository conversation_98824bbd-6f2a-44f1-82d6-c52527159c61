using System.CodeDom;
using Domain.Account;
using Domain.External;
using External.Email;
using External.ERP;
using External.Notification;
using External.SMS;
using External.Weather;
using External.LiveTracking;

using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace External;

public static class ServiceRegistration
{
    public static void AddExternalInsfrastructure(this IServiceCollection serviceCollection, IConfiguration configuration)
    {
        serviceCollection.AddScoped<IEmailManager, EmailManager>();
        serviceCollection.AddScoped<ISMSManager, SMSManager>();
        serviceCollection.AddScoped<IWeatherManager, WeatherManager>();
        serviceCollection.AddScoped<ILiveTrackingManager, LiveTrackingManager>();
        serviceCollection.AddScoped<IERPManager, ERPManager>();
        serviceCollection.AddScoped<INotificationManager, NotificationManager>();
    }
}