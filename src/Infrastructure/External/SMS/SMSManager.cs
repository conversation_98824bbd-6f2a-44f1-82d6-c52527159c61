using System.Text;
using System.Text.RegularExpressions;
using Domain.External;
using Domain.Shared;

namespace External.SMS;

public class SMSManager(AppSettings appSettings) : ISMSManager
{
    private readonly AppSettings _appSettings = appSettings;

    public async Task SendSmsAsync(string Phone, string Message)
    {
        string _gsmno = Regex.Replace(Phone, "[^0-9]+", string.Empty);
        string xml = $"""
                    <?xml version="1.0" encoding="UTF-8"?>
                    <Submit xmlns:i="http://www.w3.org/2001/XMLSchema-instance" xmlns="SmsApi">
                        <Credential>
                            <Password>{_appSettings.SmsUserCode}</Password>
                            <Username>{_appSettings.SmsUserName}</Username>
                        </Credential>
                        <DataCoding>Turkish</DataCoding>
                        <Header>
                            <From>{_appSettings.SmsUserBaslik}</From>
                            <ScheduledDeliveryTime></ScheduledDeliveryTime>
                            <ValidityPeriod>0</ValidityPeriod>
                        </Header>
                        <Message>{Message}</Message>
                        <To xmlns:d2p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
                            <d2p1:string>{_gsmno}</d2p1:string>
                        </To>
                    </Submit>
                    """;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Add("ContentType", "application/x-www-form-urlencoded");
        var content = new StringContent(xml, Encoding.UTF8, "application/x-www-form-urlencoded");
        var response = await client.PostAsync("http://panel.1telekom.com.tr/Api/Submit", content);
        var responseString = await response.Content.ReadAsStringAsync();
    }

    public async Task SendSmsNetGSMAsync(string Phone, string Message)
    {
        string _gsmno = Regex.Replace(Phone, "[^0-9]+", string.Empty);
        string _compcode = _appSettings.SmsCompanyCode;
        string _username = _appSettings.SmsUserName;
        string _passw = _appSettings.SmsUserCode;
        string _baslik = _appSettings.SmsUserBaslik;
        string _smstext = Message;
        string ss = $"""
        <?xml version='1.0' encoding='UTF-8'?>
        <mainbody>
            <header>
                <company dil='TR'>{_compcode}</company>
                <usercode>{_username}</usercode>
                <password>{_passw}</password>
                <type>1:n</type>
                <msgheader>{_baslik}</msgheader>
            </header>
            <body>
            <msg>
                <![CDATA[{_smstext}]]>
                </msg>
                <no>{_gsmno}</no>
            </body>
        </mainbody>
        """;
        using var client = new HttpClient();
        client.DefaultRequestHeaders.Add("ContentType", "application/x-www-form-urlencoded");
        var content = new StringContent(ss, Encoding.UTF8, "application/x-www-form-urlencoded");
        var response = await client.PostAsync("https://api.netgsm.com.tr/sms/send/xml", content);
        var responseString = await response.Content.ReadAsStringAsync();
    }
}