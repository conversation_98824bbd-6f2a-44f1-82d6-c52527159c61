using System.Text.Json;
using Domain.External;
using Domain.External.DTOs;
using Domain.Shared;

namespace External.Weather;

public class WeatherManager(
    AppSettings settings)
    : IWeatherManager
{
    private readonly AppSettings _settings = settings;

    public async Task<List<WeatherDTO>> GetWeather(string city, string StartDate, string EndDate)
    {
        var startDateString = StartDate;
        var endDateString = EndDate;
        var url = $"https://weather.visualcrossing.com/VisualCrossingWebServices/rest/services/timeline/{city}/{startDateString}/{endDateString}?unitGroup=metric&include=days&key={_settings.WeatherApiKey}&contentType=json";
        using var client = new HttpClient();
        var response = await client.GetAsync(url);
        var responseBody = await response.Content.ReadAsStringAsync();
        if (response.IsSuccessStatusCode == false)
        {
            throw new AppException(responseBody + " -> " + url);
        }
        var weatherResponse = JsonSerializer.Deserialize<WeatherResponse>(responseBody, new JsonSerializerOptions { PropertyNameCaseInsensitive = true });
        var result = new List<WeatherDTO>();
        for (DateTime date = DateTime.Parse(StartDate); date <= DateTime.Parse(EndDate); date = date.AddDays(1))
        {
            var day = weatherResponse?.Days?.FirstOrDefault(x => x.DateTime == date);
            if (day != null)
            {
                result.Add(new WeatherDTO
                {
                    Date = day.DateTime,
                    Type = day.Conditions,
                    Degree = day.Temp,
                    Image = GetImage(day.Icon)
                });
            }
        }
        return result;
    }

    private static string GetImage(string? icon)
    {
        return icon switch
        {
            "snow" => """<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M12 2C12.5523 2 13 2.44772 13 3V6.58579L15.2929 4.29289C15.6834 3.90237 16.3166 3.90237 16.7071 4.29289C17.0976 4.68342 17.0976 5.31658 16.7071 5.70711L13 9.41421V11H14.5858L18.2929 7.29289C18.6834 6.90237 19.3166 6.90237 19.7071 7.29289C20.0976 7.68342 20.0976 8.31658 19.7071 8.70711L17.4142 11H21C21.5523 11 22 11.4477 22 12C22 12.5523 21.5523 13 21 13H17.4142L19.7071 15.2929C20.0976 15.6834 20.0976 16.3166 19.7071 16.7071C19.3166 17.0976 18.6834 17.0976 18.2929 16.7071L14.5858 13H13V14.5858L16.7071 18.2929C17.0976 18.6834 17.0976 19.3166 16.7071 19.7071C16.3166 20.0976 15.6834 20.0976 15.2929 19.7071L13 17.4142V21C13 21.5523 12.5523 22 12 22C11.4477 22 11 21.5523 11 21V17.4142L8.70711 19.7071C8.31658 20.0976 7.68342 20.0976 7.29289 19.7071C6.90237 19.3166 6.90237 18.6834 7.29289 18.2929L11 14.5858V13H9.41421L5.70711 16.7071C5.31658 17.0976 4.68342 17.0976 4.29289 16.7071C3.90237 16.3166 3.90237 15.6834 4.29289 15.2929L6.58579 13H3C2.44772 13 2 12.5523 2 12C2 11.4477 2.44772 11 3 11H6.58579L4.29289 8.70711C3.90237 8.31658 3.90237 7.68342 4.29289 7.29289C4.68342 6.90237 5.31658 6.90237 5.70711 7.29289L9.41421 11H11V9.41421L7.29289 5.70711C6.90237 5.31658 6.90237 4.68342 7.29289 4.29289C7.68342 3.90237 8.31658 3.90237 8.70711 4.29289L11 6.58579V3C11 2.44772 11.4477 2 12 2Z" fill="#000000"></path> </g></svg>""",
            "rain" => """<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M12 3C9.79086 3 8 4.79086 8 7V8H7C4.80497 8 3.5 9.28211 3.5 10.5C3.5 11.233 3.7651 11.815 4.26518 12.2318C4.78401 12.6641 5.65684 13 7 13H17C18.3432 13 19.216 12.6641 19.7348 12.2318C20.2349 11.815 20.5 11.233 20.5 10.5C20.5 9.28211 19.195 8 17 8H16V7C16 4.79086 14.2091 3 12 3ZM6.07261 6.0638C6.52214 3.19476 9.00476 1 12 1C14.9952 1 17.4779 3.19476 17.9274 6.0638C20.3519 6.39838 22.5 8.02966 22.5 10.5C22.5 11.767 22.0151 12.935 21.0152 13.7682C20.034 14.5859 18.6568 15 17 15H7C5.34316 15 3.96599 14.5859 2.98482 13.7682C1.9849 12.935 1.5 11.767 1.5 10.5C1.5 8.02966 3.64807 6.39838 6.07261 6.0638ZM7 16.5C7.55228 16.5 8 16.9477 8 17.5V18C8 18.5523 7.55228 19 7 19C6.44772 19 6 18.5523 6 18V17.5C6 16.9477 6.44772 16.5 7 16.5ZM12 16.5C12.5523 16.5 13 16.9477 13 17.5V18C13 18.5523 12.5523 19 12 19C11.4477 19 11 18.5523 11 18V17.5C11 16.9477 11.4477 16.5 12 16.5ZM17 16.5C17.5523 16.5 18 16.9477 18 17.5V18C18 18.5523 17.5523 19 17 19C16.4477 19 16 18.5523 16 18V17.5C16 16.9477 16.4477 16.5 17 16.5ZM6 20.5C6.55228 20.5 7 20.9477 7 21.5V22C7 22.5523 6.55228 23 6 23C5.44772 23 5 22.5523 5 22V21.5C5 20.9477 5.44772 20.5 6 20.5ZM11 20.5C11.5523 20.5 12 20.9477 12 21.5V22C12 22.5523 11.5523 23 11 23C10.4477 23 10 22.5523 10 22V21.5C10 20.9477 10.4477 20.5 11 20.5ZM16 20.5C16.5523 20.5 17 20.9477 17 21.5V22C17 22.5523 16.5523 23 16 23C15.4477 23 15 22.5523 15 22V21.5C15 20.9477 15.4477 20.5 16 20.5Z" fill="#000000"></path> </g></svg>""",
            "fog" => """<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M3 18H7M10 18H21M5 21H12M16 21H19M8.8 15C6.14903 15 4 12.9466 4 10.4137C4 8.31435 5.6 6.375 8 6C8.75283 4.27403 10.5346 3 12.6127 3C15.2747 3 17.4504 4.99072 17.6 7.5C19.0127 8.09561 20 9.55741 20 11.1402C20 13.2719 18.2091 15 16 15L8.8 15Z" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>""",
            "wind" => """<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M7 5C7 2.79086 8.79086 1 11 1C13.2091 1 15 2.79086 15 5C15 7.20914 13.2091 9 11 9H3C2.44772 9 2 8.55228 2 8C2 7.44772 2.44772 7 3 7H11C12.1046 7 13 6.10457 13 5C13 3.89543 12.1046 3 11 3C9.89543 3 9 3.89543 9 5V5.1C9 5.65228 8.55228 6.1 8 6.1C7.44772 6.1 7 5.65228 7 5.1V5ZM16.9 6C16.9 5.44772 17.3477 5 17.9 5H18C20.2091 5 22 6.79086 22 9C22 11.2091 20.2091 13 18 13H5C4.44772 13 4 12.5523 4 12C4 11.4477 4.44772 11 5 11H18C19.1046 11 20 10.1046 20 9C20 7.89543 19.1046 7 18 7H17.9C17.3477 7 16.9 6.55228 16.9 6ZM0 12C0 11.4477 0.447715 11 1 11H2C2.55228 11 3 11.4477 3 12C3 12.5523 2.55228 13 2 13H1C0.447715 13 0 12.5523 0 12ZM4 16C4 15.4477 4.44772 15 5 15H6C6.55228 15 7 15.4477 7 16C7 16.5523 6.55228 17 6 17H5C4.44772 17 4 16.5523 4 16ZM8 16C8 15.4477 8.44772 15 9 15H13C15.2091 15 17 16.7909 17 19C17 21.2091 15.2091 23 13 23C10.7909 23 9 21.2091 9 19V18.9C9 18.3477 9.44771 17.9 10 17.9C10.5523 17.9 11 18.3477 11 18.9V19C11 20.1046 11.8954 21 13 21C14.1046 21 15 20.1046 15 19C15 17.8954 14.1046 17 13 17H9C8.44772 17 8 16.5523 8 16Z" fill="#000000"></path> </g></svg>""",
            "cloudy" => """<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M5.38846 12.7023C3.98522 12.1237 3 10.7636 3 9.17807C3 7.42863 4.3 5.8125 6.25 5.5C6.86168 4.0617 8.30934 3 9.9978 3C12.1607 3 13.9285 4.65893 14.05 6.75C14.8721 7.10549 15.5169 7.83126 15.8166 8.69914M5.38846 12.7023C4.50928 13.5938 4 14.7867 4 16.0315C4 18.7755 6.28335 21 9.1 21L16.75 21C19.0972 21 21 19.1279 21 16.8185C21 15.1039 19.951 13.5202 18.45 12.875C18.3457 11.0905 17.3135 9.5483 15.8166 8.69914M5.38846 12.7023C6.11557 11.9651 7.0957 11.4339 8.25 11.25C9.04989 9.3802 10.943 8 13.151 8C14.1227 8 15.0333 8.25474 15.8166 8.69914" stroke="#000000" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path> </g></svg>""",
            "partly-cloudy-day" => """<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M9 2.00024C9 1.44796 9.44771 1.00024 10 1.00024C10.5523 1.00024 11 1.44796 11 2.00024V3.00024C11 3.55253 10.5523 4.00024 10 4.00024C9.44771 4.00024 9 3.55253 9 3.00024V2.00024Z" fill="#0F0F0F"></path> <path fill-rule="evenodd" clip-rule="evenodd" d="M14.0006 7.0005C13.0884 5.78591 11.6359 5.00024 10 5.00024C7.23858 5.00024 5 7.23882 5 10.0002C5 10.3515 5.03623 10.6944 5.10515 11.0252C4.75224 11.1654 4.42503 11.3382 4.12434 11.5416C2.81684 12.426 2.1491 13.7958 2.0226 15.1823C1.77602 17.8852 3.61934 21.0002 7.17706 21.0002L18.5526 21.0002C19.9549 21.0002 21.0916 20.5599 21.8787 19.7891C22.6603 19.0236 23.0183 18.0072 22.9993 17.0093C22.9662 15.2772 21.8019 13.5863 19.7773 13.0961C20.0627 10.5291 18.2721 8.25658 16.092 7.39549C15.437 7.13676 14.7268 6.99329 14.0006 7.0005ZM11.7254 7.55237C11.2376 7.20799 10.6424 7.00568 10 7.00568C8.34615 7.00568 7.00543 8.34639 7.00543 10.0002C7.00543 10.2084 7.02667 10.4116 7.0671 10.6078C7.60146 10.5762 8.1724 10.5992 8.77842 10.6808C9.5493 9.16687 10.5659 8.13298 11.7254 7.55237ZM10.3175 12.1097C11.0062 10.4965 11.9034 9.65869 12.7511 9.27892C13.6015 8.89799 14.5189 8.92449 15.3573 9.25564C17.1117 9.94862 18.2154 11.7921 17.6508 13.5043C17.4178 14.211 17.9363 14.9336 18.6672 14.9565C20.2392 15.0058 20.981 16.071 20.9996 17.0475C21.0091 17.5438 20.8334 18.0134 20.4793 18.3602C20.1306 18.7018 19.5296 19.0002 18.5526 19.0002L7.17706 19.0002C5.0662 19.0002 3.8484 17.1828 4.01433 15.364C4.09409 14.4897 4.50102 13.7014 5.24487 13.1982C5.99008 12.6942 7.21156 12.3849 9.0849 12.7592C9.59289 12.8607 10.1116 12.5921 10.3175 12.1097Z" fill="#0F0F0F"></path> <path d="M1 10.0002C1 10.5525 1.44772 11.0002 2 11.0002H3C3.55228 11.0002 4 10.5525 4 10.0002C4 9.44796 3.55228 9.00024 3 9.00024H2C1.44772 9.00024 1 9.44796 1 10.0002Z" fill="#0F0F0F"></path> <path d="M3.63603 5.05061C3.24551 4.66009 3.24551 4.02692 3.63603 3.6364C4.02656 3.24587 4.65972 3.24587 5.05024 3.6364L5.75735 4.34351C6.14788 4.73403 6.14788 5.3672 5.75735 5.75772C5.36683 6.14824 4.73366 6.14824 4.34314 5.75772L3.63603 5.05061Z" fill="#0F0F0F"></path> <path d="M14.2426 4.34328C13.8521 4.7338 13.8521 5.36697 14.2426 5.75749C14.6332 6.14802 15.2663 6.14802 15.6569 5.75749L16.364 5.05039C16.7545 4.65986 16.7545 4.0267 16.364 3.63617C15.9734 3.24565 15.3403 3.24565 14.9498 3.63617L14.2426 4.34328Z" fill="#0F0F0F"></path> </g></svg>""",
            "partly-cloudy-night" => """<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M15.8605 5.40671C16.3496 5.14684 16.9074 5 17.5 5C19.433 5 21 6.567 21 8.5C21 9.14714 20.8249 9.75273 20.5188 10.2727C19.7909 9.66619 18.9039 9.24409 17.9296 9.07824C17.7035 7.61158 16.9462 6.32019 15.8605 5.40671ZM13.9291 4.31684C14.8893 3.4966 16.1371 3 17.5 3C20.5376 3 23 5.46243 23 8.5C23 9.77481 22.5654 10.9492 21.8379 11.8815C22.2602 12.6601 22.5 13.552 22.5 14.5C22.5 17.5376 20.0376 20 17 20H7C3.96243 20 1.5 17.5376 1.5 14.5C1.5 11.7793 3.47551 9.51997 6.07036 9.07824C6.51381 6.20213 8.99974 4 12 4C12.6748 4 13.3237 4.11141 13.9291 4.31684ZM8 10C8 7.79086 9.79086 6 12 6C14.2091 6 16 7.79086 16 10V11H17C18.933 11 20.5 12.567 20.5 14.5C20.5 16.433 18.933 18 17 18H7C5.067 18 3.5 16.433 3.5 14.5C3.5 12.567 5.067 11 7 11H8V10Z" fill="#000000"></path> </g></svg>""",
            "clear-day" => """<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M13 2C13 1.44772 12.5523 1 12 1C11.4477 1 11 1.44772 11 2V4C11 4.55228 11.4477 5 12 5C12.5523 5 13 4.55228 13 4V2ZM7.86603 2.83972C7.58988 2.36143 6.97829 2.19755 6.5 2.4737C6.02171 2.74984 5.85783 3.36143 6.13397 3.83972L7.13332 5.57064C7.40946 6.04893 8.02105 6.2128 8.49934 5.93666C8.97764 5.66052 9.14151 5.04893 8.86537 4.57064L7.86603 2.83972ZM17.866 3.83977C18.1422 3.36148 17.9783 2.74989 17.5 2.47375C17.0217 2.1976 16.4101 2.36148 16.134 2.83977L15.1347 4.57065C14.8585 5.04894 15.0224 5.66053 15.5007 5.93667C15.979 6.21282 16.5906 6.04894 16.8667 5.57065L17.866 3.83977ZM21.1602 7.86603C21.6385 7.58988 21.8024 6.97829 21.5263 6.5C21.2501 6.02171 20.6385 5.85783 20.1602 6.13397L18.4294 7.1333C17.9511 7.40944 17.7872 8.02103 18.0633 8.49932C18.3395 8.97762 18.9511 9.14149 19.4294 8.86535L21.1602 7.86603ZM3.83972 6.13397C3.36143 5.85783 2.74984 6.02171 2.4737 6.5C2.19755 6.97829 2.36143 7.58988 2.83972 7.86603L4.57064 8.86537C5.04893 9.14151 5.66052 8.97764 5.93666 8.49934C6.2128 8.02105 6.04893 7.40946 5.57064 7.13332L3.83972 6.13397ZM2 11C1.44772 11 1 11.4477 1 12C1 12.5523 1.44772 13 2 13H4C4.55228 13 5 12.5523 5 12C5 11.4477 4.55228 11 4 11H2ZM20 11C19.4477 11 19 11.4477 19 12C19 12.5523 19.4477 13 20 13H22C22.5523 13 23 12.5523 23 12C23 11.4477 22.5523 11 22 11H20ZM5.57064 16.8667C6.04893 16.5905 6.2128 15.9789 5.93666 15.5007C5.66052 15.0224 5.04893 14.8585 4.57064 15.1346L2.83972 16.134C2.36143 16.4101 2.19755 17.0217 2.4737 17.5C2.74984 17.9783 3.36143 18.1422 3.83972 17.866L5.57064 16.8667ZM19.4293 15.1347C18.9511 14.8585 18.3395 15.0224 18.0633 15.5007C17.7872 15.979 17.9511 16.5906 18.4293 16.8667L20.1602 17.866C20.6385 18.1422 21.2501 17.9783 21.5263 17.5C21.8024 17.0217 21.6385 16.4101 21.1602 16.134L19.4293 15.1347ZM16.8667 18.4293C16.5906 17.9511 15.979 17.7872 15.5007 18.0633C15.0224 18.3395 14.8585 18.9511 15.1347 19.4293L16.134 21.1602C16.4101 21.6385 17.0217 21.8024 17.5 21.5263C17.9783 21.2501 18.1422 20.6385 17.866 20.1602L16.8667 18.4293ZM8.86537 19.4294C9.14151 18.9511 8.97764 18.3395 8.49934 18.0633C8.02105 17.7872 7.40946 17.9511 7.13332 18.4294L6.13397 20.1603C5.85783 20.6386 6.02171 21.2502 6.5 21.5263C6.97829 21.8024 7.58988 21.6386 7.86603 21.1603L8.86537 19.4294ZM13 20C13 19.4477 12.5523 19 12 19C11.4477 19 11 19.4477 11 20V22C11 22.5523 11.4477 23 12 23C12.5523 23 13 22.5523 13 22V20ZM8 12C8 9.79086 9.79086 8 12 8C14.2091 8 16 9.79086 16 12C16 14.2091 14.2091 16 12 16C9.79086 16 8 14.2091 8 12ZM12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6Z" fill="#000000"></path> </g></svg>""",
            "clear-night" => """<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path fill-rule="evenodd" clip-rule="evenodd" d="M10.9866 2.81264C11.1759 3.25273 11.0265 3.76528 10.6304 4.03473C9.04069 5.11602 8 6.93663 8 8.99999C8 12.3137 10.6863 15 14 15C16.9001 15 19.3217 12.9413 19.8791 10.205C19.9749 9.7348 20.3912 9.39893 20.871 9.40466C21.3508 9.4104 21.7589 9.75612 21.8434 10.2285C21.9464 10.8042 22 11.3962 22 12C22 17.5229 17.5229 22 12 22C6.47713 22 2 17.5229 2 12C2 7.21279 5.36283 3.21342 9.85431 2.23097C10.3223 2.1286 10.7972 2.37255 10.9866 2.81264ZM6.51162 6.17934C4.96517 7.6383 4 9.70684 4 12C4 16.4183 7.5817 20 12 20C15.4257 20 18.3485 17.8468 19.4889 14.8199C18.0565 16.1715 16.1254 17 14 17C9.58175 17 6 13.4182 6 8.99999C6 8.00706 6.18101 7.05645 6.51162 6.17934Z" fill="#000000"></path> </g></svg>""",
            _ => "",
        };
    }
}

public class WeatherResponse
{
    public List<DayResponse>? Days { get; set; }
}

public class DayResponse
{
    public DateTime DateTime { get; set; }
    public decimal? Temp { get; set; }
    public string? Conditions { get; set; }
    public string? Icon { get; set; }
}