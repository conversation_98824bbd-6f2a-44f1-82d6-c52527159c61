using System.Data.SqlClient;
using Dapper;
using Domain.External;
using Domain.External.DTOs;
using Domain.Shared;

namespace External.ERP;

public class ERPManager(AppSettings appSettings) : IERPManager
{
    private readonly AppSettings _appSettings = appSettings;

    public async Task<List<AccountingDTO>> GetAccountingList(string accountingCode, DateTime startDate, DateTime endDate)
    {
        var startDateString = startDate.ToString("yyyy-MM-dd");
        var endDateString = endDate.ToString("yyyy-MM-dd");
        var sql = """
                    SELECT cha_tip as HareketTip, cha_Guid as CariRecNo,cha_meblag as <PERSON><PERSON>,
                    CONVERT(datetime, cha_tarihi,108) as <PERSON><PERSON><PERSON>, MikroDB_V16_TOKGOZ.[dbo].fn_CariHarEvrTipKisa(cha_evrak_tip) as Tur
                    FROM MikroDB_V16_TOKGOZ.dbo.CARI_HESAP_HAREKETLERI
                    WHERE cha_tarihi >= @startDateString and cha_tarihi <= @endDateString AND cha_kod = @accountingCode ORDER BY Tarihi DESC
                    """;
        return await Query<AccountingDTO>(sql, new { accountingCode, startDateString, endDateString });
    }

    public async Task<List<AccountingDetailDTO>> GetAccountingDetail(Guid? CariRecNo)
    {
        var sql = """
                    SELECT sth_miktar as Miktar,sth_tutar as Tutar,sth_vergi as Vergi,sth_aciklama as Aciklama,
                    (sth_iskonto1 + sth_iskonto2 + sth_iskonto3 + sth_iskonto4 + sth_iskonto5 + sth_iskonto6) as
                    Iskonto, sth_tarih as Tarih, sth_belge_no as FaturaNo, sto_isim as UrunAdi
                    FROM MikroDB_V16_TOKGOZ.dbo.[STOK_HAREKETLERI]
                    LEFT JOIN MikroDB_V16_TOKGOZ.dbo.[STOKLAR] ON [STOK_HAREKETLERI].sth_stok_kod = [STOKLAR].sto_kod
                    WHERE [STOK_HAREKETLERI].sth_fat_uid = @CariRecNo
                    """;
        var param = new { CariRecNo };
        return await Query<AccountingDetailDTO>(sql, param);
    }

    public async Task<List<DocumentDTO>> GetDocumentList(string accountingCode, DateTime startDate, DateTime endDate)
    {
        var startDateString = startDate.ToString("yyyy-MM-dd HH:mm:ss");
        var endDateString = endDate.AddDays(1).ToString("yyyy-MM-dd HH:mm:ss");
        var sql = """
                    SELECT SubeNo,TesisAdi, Sube_Adi as SubeAdi, Tarih, Gib_IrsaliyeNo as IrsaliyeNo, Cari_Kodu as Cariokodu,
                    Cari_Aciklama as CariAciklama, Recete_Sinifi as ReceteSinifi, StokKodu,
                    StokAciklama, Irsaliye_Miktar as IrsaliyeMiktar, SantiyeAdi, Plaka, Surucu, PompaPlaka, Pompaci
                    FROM Eylul_Data.dbo.Uretimler
                    WHERE (Silindi = 0 OR  Silindi IS NULL) AND Cari_Kodu = @accountingCode AND Tarih >= @startDateString and Tarih <= @endDateString
                    ORDER BY Tarih DESC
                    """;
        var param = new { accountingCode, startDateString, endDateString };
        return await Query<DocumentDTO>(sql, param);
    }
    public async Task<List<DocumentDTO>> GetDocumentDetail(string IrsaliyeNo)
    {
        var sql = """
                    SELECT TOP(1) SubeNo, Sube_Adi as SubeAdi, Tarih, Gib_IrsaliyeNo as IrsaliyeNo, Cari_Kodu as Cariokodu,
                    Cari_Aciklama as CariAciklama, Recete_Sinifi as ReceteSinifi, StokKodu,
                    StokAciklama, Irsaliye_Miktar as IrsaliyeMiktar, SantiyeAdi, Plaka, Surucu, PompaPlaka, Pompaci
                    FROM Eylul_Data.dbo.Uretimler
                    WHERE (Silindi = 0 OR  Silindi IS NULL) AND Gib_IrsaliyeNo = @IrsaliyeNo
                    ORDER BY Tarih DESC
                    """;
        var param = new { IrsaliyeNo };
        return await Query<DocumentDTO>(sql, param);
    }

    public async Task<List<OrderDTO>> GetOrderList()
    {
        var sql = """
                    SELECT Siparis_No AS SiparisNo,SiparisTarihi,Sube_Adi AS SantralAdi,Musteri AS FirmaAdi,CariKodu,Santiye AS SantiyeAdi,Recete AS UrunAdi,
                    CASE 
                        WHEN Hizmet = 'POMPALI' THEN CAST(1 AS bit)
                        ELSE CAST(0 AS bit)
                    END AS HasPomp,
                    Istenen,
                    Verilen,
                    Kalan
                    FROM Eylul_Data.dbo.Siparis
                    WHERE CAST(SiparisTarihi AS date) = CAST(GETDATE() AS date);
                    """;
        return await Query<OrderDTO>(sql);
    }

    private async Task<List<T>> Query<T>(string sql, object? param = null)
    {
        var connectionString = _appSettings.ConnectionStringERP;
        using var connection = new SqlConnection(connectionString);
        var result = await connection.QueryAsync<T>(sql, param);
        return result.ToList();
    }
}