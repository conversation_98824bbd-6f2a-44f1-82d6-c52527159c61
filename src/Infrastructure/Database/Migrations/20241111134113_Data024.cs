﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data024 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserDepartment_Department_DepartmentId",
                schema: "dbo",
                table: "UserDepartment");

            migrationBuilder.DropForeignKey(
                name: "FK_UserDepartment_User_UserId",
                schema: "dbo",
                table: "UserDepartment");

            migrationBuilder.AddColumn<string>(
                name: "RejectNote",
                schema: "dbo",
                table: "TransactionRequest",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "PaymentPlanId",
                schema: "dbo",
                table: "Contract",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true,
                oldDefaultValue: 1);

            migrationBuilder.AddColumn<bool>(
                name: "IsDeleted",
                schema: "dbo",
                table: "Contract",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddForeignKey(
                name: "FK_UserDepartment_Department_DepartmentId",
                schema: "dbo",
                table: "UserDepartment",
                column: "DepartmentId",
                principalSchema: "dbo",
                principalTable: "Department",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserDepartment_User_UserId",
                schema: "dbo",
                table: "UserDepartment",
                column: "UserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserDepartment_Department_DepartmentId",
                schema: "dbo",
                table: "UserDepartment");

            migrationBuilder.DropForeignKey(
                name: "FK_UserDepartment_User_UserId",
                schema: "dbo",
                table: "UserDepartment");

            migrationBuilder.DropColumn(
                name: "RejectNote",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "IsDeleted",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.AlterColumn<int>(
                name: "PaymentPlanId",
                schema: "dbo",
                table: "Contract",
                type: "int",
                nullable: true,
                defaultValue: 1,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_UserDepartment_Department_DepartmentId",
                schema: "dbo",
                table: "UserDepartment",
                column: "DepartmentId",
                principalSchema: "dbo",
                principalTable: "Department",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_UserDepartment_User_UserId",
                schema: "dbo",
                table: "UserDepartment",
                column: "UserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
