﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data032 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Building_User_AuditPersonId",
                schema: "dbo",
                table: "Building");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_PompType_PompTypeId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_TransactionRequest_PompTypeId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_Building_AuditPersonId",
                schema: "dbo",
                table: "Building");

            migrationBuilder.DropColumn(
                name: "PompTypeId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "AuditPersonId",
                schema: "dbo",
                table: "Building");

            migrationBuilder.AddColumn<bool>(
                name: "Is<PERSON>udit<PERSON>erson",
                schema: "dbo",
                table: "BuildingUser",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateTable(
                name: "ContractProductConcreteOption",
                schema: "dbo",
                columns: table => new
                {
                    ContractProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ConcreteOptionId = table.Column<int>(type: "int", nullable: false),
                    Price = table.Column<decimal>(type: "decimal(18,2)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractProductConcreteOption", x => new { x.ContractProductId, x.ConcreteOptionId });
                    table.ForeignKey(
                        name: "FK_ContractProductConcreteOption_ConcreteOption_ConcreteOptionId",
                        column: x => x.ConcreteOptionId,
                        principalSchema: "dbo",
                        principalTable: "ConcreteOption",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ContractProductConcreteOption_ContractProduct_ContractProductId",
                        column: x => x.ContractProductId,
                        principalSchema: "dbo",
                        principalTable: "ContractProduct",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "TransactionRequestPompType",
                schema: "dbo",
                columns: table => new
                {
                    TransactionRequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PompTypeId = table.Column<int>(type: "int", nullable: false),
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RequestedDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    SendDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsSend = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionRequestPompType", x => new { x.TransactionRequestId, x.PompTypeId });
                    table.ForeignKey(
                        name: "FK_TransactionRequestPompType_PompType_PompTypeId",
                        column: x => x.PompTypeId,
                        principalSchema: "dbo",
                        principalTable: "PompType",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TransactionRequestPompType_TransactionRequest_TransactionRequestId",
                        column: x => x.TransactionRequestId,
                        principalSchema: "dbo",
                        principalTable: "TransactionRequest",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductConcreteOption_ConcreteOptionId",
                schema: "dbo",
                table: "ContractProductConcreteOption",
                column: "ConcreteOptionId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductConcreteOption_ContractProductId_ConcreteOptionId",
                schema: "dbo",
                table: "ContractProductConcreteOption",
                columns: new[] { "ContractProductId", "ConcreteOptionId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequestPompType_PompTypeId",
                schema: "dbo",
                table: "TransactionRequestPompType",
                column: "PompTypeId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ContractProductConcreteOption",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "TransactionRequestPompType",
                schema: "dbo");

            migrationBuilder.DropColumn(
                name: "IsAuditPerson",
                schema: "dbo",
                table: "BuildingUser");

            migrationBuilder.AddColumn<int>(
                name: "PompTypeId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "AuditPersonId",
                schema: "dbo",
                table: "Building",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequest_PompTypeId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "PompTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_Building_AuditPersonId",
                schema: "dbo",
                table: "Building",
                column: "AuditPersonId");

            migrationBuilder.AddForeignKey(
                name: "FK_Building_User_AuditPersonId",
                schema: "dbo",
                table: "Building",
                column: "AuditPersonId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_PompType_PompTypeId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "PompTypeId",
                principalSchema: "dbo",
                principalTable: "PompType",
                principalColumn: "Id");
        }
    }
}
