﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data036 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductBuilding_ContractProduct_ContractProductId",
                schema: "dbo",
                table: "ContractProductBuilding");

            migrationBuilder.AddColumn<int>(
                name: "PriceTypeId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "int",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<decimal>(
                name: "LeftPrice",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Old<PERSON>rice",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "PriceUnderPowerPlant",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "PriceWithoutPlug",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalPrice",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LeftAmount",
                schema: "dbo",
                table: "Contract",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "OldAmount",
                schema: "dbo",
                table: "Contract",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalAmount",
                schema: "dbo",
                table: "Contract",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductBuilding_ContractProduct_ContractProductId",
                schema: "dbo",
                table: "ContractProductBuilding",
                column: "ContractProductId",
                principalSchema: "dbo",
                principalTable: "ContractProduct",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductBuilding_ContractProduct_ContractProductId",
                schema: "dbo",
                table: "ContractProductBuilding");

            migrationBuilder.DropColumn(
                name: "PriceTypeId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "LeftPrice",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "OldPrice",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "PriceUnderPowerPlant",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "PriceWithoutPlug",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "TotalPrice",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "LeftAmount",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropColumn(
                name: "OldAmount",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropColumn(
                name: "TotalAmount",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductBuilding_ContractProduct_ContractProductId",
                schema: "dbo",
                table: "ContractProductBuilding",
                column: "ContractProductId",
                principalSchema: "dbo",
                principalTable: "ContractProduct",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
