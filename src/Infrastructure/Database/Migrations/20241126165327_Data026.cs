﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data026 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "OldAmount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.UpdateData(
                schema: "dbo",
                table: "TransactionStatus",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CustomerName", "Name" },
                values: new object[] { "Planlanacak", "Planlanacak" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OldAmount",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.UpdateData(
                schema: "dbo",
                table: "TransactionStatus",
                keyColumn: "Id",
                keyValue: 2,
                columns: new[] { "CustomerName", "Name" },
                values: new object[] { "Onaylandı", "Onaylandı" });
        }
    }
}
