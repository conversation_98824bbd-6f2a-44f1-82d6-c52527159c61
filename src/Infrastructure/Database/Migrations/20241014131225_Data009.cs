﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data009 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FCMDeviceId",
                schema: "Users",
                table: "User",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ConcreteLocationId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ConsistencyClassId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TypeId",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.CreateTable(
                name: "ConcreteLocation",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false),
                    SubItem = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConcreteLocation", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ConcreteOption",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConcreteOption", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ConsistencyClass",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ConsistencyClass", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TransactionType",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionType", x => x.Id);
                });

            migrationBuilder.InsertData(
                schema: "dbo",
                table: "ConcreteLocation",
                columns: new[] { "Id", "Name", "SubItem" },
                values: new object[,]
                {
                    { 1, "Temel", "S3,S4" },
                    { 2, "Kolon", "S3" },
                    { 3, "Kiriş", "S3" },
                    { 4, "Tabliye", "S3,S4" },
                    { 5, "İstinat Duvarı", "S3" },
                    { 6, "Merdiven", "S2,S3" },
                    { 7, "Hatıl", "S3,S4" },
                    { 8, "Diğer", "S1,S2,S3,S4,S5" }
                });

            migrationBuilder.InsertData(
                schema: "dbo",
                table: "ConcreteOption",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { 1, "Normal" },
                    { 2, "Brüt" },
                    { 3, "Su Geçirimsiz" },
                    { 4, "Katkısız" },
                    { 5, "Antifrizli" },
                    { 6, "Sabit" },
                    { 7, "Dsi" },
                    { 8, "Bahçe Duvarı" }
                });

            migrationBuilder.InsertData(
                schema: "dbo",
                table: "ConsistencyClass",
                columns: new[] { "Id", "Description", "Name" },
                values: new object[,]
                {
                    { 1, "1 cm - 4 cm ÇÖKME ARALIĞI", "S1" },
                    { 2, "5 cm - 9 cm  ÇÖKME ARALIĞI", "S2" },
                    { 3, "10 cm - 15 cm  ÇÖKME ARALIĞI", "S3" },
                    { 4, "16 cm - 21 cm  ÇÖKME ARALIĞI", "S4" },
                    { 5, "22 cm ve üzeri ÇÖKME ARALIĞI", "S5" }
                });

            migrationBuilder.InsertData(
                schema: "dbo",
                table: "TransactionType",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { 1, "Normal" },
                    { 2, "İade" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequest_ConcreteLocationId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ConcreteLocationId");

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequest_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ConcreteOptionId");

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequest_ConsistencyClassId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ConsistencyClassId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransaction_TypeId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "TypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductTransaction_TransactionType_TypeId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "TypeId",
                principalSchema: "dbo",
                principalTable: "TransactionType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_ConcreteLocation_ConcreteLocationId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ConcreteLocationId",
                principalSchema: "dbo",
                principalTable: "ConcreteLocation",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_ConcreteOption_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ConcreteOptionId",
                principalSchema: "dbo",
                principalTable: "ConcreteOption",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_ConsistencyClass_ConsistencyClassId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ConsistencyClassId",
                principalSchema: "dbo",
                principalTable: "ConsistencyClass",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductTransaction_TransactionType_TypeId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_ConcreteLocation_ConcreteLocationId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_ConcreteOption_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_ConsistencyClass_ConsistencyClassId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropTable(
                name: "ConcreteLocation",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "ConcreteOption",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "ConsistencyClass",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "TransactionType",
                schema: "dbo");

            migrationBuilder.DropIndex(
                name: "IX_TransactionRequest_ConcreteLocationId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_TransactionRequest_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_TransactionRequest_ConsistencyClassId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_ContractProductTransaction_TypeId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "FCMDeviceId",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "ConcreteLocationId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "ConsistencyClassId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "TypeId",
                schema: "dbo",
                table: "ContractProductTransaction");
        }
    }
}
