﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data028 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Vehicle_PompType_PompTypeId",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.AlterColumn<int>(
                name: "PompTypeId",
                schema: "dbo",
                table: "Vehicle",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddForeignKey(
                name: "FK_Vehicle_PompType_PompTypeId",
                schema: "dbo",
                table: "Vehicle",
                column: "PompTypeId",
                principalSchema: "dbo",
                principalTable: "PompType",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Vehicle_PompType_PompTypeId",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.AlterColumn<int>(
                name: "PompTypeId",
                schema: "dbo",
                table: "Vehicle",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Vehicle_PompType_PompTypeId",
                schema: "dbo",
                table: "Vehicle",
                column: "PompTypeId",
                principalSchema: "dbo",
                principalTable: "PompType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
