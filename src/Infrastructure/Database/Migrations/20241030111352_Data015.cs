﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data015 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Certificate",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "ContractTemplate",
                schema: "dbo");

            migrationBuilder.AddColumn<string>(
                name: "ApprovedNote",
                schema: "dbo",
                table: "TransactionRequest",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalConcreteRefundable",
                schema: "dbo",
                table: "TransactionRequest",
                type: "decimal(18,4)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalConcreteRemaining",
                schema: "dbo",
                table: "TransactionRequest",
                type: "decimal(18,4)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalConcreteSent",
                schema: "dbo",
                table: "TransactionRequest",
                type: "decimal(18,4)",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "TransactionrequestTypeId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.AddColumn<bool>(
                name: "Refundable",
                schema: "dbo",
                table: "Product",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<decimal>(
                name: "RefundableAmount",
                schema: "dbo",
                table: "Product",
                type: "decimal(18,4)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                schema: "dbo",
                table: "PompType",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsRedirect",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<int>(
                name: "ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Price",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<DateTime>(
                name: "PriceGuaranteeDate",
                schema: "dbo",
                table: "ContractProduct",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ApprovedDateTime",
                schema: "dbo",
                table: "Contract",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ApprovedNote",
                schema: "dbo",
                table: "Contract",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ApprovedUserId",
                schema: "dbo",
                table: "Contract",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PaymentPlanId",
                schema: "dbo",
                table: "Contract",
                type: "int",
                nullable: false,
                defaultValue: 1);

            migrationBuilder.CreateTable(
                name: "ContractFile",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    ContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractFile", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContractFile_Contract_ContractId",
                        column: x => x.ContractId,
                        principalSchema: "dbo",
                        principalTable: "Contract",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ContractProductBuilding",
                schema: "dbo",
                columns: table => new
                {
                    ContractProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    BuildingId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ContractProductContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    ContractProductProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractProductBuilding", x => new { x.ContractProductId, x.BuildingId });
                    table.ForeignKey(
                        name: "FK_ContractProductBuilding_Building_BuildingId",
                        column: x => x.BuildingId,
                        principalSchema: "dbo",
                        principalTable: "Building",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ContractProductBuilding_ContractProduct_ContractProductContractId_ContractProductProductId",
                        columns: x => new { x.ContractProductContractId, x.ContractProductProductId },
                        principalSchema: "dbo",
                        principalTable: "ContractProduct",
                        principalColumns: new[] { "ContractId", "ProductId" });
                });

            migrationBuilder.CreateTable(
                name: "LabResult",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    TransactionRequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_LabResult", x => x.Id);
                    table.ForeignKey(
                        name: "FK_LabResult_TransactionRequest_TransactionRequestId",
                        column: x => x.TransactionRequestId,
                        principalSchema: "dbo",
                        principalTable: "TransactionRequest",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PaymentPlan",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PaymentPlan", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TransactionRequestType",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Name = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionRequestType", x => x.Id);
                });

            migrationBuilder.InsertData(
                schema: "dbo",
                table: "ContractProductTransactionStatus",
                columns: new[] { "Id", "Name" },
                values: new object[] { 5, "İptal" });

            migrationBuilder.InsertData(
                schema: "dbo",
                table: "PaymentPlan",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { 1, "Peşin" },
                    { 2, "Kredi Kartı" },
                    { 3, "Kredi Kartı Vadeli" },
                    { 4, "Çek 2 Ay Vadeli" },
                    { 5, "Ay + 30 Gün" }
                });

            migrationBuilder.InsertData(
                schema: "Users",
                table: "Role",
                columns: new[] { "Id", "ConcurrencyStamp", "Name", "NormalizedName" },
                values: new object[] { new Guid("d9530b95-6cc4-4e13-8225-6d65d5b1e617"), "d9530b95-6cc4-4e13-8225-6d65d5b1e617", "Kalıpçı", "MOULDER" });

            migrationBuilder.InsertData(
                schema: "dbo",
                table: "TransactionRequestType",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { 1, "Normal" },
                    { 2, "Keşif Talebi" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequest_TransactionrequestTypeId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "TransactionrequestTypeId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProduct_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct",
                column: "ConsistencyClassId");

            migrationBuilder.CreateIndex(
                name: "IX_Contract_ApprovedUserId",
                schema: "dbo",
                table: "Contract",
                column: "ApprovedUserId");

            migrationBuilder.CreateIndex(
                name: "IX_Contract_PaymentPlanId",
                schema: "dbo",
                table: "Contract",
                column: "PaymentPlanId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractFile_ContractId",
                schema: "dbo",
                table: "ContractFile",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductBuilding_BuildingId",
                schema: "dbo",
                table: "ContractProductBuilding",
                column: "BuildingId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductBuilding_ContractProductContractId_ContractProductProductId",
                schema: "dbo",
                table: "ContractProductBuilding",
                columns: new[] { "ContractProductContractId", "ContractProductProductId" });

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductBuilding_ContractProductId_BuildingId",
                schema: "dbo",
                table: "ContractProductBuilding",
                columns: new[] { "ContractProductId", "BuildingId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_LabResult_TransactionRequestId",
                schema: "dbo",
                table: "LabResult",
                column: "TransactionRequestId");

            migrationBuilder.AddForeignKey(
                name: "FK_Contract_PaymentPlan_PaymentPlanId",
                schema: "dbo",
                table: "Contract",
                column: "PaymentPlanId",
                principalSchema: "dbo",
                principalTable: "PaymentPlan",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Contract_User_ApprovedUserId",
                schema: "dbo",
                table: "Contract",
                column: "ApprovedUserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProduct_ConsistencyClass_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct",
                column: "ConsistencyClassId",
                principalSchema: "dbo",
                principalTable: "ConsistencyClass",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_TransactionRequestType_TransactionrequestTypeId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "TransactionrequestTypeId",
                principalSchema: "dbo",
                principalTable: "TransactionRequestType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Contract_PaymentPlan_PaymentPlanId",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropForeignKey(
                name: "FK_Contract_User_ApprovedUserId",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropForeignKey(
                name: "FK_ContractProduct_ConsistencyClass_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_TransactionRequestType_TransactionrequestTypeId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropTable(
                name: "ContractFile",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "ContractProductBuilding",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "LabResult",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "PaymentPlan",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "TransactionRequestType",
                schema: "dbo");

            migrationBuilder.DropIndex(
                name: "IX_TransactionRequest_TransactionrequestTypeId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_ContractProduct_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropIndex(
                name: "IX_Contract_ApprovedUserId",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropIndex(
                name: "IX_Contract_PaymentPlanId",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DeleteData(
                schema: "dbo",
                table: "ContractProductTransactionStatus",
                keyColumn: "Id",
                keyValue: 5);

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("d9530b95-6cc4-4e13-8225-6d65d5b1e617"));

            migrationBuilder.DropColumn(
                name: "ApprovedNote",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "TotalConcreteRefundable",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "TotalConcreteRemaining",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "TotalConcreteSent",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "TransactionrequestTypeId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "Refundable",
                schema: "dbo",
                table: "Product");

            migrationBuilder.DropColumn(
                name: "RefundableAmount",
                schema: "dbo",
                table: "Product");

            migrationBuilder.DropColumn(
                name: "Description",
                schema: "dbo",
                table: "PompType");

            migrationBuilder.DropColumn(
                name: "IsRedirect",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "Price",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "PriceGuaranteeDate",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "ApprovedDateTime",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropColumn(
                name: "ApprovedNote",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropColumn(
                name: "ApprovedUserId",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropColumn(
                name: "PaymentPlanId",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.CreateTable(
                name: "Certificate",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    TransactionRequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Certificate", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Certificate_TransactionRequest_TransactionRequestId",
                        column: x => x.TransactionRequestId,
                        principalSchema: "dbo",
                        principalTable: "TransactionRequest",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "ContractTemplate",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Active = table.Column<bool>(type: "bit", nullable: false),
                    Template = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false),
                    Title = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractTemplate", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Certificate_TransactionRequestId",
                schema: "dbo",
                table: "Certificate",
                column: "TransactionRequestId");
        }
    }
}
