﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data019 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_ConcreteOption_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_TransactionRequest_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.CreateTable(
                name: "TransactionRequestConcreteOption",
                schema: "dbo",
                columns: table => new
                {
                    TransactionRequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ConcreteOptionId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionRequestConcreteOption", x => new { x.TransactionRequestId, x.ConcreteOptionId });
                    table.ForeignKey(
                        name: "FK_TransactionRequestConcreteOption_ConcreteOption_ConcreteOptionId",
                        column: x => x.ConcreteOptionId,
                        principalSchema: "dbo",
                        principalTable: "ConcreteOption",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TransactionRequestConcreteOption_TransactionRequest_TransactionRequestId",
                        column: x => x.TransactionRequestId,
                        principalSchema: "dbo",
                        principalTable: "TransactionRequest",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequestConcreteOption_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequestConcreteOption",
                column: "ConcreteOptionId");

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequestConcreteOption_TransactionRequestId_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequestConcreteOption",
                columns: new[] { "TransactionRequestId", "ConcreteOptionId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TransactionRequestConcreteOption",
                schema: "dbo");

            migrationBuilder.AddColumn<int>(
                name: "ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequest_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ConcreteOptionId");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_ConcreteOption_ConcreteOptionId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ConcreteOptionId",
                principalSchema: "dbo",
                principalTable: "ConcreteOption",
                principalColumn: "Id");
        }
    }
}
