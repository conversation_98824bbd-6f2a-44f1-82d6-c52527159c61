﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data017 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_Product_ProductId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "FileName",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDate",
                schema: "Users",
                table: "User",
                type: "datetime2",
                nullable: true,
                defaultValueSql: "getdate()");

            migrationBuilder.AddColumn<bool>(
                name: "Kvkk",
                schema: "Users",
                table: "User",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "TaxNumber",
                schema: "Users",
                table: "User",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "ProductId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_Product_ProductId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ProductId",
                principalSchema: "dbo",
                principalTable: "Product",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_Product_ProductId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "InsertDate",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "Kvkk",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "TaxNumber",
                schema: "Users",
                table: "User");

            migrationBuilder.AlterColumn<Guid>(
                name: "ProductId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FileName",
                schema: "dbo",
                table: "Contract",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_Product_ProductId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ProductId",
                principalSchema: "dbo",
                principalTable: "Product",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
