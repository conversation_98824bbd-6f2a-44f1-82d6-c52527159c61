﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data014 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductTransaction_Contract_ContractId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "Certificate",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.AlterColumn<Guid>(
                name: "ContractId",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.CreateTable(
                name: "Certificate",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    TransactionRequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Certificate", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Certificate_TransactionRequest_TransactionRequestId",
                        column: x => x.TransactionRequestId,
                        principalSchema: "dbo",
                        principalTable: "TransactionRequest",
                        principalColumn: "Id");
                });

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("44a832a5-fbe2-43eb-8c27-0e5050abd9ea"),
                column: "Name",
                value: "Şantiye Görevlisi");

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("734354f3-c622-4750-a11c-04ab6f0fa497"),
                column: "Name",
                value: "Yapı Denetim Personeli");

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aaaa544b-b5f7-4bad-8c68-22c87005bfac"),
                column: "Name",
                value: "Şirket Yetkilisi");

            migrationBuilder.UpdateData(
                schema: "dbo",
                table: "TransactionStatus",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CustomerName", "Name" },
                values: new object[] { "Döküm Devam Ediyor", "Döküm Devam Ediyor" });

            migrationBuilder.CreateIndex(
                name: "IX_Certificate_TransactionRequestId",
                schema: "dbo",
                table: "Certificate",
                column: "TransactionRequestId");

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductTransaction_Contract_ContractId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "ContractId",
                principalSchema: "dbo",
                principalTable: "Contract",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductTransaction_Contract_ContractId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropTable(
                name: "Certificate",
                schema: "dbo");

            migrationBuilder.AddColumn<string>(
                name: "Certificate",
                schema: "dbo",
                table: "TransactionRequest",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "ContractId",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("44a832a5-fbe2-43eb-8c27-0e5050abd9ea"),
                column: "Name",
                value: "Santiye Çalışanı");

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("734354f3-c622-4750-a11c-04ab6f0fa497"),
                column: "Name",
                value: "YapıDenetim Firma Çalışanı");

            migrationBuilder.UpdateData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aaaa544b-b5f7-4bad-8c68-22c87005bfac"),
                column: "Name",
                value: "Şirket Çalışanı");

            migrationBuilder.UpdateData(
                schema: "dbo",
                table: "TransactionStatus",
                keyColumn: "Id",
                keyValue: 5,
                columns: new[] { "CustomerName", "Name" },
                values: new object[] { "Operasyonda", "Operasyonda" });

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductTransaction_Contract_ContractId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "ContractId",
                principalSchema: "dbo",
                principalTable: "Contract",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
