﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data003 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                schema: "Users",
                table: "Role",
                columns: new[] { "Id", "ConcurrencyStamp", "Name", "NormalizedName" },
                values: new object[,]
                {
                    { new Guid("439aeada-02d0-4962-9dfd-bc41363461a3"), "439aeada-02d0-4962-9dfd-bc41363461a3", "Admin", "ADMIN" },
                    { new Guid("44a832a5-fbe2-43eb-8c27-0e5050abd9ea"), "44a832a5-fbe2-43eb-8c27-0e5050abd9ea", "<PERSON><PERSON><PERSON>", "BUILDING" },
                    { new Guid("5351cc0b-20d7-4dcc-8fba-90c797b3f5b8"), "5351cc0b-20d7-4dcc-8fba-90c797b3f5b8", "Santral Çalışanı", "STATION" },
                    { new Guid("734354f3-c622-4750-a11c-04ab6f0fa497"), "734354f3-c622-4750-a11c-04ab6f0fa497", "YapıDenetim Firma Çalışanı", "CONTROL" },
                    { new Guid("98b3e5cc-cf74-4ee9-bc6a-6871c51a6469"), "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469", "Şoför", "DRIVER" },
                    { new Guid("aaaa544b-b5f7-4bad-8c68-22c87005bfac"), "aaaa544b-b5f7-4bad-8c68-22c87005bfac", "Şirket Çalışanı", "COMPANY" },
                    { new Guid("f6bcd1b9-328e-4008-9da2-2ce37f35940e"), "f6bcd1b9-328e-4008-9da2-2ce37f35940e", "Satış Temsilcisi", "SALES" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("439aeada-02d0-4962-9dfd-bc41363461a3"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("44a832a5-fbe2-43eb-8c27-0e5050abd9ea"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("5351cc0b-20d7-4dcc-8fba-90c797b3f5b8"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("734354f3-c622-4750-a11c-04ab6f0fa497"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("98b3e5cc-cf74-4ee9-bc6a-6871c51a6469"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("aaaa544b-b5f7-4bad-8c68-22c87005bfac"));

            migrationBuilder.DeleteData(
                schema: "Users",
                table: "Role",
                keyColumn: "Id",
                keyValue: new Guid("f6bcd1b9-328e-4008-9da2-2ce37f35940e"));
        }
    }
}
