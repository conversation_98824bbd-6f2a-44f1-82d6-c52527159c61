﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data010 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "StationId",
                schema: "dbo",
                table: "NotAvailableDate",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_NotAvailableDate_StationId",
                schema: "dbo",
                table: "NotAvailableDate",
                column: "StationId");

            migrationBuilder.AddForeignKey(
                name: "FK_NotAvailableDate_Station_StationId",
                schema: "dbo",
                table: "NotAvailableDate",
                column: "StationId",
                principalSchema: "dbo",
                principalTable: "Station",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_NotAvailableDate_Station_StationId",
                schema: "dbo",
                table: "NotAvailableDate");

            migrationBuilder.DropIndex(
                name: "IX_NotAvailableDate_StationId",
                schema: "dbo",
                table: "NotAvailableDate");

            migrationBuilder.DropColumn(
                name: "StationId",
                schema: "dbo",
                table: "NotAvailableDate");
        }
    }
}
