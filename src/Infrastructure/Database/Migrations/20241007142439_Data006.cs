﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data006 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CreatedDate",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.DropColumn(
                name: "CreatedUserId",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.DropColumn(
                name: "CreatedDateTime",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                schema: "dbo",
                table: "Station");

            migrationBuilder.DropColumn(
                name: "CreatedUserId",
                schema: "dbo",
                table: "Station");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                schema: "dbo",
                table: "Notification");

            migrationBuilder.DropColumn(
                name: "InsertDateTime",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "CreatedDate",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDate",
                schema: "dbo",
                table: "Vehicle",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "getdate()");

            migrationBuilder.AddColumn<Guid>(
                name: "InsertUserId",
                schema: "dbo",
                table: "Vehicle",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDate",
                schema: "dbo",
                table: "TransactionRequest",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "getdate()");

            migrationBuilder.AddColumn<Guid>(
                name: "StationId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Longitude",
                schema: "dbo",
                table: "Station",
                type: "decimal(18,6)",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AlterColumn<decimal>(
                name: "Latitude",
                schema: "dbo",
                table: "Station",
                type: "decimal(18,6)",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "int");

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDate",
                schema: "dbo",
                table: "Station",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "getdate()");

            migrationBuilder.AddColumn<Guid>(
                name: "InsertUserId",
                schema: "dbo",
                table: "Station",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDate",
                schema: "dbo",
                table: "Notification",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "getdate()");

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDate",
                schema: "dbo",
                table: "ContractProductTransactionLog",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "getdate()");

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDate",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "getdate()");

            migrationBuilder.AddColumn<Guid>(
                name: "StationId",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDate",
                schema: "dbo",
                table: "Contract",
                type: "datetime2",
                nullable: false,
                defaultValueSql: "getdate()");

            migrationBuilder.AddColumn<Guid>(
                name: "StationId",
                schema: "dbo",
                table: "Building",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequest_StationId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "StationId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransaction_StationId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "StationId");

            migrationBuilder.CreateIndex(
                name: "IX_Building_StationId",
                schema: "dbo",
                table: "Building",
                column: "StationId");

            migrationBuilder.AddForeignKey(
                name: "FK_Building_Station_StationId",
                schema: "dbo",
                table: "Building",
                column: "StationId",
                principalSchema: "dbo",
                principalTable: "Station",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductTransaction_Station_StationId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "StationId",
                principalSchema: "dbo",
                principalTable: "Station",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_Station_StationId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "StationId",
                principalSchema: "dbo",
                principalTable: "Station",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Building_Station_StationId",
                schema: "dbo",
                table: "Building");

            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductTransaction_Station_StationId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_Station_StationId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_TransactionRequest_StationId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_ContractProductTransaction_StationId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropIndex(
                name: "IX_Building_StationId",
                schema: "dbo",
                table: "Building");

            migrationBuilder.DropColumn(
                name: "InsertDate",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.DropColumn(
                name: "InsertUserId",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.DropColumn(
                name: "InsertDate",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "StationId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "InsertDate",
                schema: "dbo",
                table: "Station");

            migrationBuilder.DropColumn(
                name: "InsertUserId",
                schema: "dbo",
                table: "Station");

            migrationBuilder.DropColumn(
                name: "InsertDate",
                schema: "dbo",
                table: "Notification");

            migrationBuilder.DropColumn(
                name: "InsertDate",
                schema: "dbo",
                table: "ContractProductTransactionLog");

            migrationBuilder.DropColumn(
                name: "InsertDate",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "StationId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "InsertDate",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropColumn(
                name: "StationId",
                schema: "dbo",
                table: "Building");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                schema: "dbo",
                table: "Vehicle",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "CreatedUserId",
                schema: "dbo",
                table: "Vehicle",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDateTime",
                schema: "dbo",
                table: "TransactionRequest",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AlterColumn<int>(
                name: "Longitude",
                schema: "dbo",
                table: "Station",
                type: "int",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,6)");

            migrationBuilder.AlterColumn<int>(
                name: "Latitude",
                schema: "dbo",
                table: "Station",
                type: "int",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,6)");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                schema: "dbo",
                table: "Station",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<Guid>(
                name: "CreatedUserId",
                schema: "dbo",
                table: "Station",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                schema: "dbo",
                table: "Notification",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "InsertDateTime",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedDate",
                schema: "dbo",
                table: "Contract",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));
        }
    }
}
