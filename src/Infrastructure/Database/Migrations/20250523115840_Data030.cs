﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data030 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProduct_ConsistencyClass_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_User_ApprovedUserId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_ContractProduct_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.AddColumn<string>(
                name: "CanceledNote",
                schema: "dbo",
                table: "TransactionRequest",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CanceledUserId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "UserId",
                schema: "dbo",
                table: "TransactionRequest",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "DriverId",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AlterColumn<decimal>(
                name: "OldAmount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "LeftAmount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Amount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: true,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AddColumn<decimal>(
                name: "LeftWorth",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "OldWorth",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Worth",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Comment",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InsertDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Text = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    Rate = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    CommenterId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ContractProductTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    VehicleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DriverId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Comment", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Comment_ContractProductTransaction_ContractProductTransactionId",
                        column: x => x.ContractProductTransactionId,
                        principalSchema: "dbo",
                        principalTable: "ContractProductTransaction",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_Comment_User_CommenterId",
                        column: x => x.CommenterId,
                        principalSchema: "Users",
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_Comment_User_DriverId",
                        column: x => x.DriverId,
                        principalSchema: "Users",
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                    table.ForeignKey(
                        name: "FK_Comment_Vehicle_VehicleId",
                        column: x => x.VehicleId,
                        principalSchema: "dbo",
                        principalTable: "Vehicle",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.NoAction);
                });

            migrationBuilder.CreateTable(
                name: "ContractProductConsistencyClass",
                schema: "dbo",
                columns: table => new
                {
                    ContractProductId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ConsistencyClassId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractProductConsistencyClass", x => new { x.ContractProductId, x.ConsistencyClassId });
                    table.ForeignKey(
                        name: "FK_ContractProductConsistencyClass_ConsistencyClass_ConsistencyClassId",
                        column: x => x.ConsistencyClassId,
                        principalSchema: "dbo",
                        principalTable: "ConsistencyClass",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ContractProductConsistencyClass_ContractProduct_ContractProductId",
                        column: x => x.ContractProductId,
                        principalSchema: "dbo",
                        principalTable: "ContractProduct",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "CommentFile",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    FileName = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    CommentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CommentFile", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CommentFile_Comment_CommentId",
                        column: x => x.CommentId,
                        principalSchema: "dbo",
                        principalTable: "Comment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequest_CanceledUserId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "CanceledUserId");

            migrationBuilder.CreateIndex(
                name: "IX_TransactionRequest_UserId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransaction_DriverId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "DriverId");

            migrationBuilder.CreateIndex(
                name: "IX_Comment_CommenterId",
                schema: "dbo",
                table: "Comment",
                column: "CommenterId");

            migrationBuilder.CreateIndex(
                name: "IX_Comment_ContractProductTransactionId",
                schema: "dbo",
                table: "Comment",
                column: "ContractProductTransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_Comment_DriverId",
                schema: "dbo",
                table: "Comment",
                column: "DriverId");

            migrationBuilder.CreateIndex(
                name: "IX_Comment_VehicleId",
                schema: "dbo",
                table: "Comment",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_CommentFile_CommentId",
                schema: "dbo",
                table: "CommentFile",
                column: "CommentId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductConsistencyClass_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProductConsistencyClass",
                column: "ConsistencyClassId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductConsistencyClass_ContractProductId_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProductConsistencyClass",
                columns: new[] { "ContractProductId", "ConsistencyClassId" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductTransaction_User_DriverId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "DriverId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_User_ApprovedUserId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ApprovedUserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_User_CanceledUserId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "CanceledUserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_User_UserId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "UserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductTransaction_User_DriverId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_User_ApprovedUserId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_User_CanceledUserId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropForeignKey(
                name: "FK_TransactionRequest_User_UserId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropTable(
                name: "CommentFile",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "ContractProductConsistencyClass",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "Comment",
                schema: "dbo");

            migrationBuilder.DropIndex(
                name: "IX_TransactionRequest_CanceledUserId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_TransactionRequest_UserId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropIndex(
                name: "IX_ContractProductTransaction_DriverId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "CanceledNote",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "CanceledUserId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "UserId",
                schema: "dbo",
                table: "TransactionRequest");

            migrationBuilder.DropColumn(
                name: "DriverId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "LeftWorth",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "OldWorth",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "Worth",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.AlterColumn<decimal>(
                name: "OldAmount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "LeftAmount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldNullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "Amount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: false,
                defaultValue: 0m,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)",
                oldNullable: true);

            migrationBuilder.AddColumn<int>(
                name: "ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct",
                type: "int",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ContractProduct_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct",
                column: "ConsistencyClassId");

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProduct_ConsistencyClass_ConsistencyClassId",
                schema: "dbo",
                table: "ContractProduct",
                column: "ConsistencyClassId",
                principalSchema: "dbo",
                principalTable: "ConsistencyClass",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TransactionRequest_User_ApprovedUserId",
                schema: "dbo",
                table: "TransactionRequest",
                column: "ApprovedUserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id");
        }
    }
}
