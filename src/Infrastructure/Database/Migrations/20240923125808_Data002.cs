﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data002 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CompanyUser_Building_BuildingId",
                schema: "dbo",
                table: "CompanyUser");

            migrationBuilder.DropForeignKey(
                name: "FK_CompanyUser_Company_CompanyId",
                schema: "dbo",
                table: "CompanyUser");

            migrationBuilder.DropForeignKey(
                name: "FK_CompanyUser_User_UserId",
                schema: "dbo",
                table: "CompanyUser");

            migrationBuilder.DropForeignKey(
                name: "FK_Vehicle_VehicleType_CarTypeId",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CompanyUser",
                schema: "dbo",
                table: "CompanyUser");

            migrationBuilder.DropColumn(
                name: "Company",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "Phone",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "AuditPerson",
                schema: "dbo",
                table: "Building");

            migrationBuilder.DropColumn(
                name: "AuditPersonPhone",
                schema: "dbo",
                table: "Building");

            migrationBuilder.RenameTable(
                name: "CompanyUser",
                schema: "dbo",
                newName: "BuildingUser",
                newSchema: "dbo");

            migrationBuilder.RenameColumn(
                name: "CarTypeId",
                schema: "dbo",
                table: "Vehicle",
                newName: "VehicleTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Vehicle_CarTypeId",
                schema: "dbo",
                table: "Vehicle",
                newName: "IX_Vehicle_VehicleTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_CompanyUser_UserId_BuildingId",
                schema: "dbo",
                table: "BuildingUser",
                newName: "IX_BuildingUser_UserId_BuildingId");

            migrationBuilder.RenameIndex(
                name: "IX_CompanyUser_CompanyId",
                schema: "dbo",
                table: "BuildingUser",
                newName: "IX_BuildingUser_CompanyId");

            migrationBuilder.RenameIndex(
                name: "IX_CompanyUser_BuildingId",
                schema: "dbo",
                table: "BuildingUser",
                newName: "IX_BuildingUser_BuildingId");

            migrationBuilder.AddColumn<decimal>(
                name: "Capacity",
                schema: "dbo",
                table: "Vehicle",
                type: "decimal(18,4)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<string>(
                name: "Description",
                schema: "dbo",
                table: "Vehicle",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CompanyId",
                schema: "Users",
                table: "User",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsMaster",
                schema: "Users",
                table: "User",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AlterColumn<decimal>(
                name: "DistanceInDestination",
                schema: "dbo",
                table: "TransactionRequest",
                type: "decimal(18,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "DesiredTotalConcrete",
                schema: "dbo",
                table: "TransactionRequest",
                type: "decimal(18,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AddColumn<string>(
                name: "Data",
                schema: "dbo",
                table: "Notification",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Type",
                schema: "dbo",
                table: "Notification",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PhoneNumber",
                schema: "dbo",
                table: "Department",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AlterColumn<decimal>(
                name: "LeftAmount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Amount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,2)");

            migrationBuilder.AddColumn<Guid>(
                name: "AuditPersonId",
                schema: "dbo",
                table: "Building",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_BuildingUser",
                schema: "dbo",
                table: "BuildingUser",
                columns: new[] { "UserId", "BuildingId" });

            migrationBuilder.CreateTable(
                name: "ContractProductTransaction",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ContractId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TransactionRequestId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    VehicleId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    InsertDateTime = table.Column<DateTime>(type: "datetime2", nullable: false),
                    InsertUserId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    StatusId = table.Column<int>(type: "int", nullable: false),
                    SendingAmount = table.Column<decimal>(type: "decimal(18,4)", nullable: false),
                    DocumentNo = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractProductTransaction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContractProductTransaction_ContractProductTransactionStatus_StatusId",
                        column: x => x.StatusId,
                        principalSchema: "dbo",
                        principalTable: "ContractProductTransactionStatus",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ContractProductTransaction_Contract_ContractId",
                        column: x => x.ContractId,
                        principalSchema: "dbo",
                        principalTable: "Contract",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ContractProductTransaction_TransactionRequest_TransactionRequestId",
                        column: x => x.TransactionRequestId,
                        principalSchema: "dbo",
                        principalTable: "TransactionRequest",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ContractProductTransaction_Vehicle_VehicleId",
                        column: x => x.VehicleId,
                        principalSchema: "dbo",
                        principalTable: "Vehicle",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "NotAvailableDate",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_NotAvailableDate", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "StationDepartment",
                schema: "dbo",
                columns: table => new
                {
                    StationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DepartmentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_StationDepartment", x => new { x.StationId, x.DepartmentId });
                    table.ForeignKey(
                        name: "FK_StationDepartment_Department_DepartmentId",
                        column: x => x.DepartmentId,
                        principalSchema: "dbo",
                        principalTable: "Department",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_StationDepartment_Station_StationId",
                        column: x => x.StationId,
                        principalSchema: "dbo",
                        principalTable: "Station",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "UserDepartment",
                schema: "dbo",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    DepartmentId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserDepartment", x => new { x.UserId, x.DepartmentId });
                    table.ForeignKey(
                        name: "FK_UserDepartment_Department_DepartmentId",
                        column: x => x.DepartmentId,
                        principalSchema: "dbo",
                        principalTable: "Department",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_UserDepartment_User_UserId",
                        column: x => x.UserId,
                        principalSchema: "Users",
                        principalTable: "User",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ContractProductTransactionLog",
                schema: "dbo",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ContractProductTransactionId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ContractProductTransactionLog", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ContractProductTransactionLog_ContractProductTransaction_ContractProductTransactionId",
                        column: x => x.ContractProductTransactionId,
                        principalSchema: "dbo",
                        principalTable: "ContractProductTransaction",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.InsertData(
                schema: "dbo",
                table: "TransactionStatus",
                columns: new[] { "Id", "CustomerName", "Name" },
                values: new object[] { 7, "İptal Talebi", "İptal Talebi" });

            migrationBuilder.CreateIndex(
                name: "IX_User_CompanyId",
                schema: "Users",
                table: "User",
                column: "CompanyId");

            migrationBuilder.CreateIndex(
                name: "IX_Building_AuditPersonId",
                schema: "dbo",
                table: "Building",
                column: "AuditPersonId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransaction_ContractId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "ContractId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransaction_StatusId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "StatusId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransaction_TransactionRequestId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "TransactionRequestId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransaction_VehicleId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "VehicleId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransactionLog_ContractProductTransactionId",
                schema: "dbo",
                table: "ContractProductTransactionLog",
                column: "ContractProductTransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_StationDepartment_DepartmentId",
                schema: "dbo",
                table: "StationDepartment",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_StationDepartment_StationId_DepartmentId",
                schema: "dbo",
                table: "StationDepartment",
                columns: new[] { "StationId", "DepartmentId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_UserDepartment_DepartmentId",
                schema: "dbo",
                table: "UserDepartment",
                column: "DepartmentId");

            migrationBuilder.CreateIndex(
                name: "IX_UserDepartment_UserId_DepartmentId",
                schema: "dbo",
                table: "UserDepartment",
                columns: new[] { "UserId", "DepartmentId" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Building_User_AuditPersonId",
                schema: "dbo",
                table: "Building",
                column: "AuditPersonId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BuildingUser_Building_BuildingId",
                schema: "dbo",
                table: "BuildingUser",
                column: "BuildingId",
                principalSchema: "dbo",
                principalTable: "Building",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_BuildingUser_Company_CompanyId",
                schema: "dbo",
                table: "BuildingUser",
                column: "CompanyId",
                principalSchema: "dbo",
                principalTable: "Company",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_BuildingUser_User_UserId",
                schema: "dbo",
                table: "BuildingUser",
                column: "UserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_User_Company_CompanyId",
                schema: "Users",
                table: "User",
                column: "CompanyId",
                principalSchema: "dbo",
                principalTable: "Company",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Vehicle_VehicleType_VehicleTypeId",
                schema: "dbo",
                table: "Vehicle",
                column: "VehicleTypeId",
                principalSchema: "dbo",
                principalTable: "VehicleType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Building_User_AuditPersonId",
                schema: "dbo",
                table: "Building");

            migrationBuilder.DropForeignKey(
                name: "FK_BuildingUser_Building_BuildingId",
                schema: "dbo",
                table: "BuildingUser");

            migrationBuilder.DropForeignKey(
                name: "FK_BuildingUser_Company_CompanyId",
                schema: "dbo",
                table: "BuildingUser");

            migrationBuilder.DropForeignKey(
                name: "FK_BuildingUser_User_UserId",
                schema: "dbo",
                table: "BuildingUser");

            migrationBuilder.DropForeignKey(
                name: "FK_User_Company_CompanyId",
                schema: "Users",
                table: "User");

            migrationBuilder.DropForeignKey(
                name: "FK_Vehicle_VehicleType_VehicleTypeId",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.DropTable(
                name: "ContractProductTransactionLog",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "NotAvailableDate",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "StationDepartment",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "UserDepartment",
                schema: "dbo");

            migrationBuilder.DropTable(
                name: "ContractProductTransaction",
                schema: "dbo");

            migrationBuilder.DropIndex(
                name: "IX_User_CompanyId",
                schema: "Users",
                table: "User");

            migrationBuilder.DropIndex(
                name: "IX_Building_AuditPersonId",
                schema: "dbo",
                table: "Building");

            migrationBuilder.DropPrimaryKey(
                name: "PK_BuildingUser",
                schema: "dbo",
                table: "BuildingUser");

            migrationBuilder.DeleteData(
                schema: "dbo",
                table: "TransactionStatus",
                keyColumn: "Id",
                keyValue: 7);

            migrationBuilder.DropColumn(
                name: "Capacity",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.DropColumn(
                name: "Description",
                schema: "dbo",
                table: "Vehicle");

            migrationBuilder.DropColumn(
                name: "CompanyId",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "IsMaster",
                schema: "Users",
                table: "User");

            migrationBuilder.DropColumn(
                name: "Data",
                schema: "dbo",
                table: "Notification");

            migrationBuilder.DropColumn(
                name: "Type",
                schema: "dbo",
                table: "Notification");

            migrationBuilder.DropColumn(
                name: "PhoneNumber",
                schema: "dbo",
                table: "Department");

            migrationBuilder.DropColumn(
                name: "AuditPersonId",
                schema: "dbo",
                table: "Building");

            migrationBuilder.RenameTable(
                name: "BuildingUser",
                schema: "dbo",
                newName: "CompanyUser",
                newSchema: "dbo");

            migrationBuilder.RenameColumn(
                name: "VehicleTypeId",
                schema: "dbo",
                table: "Vehicle",
                newName: "CarTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Vehicle_VehicleTypeId",
                schema: "dbo",
                table: "Vehicle",
                newName: "IX_Vehicle_CarTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_BuildingUser_UserId_BuildingId",
                schema: "dbo",
                table: "CompanyUser",
                newName: "IX_CompanyUser_UserId_BuildingId");

            migrationBuilder.RenameIndex(
                name: "IX_BuildingUser_CompanyId",
                schema: "dbo",
                table: "CompanyUser",
                newName: "IX_CompanyUser_CompanyId");

            migrationBuilder.RenameIndex(
                name: "IX_BuildingUser_BuildingId",
                schema: "dbo",
                table: "CompanyUser",
                newName: "IX_CompanyUser_BuildingId");

            migrationBuilder.AddColumn<string>(
                name: "Company",
                schema: "Users",
                table: "User",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Phone",
                schema: "Users",
                table: "User",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "DistanceInDestination",
                schema: "dbo",
                table: "TransactionRequest",
                type: "decimal(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "DesiredTotalConcrete",
                schema: "dbo",
                table: "TransactionRequest",
                type: "decimal(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "LeftAmount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AlterColumn<decimal>(
                name: "Amount",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,2)",
                nullable: false,
                oldClrType: typeof(decimal),
                oldType: "decimal(18,4)");

            migrationBuilder.AddColumn<string>(
                name: "AuditPerson",
                schema: "dbo",
                table: "Building",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AuditPersonPhone",
                schema: "dbo",
                table: "Building",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_CompanyUser",
                schema: "dbo",
                table: "CompanyUser",
                columns: new[] { "UserId", "BuildingId" });

            migrationBuilder.AddForeignKey(
                name: "FK_CompanyUser_Building_BuildingId",
                schema: "dbo",
                table: "CompanyUser",
                column: "BuildingId",
                principalSchema: "dbo",
                principalTable: "Building",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_CompanyUser_Company_CompanyId",
                schema: "dbo",
                table: "CompanyUser",
                column: "CompanyId",
                principalSchema: "dbo",
                principalTable: "Company",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CompanyUser_User_UserId",
                schema: "dbo",
                table: "CompanyUser",
                column: "UserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Vehicle_VehicleType_CarTypeId",
                schema: "dbo",
                table: "Vehicle",
                column: "CarTypeId",
                principalSchema: "dbo",
                principalTable: "VehicleType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
