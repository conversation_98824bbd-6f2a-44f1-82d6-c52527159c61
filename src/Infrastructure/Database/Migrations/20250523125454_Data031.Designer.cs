﻿// <auto-generated />
using System;
using Database.Context;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Database.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250523125454_Data031")]
    partial class Data031
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("dbo")
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Domain.Account.Page", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Method")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Url")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.HasIndex("Url", "Method")
                        .IsUnique();

                    b.ToTable("Page", "Users");
                });

            modelBuilder.Entity("Domain.Account.PageRule", b =>
                {
                    b.Property<Guid>("PageId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("PageId", "UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.HasIndex("UserId");

                    b.HasIndex("PageId", "UserId", "RoleId")
                        .IsUnique();

                    b.ToTable("PageRule", "Users");
                });

            modelBuilder.Entity("Domain.Account.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("Role", "Users");

                    b.HasData(
                        new
                        {
                            Id = new Guid("439aeada-02d0-4962-9dfd-bc41363461a3"),
                            ConcurrencyStamp = "439aeada-02d0-4962-9dfd-bc41363461a3",
                            Name = "Admin",
                            NormalizedName = "ADMIN"
                        },
                        new
                        {
                            Id = new Guid("0f2a72f5-7dcf-4301-9240-ab806d804007"),
                            ConcurrencyStamp = "0f2a72f5-7dcf-4301-9240-ab806d804007",
                            Name = "Süper Admin",
                            NormalizedName = "SUPERADMIN"
                        },
                        new
                        {
                            Id = new Guid("44a832a5-fbe2-43eb-8c27-0e5050abd9ea"),
                            ConcurrencyStamp = "44a832a5-fbe2-43eb-8c27-0e5050abd9ea",
                            Name = "Şantiye Görevlisi",
                            NormalizedName = "BUILDING"
                        },
                        new
                        {
                            Id = new Guid("aaaa544b-b5f7-4bad-8c68-22c87005bfac"),
                            ConcurrencyStamp = "aaaa544b-b5f7-4bad-8c68-22c87005bfac",
                            Name = "Şirket Yetkilisi",
                            NormalizedName = "COMPANY"
                        },
                        new
                        {
                            Id = new Guid("734354f3-c622-4750-a11c-04ab6f0fa497"),
                            ConcurrencyStamp = "734354f3-c622-4750-a11c-04ab6f0fa497",
                            Name = "Yapı Denetim Personeli",
                            NormalizedName = "CONTROL"
                        },
                        new
                        {
                            Id = new Guid("98b3e5cc-cf74-4ee9-bc6a-6871c51a6469"),
                            ConcurrencyStamp = "98b3e5cc-cf74-4ee9-bc6a-6871c51a6469",
                            Name = "Şoför",
                            NormalizedName = "DRIVER"
                        },
                        new
                        {
                            Id = new Guid("f6bcd1b9-328e-4008-9da2-2ce37f35940e"),
                            ConcurrencyStamp = "f6bcd1b9-328e-4008-9da2-2ce37f35940e",
                            Name = "Satış Temsilcisi",
                            NormalizedName = "SALES"
                        },
                        new
                        {
                            Id = new Guid("5351cc0b-20d7-4dcc-8fba-90c797b3f5b8"),
                            ConcurrencyStamp = "5351cc0b-20d7-4dcc-8fba-90c797b3f5b8",
                            Name = "Santral Çalışanı",
                            NormalizedName = "STATION"
                        },
                        new
                        {
                            Id = new Guid("d9530b95-6cc4-4e13-8225-6d65d5b1e617"),
                            ConcurrencyStamp = "d9530b95-6cc4-4e13-8225-6d65d5b1e617",
                            Name = "Kalıpçı",
                            NormalizedName = "MOULDER"
                        });
                });

            modelBuilder.Entity("Domain.Account.RoleClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("RoleClaim", "Users");
                });

            modelBuilder.Entity("Domain.Account.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CompanyName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FCMDeviceId")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime?>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsMaster")
                        .HasColumnType("bit");

                    b.Property<bool>("Kvkk")
                        .HasColumnType("bit");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Surname")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("TaxNumber")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("CompanyId");

                    b.HasIndex("Email")
                        .IsUnique()
                        .HasFilter("[Email] IS NOT NULL");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("User", "Users");
                });

            modelBuilder.Entity("Domain.Account.UserClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("UserClaim", "Users");
                });

            modelBuilder.Entity("Domain.Account.UserDepartment", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserId", "DepartmentId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("UserId", "DepartmentId")
                        .IsUnique();

                    b.ToTable("UserDepartment", "dbo");
                });

            modelBuilder.Entity("Domain.Account.UserLogin", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("ProviderDisplayName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("UserLogin", "Users");
                });

            modelBuilder.Entity("Domain.Account.UserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UserRole", "Users");
                });

            modelBuilder.Entity("Domain.Account.UserToken", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Value")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("UserToken", "Users");
                });

            modelBuilder.Entity("Domain.Catalog.Product", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<bool>("Refundable")
                        .HasColumnType("bit");

                    b.Property<decimal>("RefundableAmount")
                        .HasColumnType("decimal(18,4)");

                    b.HasKey("Id");

                    b.ToTable("Product", "dbo");
                });

            modelBuilder.Entity("Domain.Companies.Building", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Address")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid?>("AuditPersonId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CityId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("DistrictId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<decimal>("Latitude")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("Longitude")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid?>("StateProvinceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("StationId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("AuditPersonId");

                    b.HasIndex("CityId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("DistrictId");

                    b.HasIndex("StateProvinceId");

                    b.HasIndex("StationId");

                    b.ToTable("Building", "dbo");
                });

            modelBuilder.Entity("Domain.Companies.BuildingUser", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("BuildingId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UserId", "BuildingId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("UserId", "BuildingId")
                        .IsUnique();

                    b.ToTable("BuildingUser", "dbo");
                });

            modelBuilder.Entity("Domain.Companies.Company", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AccountingCode")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<string>("Address")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Email")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Phone")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("TaxNumber")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("TaxOffice")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("Company", "dbo");
                });

            modelBuilder.Entity("Domain.Contracts.Contract", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("Active")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("ApprovedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ApprovedNote")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid?>("ApprovedUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CompanyId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<Guid>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("IsApproved")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<int?>("PaymentPlanId")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime?>("UpdateDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("UpdatedUserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedUserId");

                    b.HasIndex("CompanyId");

                    b.HasIndex("PaymentPlanId");

                    b.ToTable("Contract", "dbo");
                });

            modelBuilder.Entity("Domain.Contracts.ContractFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FileName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.ToTable("ContractFile", "dbo");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProduct", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("Amount")
                        .HasColumnType("decimal(18,4)");

                    b.Property<Guid>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool?>("IncludeTax")
                        .HasColumnType("bit");

                    b.Property<decimal?>("LeftAmount")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("LeftWorth")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("OldAmount")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("OldWorth")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime?>("PriceGuaranteeDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("Worth")
                        .HasColumnType("decimal(18,4)");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("ProductId");

                    b.ToTable("ContractProduct", "dbo");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProductBuilding", b =>
                {
                    b.Property<Guid>("ContractProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BuildingId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ContractProductId", "BuildingId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("ContractProductId", "BuildingId")
                        .IsUnique();

                    b.ToTable("ContractProductBuilding", "dbo");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProductConsistencyClass", b =>
                {
                    b.Property<Guid>("ContractProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ConsistencyClassId")
                        .HasColumnType("int");

                    b.HasKey("ContractProductId", "ConsistencyClassId");

                    b.HasIndex("ConsistencyClassId");

                    b.HasIndex("ContractProductId", "ConsistencyClassId")
                        .IsUnique();

                    b.ToTable("ContractProductConsistencyClass", "dbo");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProductTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("DocumentNo")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("DriverId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsRedirect")
                        .HasColumnType("bit");

                    b.Property<decimal>("SendingAmount")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<Guid?>("StationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("StatusId")
                        .HasColumnType("int");

                    b.Property<Guid>("TransactionRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<Guid>("VehicleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ContractId");

                    b.HasIndex("DriverId");

                    b.HasIndex("StationId");

                    b.HasIndex("StatusId");

                    b.HasIndex("TransactionRequestId");

                    b.HasIndex("TypeId");

                    b.HasIndex("VehicleId");

                    b.ToTable("ContractProductTransaction", "dbo");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProductTransactionLog", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ContractProductTransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.HasKey("Id");

                    b.HasIndex("ContractProductTransactionId");

                    b.ToTable("ContractProductTransactionLog", "dbo");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProductTransactionStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("ContractProductTransactionStatus", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Beklemede"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Yolda"
                        },
                        new
                        {
                            Id = 3,
                            Name = "İşlemde"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Tamamlandı"
                        },
                        new
                        {
                            Id = 5,
                            Name = "İptal"
                        });
                });

            modelBuilder.Entity("Domain.Contracts.PaymentPlan", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("PaymentPlan", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Peşin"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Kredi Kartı"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Kredi Kartı Vadeli"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Çek 2 Ay Vadeli"
                        },
                        new
                        {
                            Id = 5,
                            Name = "Ay + 30 Gün"
                        });
                });

            modelBuilder.Entity("Domain.Contracts.TransactionType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("TransactionType", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Normal"
                        },
                        new
                        {
                            Id = 2,
                            Name = "İade"
                        });
                });

            modelBuilder.Entity("Domain.General.City", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DistrictId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.HasIndex("DistrictId");

                    b.ToTable("City", "dbo");
                });

            modelBuilder.Entity("Domain.General.Comment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CommenterId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ContractProductTransactionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DriverId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("InsertDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("Rate")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Text")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("VehicleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("CommenterId");

                    b.HasIndex("ContractProductTransactionId");

                    b.HasIndex("DriverId");

                    b.HasIndex("VehicleId");

                    b.ToTable("Comment", "dbo");
                });

            modelBuilder.Entity("Domain.General.CommentFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("CommentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FileName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.HasIndex("CommentId");

                    b.ToTable("CommentFile", "dbo");
                });

            modelBuilder.Entity("Domain.General.District", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("StateProvinceId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StateProvinceId");

                    b.ToTable("District", "dbo");
                });

            modelBuilder.Entity("Domain.General.Gallery", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Data")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("FileName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Type")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("Gallery", "dbo");
                });

            modelBuilder.Entity("Domain.General.NotAvailableDate", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid?>("StationId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("StationId");

                    b.ToTable("NotAvailableDate", "dbo");
                });

            modelBuilder.Entity("Domain.General.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Data")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsRead")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSend")
                        .HasColumnType("bit");

                    b.Property<string>("Note")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Title")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Type")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("Notification", "dbo");
                });

            modelBuilder.Entity("Domain.General.StateProvince", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("StateProvince", "dbo");
                });

            modelBuilder.Entity("Domain.Stations.Department", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsBase")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("PhoneNumber")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("Department", "dbo");
                });

            modelBuilder.Entity("Domain.Stations.PompType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("PompType", "dbo");
                });

            modelBuilder.Entity("Domain.Stations.Station", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Address")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<int>("Capacity")
                        .HasColumnType("int");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<DateTime>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<decimal>("Latitude")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal>("Longitude")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<int>("StatusId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("StatusId");

                    b.ToTable("Station", "dbo");
                });

            modelBuilder.Entity("Domain.Stations.StationDepartment", b =>
                {
                    b.Property<Guid>("StationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("StationId", "DepartmentId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("StationId", "DepartmentId")
                        .IsUnique();

                    b.ToTable("StationDepartment", "dbo");
                });

            modelBuilder.Entity("Domain.Stations.StationStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("StationStatus", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            DisplayName = "Aktif",
                            Name = "Aktif"
                        },
                        new
                        {
                            Id = 2,
                            DisplayName = "Pasif",
                            Name = "Pasif"
                        });
                });

            modelBuilder.Entity("Domain.Stations.Vehicle", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("Capacity")
                        .HasColumnType("decimal(18,4)");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("DriverId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<Guid?>("InsertUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Plate")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<int?>("PompTypeId")
                        .HasColumnType("int");

                    b.Property<int>("StatusId")
                        .HasColumnType("int");

                    b.Property<int>("VehicleTypeId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DriverId");

                    b.HasIndex("PompTypeId");

                    b.HasIndex("StatusId");

                    b.HasIndex("VehicleTypeId");

                    b.ToTable("Vehicle", "dbo");
                });

            modelBuilder.Entity("Domain.Stations.VehicleStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("VehicleStatus", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Aktif"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Pasif"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Tamirde"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Kullanım Dışı"
                        });
                });

            modelBuilder.Entity("Domain.Stations.VehicleType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("VehicleType", "dbo");
                });

            modelBuilder.Entity("Domain.Transactions.ConcreteLocation", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("SubItem")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("ConcreteLocation", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Temel",
                            SubItem = "S3,S4"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Kolon",
                            SubItem = "S3"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Kiriş",
                            SubItem = "S3"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Tabliye",
                            SubItem = "S3,S4"
                        },
                        new
                        {
                            Id = 5,
                            Name = "İstinat Duvarı",
                            SubItem = "S3"
                        },
                        new
                        {
                            Id = 6,
                            Name = "Merdiven",
                            SubItem = "S2,S3"
                        },
                        new
                        {
                            Id = 7,
                            Name = "Hatıl",
                            SubItem = "S3,S4"
                        },
                        new
                        {
                            Id = 8,
                            Name = "Diğer",
                            SubItem = "S1,S2,S3,S4,S5"
                        });
                });

            modelBuilder.Entity("Domain.Transactions.ConcreteOption", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("ConcreteOption", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Normal"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Brüt"
                        },
                        new
                        {
                            Id = 3,
                            Name = "Su Geçirimsiz"
                        },
                        new
                        {
                            Id = 4,
                            Name = "Katkısız"
                        },
                        new
                        {
                            Id = 5,
                            Name = "Antifrizli"
                        },
                        new
                        {
                            Id = 6,
                            Name = "Sabit"
                        },
                        new
                        {
                            Id = 7,
                            Name = "Dsi"
                        },
                        new
                        {
                            Id = 8,
                            Name = "Bahçe Duvarı"
                        });
                });

            modelBuilder.Entity("Domain.Transactions.ConsistencyClass", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("ConsistencyClass", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Description = "1 cm - 4 cm ÇÖKME ARALIĞI",
                            Name = "S1"
                        },
                        new
                        {
                            Id = 2,
                            Description = "5 cm - 9 cm  ÇÖKME ARALIĞI",
                            Name = "S2"
                        },
                        new
                        {
                            Id = 3,
                            Description = "10 cm - 15 cm  ÇÖKME ARALIĞI",
                            Name = "S3"
                        },
                        new
                        {
                            Id = 4,
                            Description = "16 cm - 21 cm  ÇÖKME ARALIĞI",
                            Name = "S4"
                        },
                        new
                        {
                            Id = 5,
                            Description = "22 cm ve üzeri ÇÖKME ARALIĞI",
                            Name = "S5"
                        });
                });

            modelBuilder.Entity("Domain.Transactions.LabResult", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("FileName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("TransactionRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TransactionRequestId");

                    b.ToTable("LabResult", "dbo");
                });

            modelBuilder.Entity("Domain.Transactions.TransactionRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("ApprovedDateTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("ApprovedNote")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid?>("ApprovedUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("BuildingId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CanceledNote")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid?>("CanceledUserId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int?>("CarTypeId")
                        .HasColumnType("int");

                    b.Property<int?>("ConcreteLocationId")
                        .HasColumnType("int");

                    b.Property<int?>("ConsistencyClassId")
                        .HasColumnType("int");

                    b.Property<Guid?>("ContractId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DesiredDateTime")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("DesiredTotalConcrete")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("DistanceInDestination")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime>("InsertDate")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getdate()");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("bit");

                    b.Property<string>("Note")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<int?>("PompTypeId")
                        .HasColumnType("int");

                    b.Property<Guid?>("ProductId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("RejectNote")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<Guid>("RequestedPersonId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("StationId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("StatusId")
                        .HasColumnType("int");

                    b.Property<decimal?>("TotalConcreteRefundable")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TotalConcreteRemaining")
                        .HasColumnType("decimal(18,4)");

                    b.Property<decimal?>("TotalConcreteSent")
                        .HasColumnType("decimal(18,4)");

                    b.Property<DateTime?>("TransactionEndDateTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("TransactionStartDateTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("TransactionrequestTypeId")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(1);

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ApprovedUserId");

                    b.HasIndex("BuildingId");

                    b.HasIndex("CanceledUserId");

                    b.HasIndex("CarTypeId");

                    b.HasIndex("ConcreteLocationId");

                    b.HasIndex("ConsistencyClassId");

                    b.HasIndex("ContractId");

                    b.HasIndex("PompTypeId");

                    b.HasIndex("ProductId");

                    b.HasIndex("RequestedPersonId");

                    b.HasIndex("StationId");

                    b.HasIndex("StatusId");

                    b.HasIndex("TransactionrequestTypeId");

                    b.HasIndex("UserId");

                    b.ToTable("TransactionRequest", "dbo");
                });

            modelBuilder.Entity("Domain.Transactions.TransactionRequestConcreteOption", b =>
                {
                    b.Property<Guid>("TransactionRequestId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ConcreteOptionId")
                        .HasColumnType("int");

                    b.HasKey("TransactionRequestId", "ConcreteOptionId");

                    b.HasIndex("ConcreteOptionId");

                    b.HasIndex("TransactionRequestId", "ConcreteOptionId")
                        .IsUnique();

                    b.ToTable("TransactionRequestConcreteOption", "dbo");
                });

            modelBuilder.Entity("Domain.Transactions.TransactionRequestType", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("TransactionRequestType", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            Name = "Normal"
                        },
                        new
                        {
                            Id = 2,
                            Name = "Keşif Talebi"
                        });
                });

            modelBuilder.Entity("Domain.Transactions.TransactionStatus", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("CustomerName")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)");

                    b.HasKey("Id");

                    b.ToTable("TransactionStatus", "dbo");

                    b.HasData(
                        new
                        {
                            Id = 1,
                            CustomerName = "Onay Bekliyor",
                            Name = "Onay Bekliyor"
                        },
                        new
                        {
                            Id = 2,
                            CustomerName = "Planlanacak",
                            Name = "Planlanacak"
                        },
                        new
                        {
                            Id = 3,
                            CustomerName = "Planlamada",
                            Name = "Planlamada"
                        },
                        new
                        {
                            Id = 4,
                            CustomerName = "Planlandı",
                            Name = "Planlandı"
                        },
                        new
                        {
                            Id = 5,
                            CustomerName = "Döküm Devam Ediyor",
                            Name = "Döküm Devam Ediyor"
                        },
                        new
                        {
                            Id = 6,
                            CustomerName = "Tamamlandı",
                            Name = "Tamamlandı"
                        },
                        new
                        {
                            Id = 7,
                            CustomerName = "İptal Talebi",
                            Name = "İptal Talebi"
                        },
                        new
                        {
                            Id = 8,
                            CustomerName = "Reddedildi",
                            Name = "Reddedildi"
                        });
                });

            modelBuilder.Entity("Domain.Account.PageRule", b =>
                {
                    b.HasOne("Domain.Account.Page", "Page")
                        .WithMany()
                        .HasForeignKey("PageId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Account.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Account.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Page");

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Account.RoleClaim", b =>
                {
                    b.HasOne("Domain.Account.Role", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Domain.Account.User", b =>
                {
                    b.HasOne("Domain.Companies.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId");

                    b.Navigation("Company");
                });

            modelBuilder.Entity("Domain.Account.UserClaim", b =>
                {
                    b.HasOne("Domain.Account.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Domain.Account.UserDepartment", b =>
                {
                    b.HasOne("Domain.Stations.Department", "Department")
                        .WithMany("UserDepartment")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Account.User", "User")
                        .WithMany("UserDepartment")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Account.UserLogin", b =>
                {
                    b.HasOne("Domain.Account.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Domain.Account.UserRole", b =>
                {
                    b.HasOne("Domain.Account.Role", "Role")
                        .WithMany("UserRole")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Account.User", "User")
                        .WithMany("UserRole")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Account.UserToken", b =>
                {
                    b.HasOne("Domain.Account.User", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Domain.Companies.Building", b =>
                {
                    b.HasOne("Domain.Account.User", "AuditPerson")
                        .WithMany()
                        .HasForeignKey("AuditPersonId");

                    b.HasOne("Domain.General.City", "City")
                        .WithMany()
                        .HasForeignKey("CityId");

                    b.HasOne("Domain.Companies.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.General.District", "District")
                        .WithMany()
                        .HasForeignKey("DistrictId");

                    b.HasOne("Domain.General.StateProvince", "StateProvince")
                        .WithMany()
                        .HasForeignKey("StateProvinceId");

                    b.HasOne("Domain.Stations.Station", "Station")
                        .WithMany()
                        .HasForeignKey("StationId");

                    b.Navigation("AuditPerson");

                    b.Navigation("City");

                    b.Navigation("Company");

                    b.Navigation("District");

                    b.Navigation("StateProvince");

                    b.Navigation("Station");
                });

            modelBuilder.Entity("Domain.Companies.BuildingUser", b =>
                {
                    b.HasOne("Domain.Companies.Building", "Building")
                        .WithMany("BuildingUser")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Companies.Company", null)
                        .WithMany("CompanyUser")
                        .HasForeignKey("CompanyId");

                    b.HasOne("Domain.Account.User", "User")
                        .WithMany("BuildingUser")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Building");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Contracts.Contract", b =>
                {
                    b.HasOne("Domain.Account.User", "ApprovedUser")
                        .WithMany()
                        .HasForeignKey("ApprovedUserId");

                    b.HasOne("Domain.Companies.Company", "Company")
                        .WithMany()
                        .HasForeignKey("CompanyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Contracts.PaymentPlan", "PaymentPlan")
                        .WithMany()
                        .HasForeignKey("PaymentPlanId");

                    b.Navigation("ApprovedUser");

                    b.Navigation("Company");

                    b.Navigation("PaymentPlan");
                });

            modelBuilder.Entity("Domain.Contracts.ContractFile", b =>
                {
                    b.HasOne("Domain.Contracts.Contract", "Contract")
                        .WithMany("ContractFile")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProduct", b =>
                {
                    b.HasOne("Domain.Contracts.Contract", "Contract")
                        .WithMany("ContractProduct")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Catalog.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");

                    b.Navigation("Product");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProductBuilding", b =>
                {
                    b.HasOne("Domain.Companies.Building", "Building")
                        .WithMany("ContractProductBuilding")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Contracts.ContractProduct", "ContractProduct")
                        .WithMany("ContractProductBuilding")
                        .HasForeignKey("ContractProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Building");

                    b.Navigation("ContractProduct");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProductConsistencyClass", b =>
                {
                    b.HasOne("Domain.Transactions.ConsistencyClass", "ConsistencyClass")
                        .WithMany("ContractProductConsistencyClass")
                        .HasForeignKey("ConsistencyClassId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Contracts.ContractProduct", "ContractProduct")
                        .WithMany("ContractProductConsistencyClass")
                        .HasForeignKey("ContractProductId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("ConsistencyClass");

                    b.Navigation("ContractProduct");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProductTransaction", b =>
                {
                    b.HasOne("Domain.Contracts.Contract", "Contract")
                        .WithMany()
                        .HasForeignKey("ContractId");

                    b.HasOne("Domain.Account.User", "Driver")
                        .WithMany()
                        .HasForeignKey("DriverId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Stations.Station", "Station")
                        .WithMany()
                        .HasForeignKey("StationId");

                    b.HasOne("Domain.Contracts.ContractProductTransactionStatus", "Status")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Transactions.TransactionRequest", "TransactionRequest")
                        .WithMany("ContractProductTransaction")
                        .HasForeignKey("TransactionRequestId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Contracts.TransactionType", "Type")
                        .WithMany()
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Stations.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Contract");

                    b.Navigation("Driver");

                    b.Navigation("Station");

                    b.Navigation("Status");

                    b.Navigation("TransactionRequest");

                    b.Navigation("Type");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProductTransactionLog", b =>
                {
                    b.HasOne("Domain.Contracts.ContractProductTransaction", "ContractProductTransaction")
                        .WithMany()
                        .HasForeignKey("ContractProductTransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ContractProductTransaction");
                });

            modelBuilder.Entity("Domain.General.City", b =>
                {
                    b.HasOne("Domain.General.District", "District")
                        .WithMany("City")
                        .HasForeignKey("DistrictId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("District");
                });

            modelBuilder.Entity("Domain.General.Comment", b =>
                {
                    b.HasOne("Domain.Account.User", "Commenter")
                        .WithMany()
                        .HasForeignKey("CommenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Contracts.ContractProductTransaction", "ContractProductTransaction")
                        .WithMany()
                        .HasForeignKey("ContractProductTransactionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Account.User", "Driver")
                        .WithMany()
                        .HasForeignKey("DriverId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Stations.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Commenter");

                    b.Navigation("ContractProductTransaction");

                    b.Navigation("Driver");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Domain.General.CommentFile", b =>
                {
                    b.HasOne("Domain.General.Comment", "Comment")
                        .WithMany("CommentFile")
                        .HasForeignKey("CommentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Comment");
                });

            modelBuilder.Entity("Domain.General.District", b =>
                {
                    b.HasOne("Domain.General.StateProvince", "StateProvince")
                        .WithMany("District")
                        .HasForeignKey("StateProvinceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StateProvince");
                });

            modelBuilder.Entity("Domain.General.NotAvailableDate", b =>
                {
                    b.HasOne("Domain.Stations.Station", "Station")
                        .WithMany()
                        .HasForeignKey("StationId");

                    b.Navigation("Station");
                });

            modelBuilder.Entity("Domain.General.Notification", b =>
                {
                    b.HasOne("Domain.Account.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Domain.Stations.Station", b =>
                {
                    b.HasOne("Domain.Stations.StationStatus", "Status")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Status");
                });

            modelBuilder.Entity("Domain.Stations.StationDepartment", b =>
                {
                    b.HasOne("Domain.Stations.Department", "Department")
                        .WithMany("StationDepartment")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Stations.Station", "Station")
                        .WithMany("StationDepartment")
                        .HasForeignKey("StationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Station");
                });

            modelBuilder.Entity("Domain.Stations.Vehicle", b =>
                {
                    b.HasOne("Domain.Account.User", "Driver")
                        .WithMany("Vehicle")
                        .HasForeignKey("DriverId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Stations.PompType", "PompType")
                        .WithMany()
                        .HasForeignKey("PompTypeId");

                    b.HasOne("Domain.Stations.VehicleStatus", "Status")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Stations.VehicleType", "VehicleType")
                        .WithMany()
                        .HasForeignKey("VehicleTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Driver");

                    b.Navigation("PompType");

                    b.Navigation("Status");

                    b.Navigation("VehicleType");
                });

            modelBuilder.Entity("Domain.Transactions.LabResult", b =>
                {
                    b.HasOne("Domain.Transactions.TransactionRequest", "TransactionRequest")
                        .WithMany("LabResult")
                        .HasForeignKey("TransactionRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TransactionRequest");
                });

            modelBuilder.Entity("Domain.Transactions.TransactionRequest", b =>
                {
                    b.HasOne("Domain.Account.User", "ApprovedUser")
                        .WithMany()
                        .HasForeignKey("ApprovedUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Companies.Building", "Building")
                        .WithMany("TransactionRequest")
                        .HasForeignKey("BuildingId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Account.User", "CanceledUser")
                        .WithMany()
                        .HasForeignKey("CanceledUserId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Stations.VehicleType", "CarType")
                        .WithMany()
                        .HasForeignKey("CarTypeId");

                    b.HasOne("Domain.Transactions.ConcreteLocation", "ConcreteLocation")
                        .WithMany()
                        .HasForeignKey("ConcreteLocationId");

                    b.HasOne("Domain.Transactions.ConsistencyClass", "ConsistencyClass")
                        .WithMany()
                        .HasForeignKey("ConsistencyClassId");

                    b.HasOne("Domain.Contracts.Contract", "Contract")
                        .WithMany("TransactionRequest")
                        .HasForeignKey("ContractId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Domain.Stations.PompType", "PompType")
                        .WithMany()
                        .HasForeignKey("PompTypeId");

                    b.HasOne("Domain.Catalog.Product", "Product")
                        .WithMany()
                        .HasForeignKey("ProductId");

                    b.HasOne("Domain.Account.User", "RequestedPerson")
                        .WithMany()
                        .HasForeignKey("RequestedPersonId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Domain.Stations.Station", "Station")
                        .WithMany()
                        .HasForeignKey("StationId");

                    b.HasOne("Domain.Transactions.TransactionStatus", "Status")
                        .WithMany()
                        .HasForeignKey("StatusId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Transactions.TransactionRequestType", "TransactionrequestType")
                        .WithMany()
                        .HasForeignKey("TransactionrequestTypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Account.User", null)
                        .WithMany("TransactionRequest")
                        .HasForeignKey("UserId");

                    b.Navigation("ApprovedUser");

                    b.Navigation("Building");

                    b.Navigation("CanceledUser");

                    b.Navigation("CarType");

                    b.Navigation("ConcreteLocation");

                    b.Navigation("ConsistencyClass");

                    b.Navigation("Contract");

                    b.Navigation("PompType");

                    b.Navigation("Product");

                    b.Navigation("RequestedPerson");

                    b.Navigation("Station");

                    b.Navigation("Status");

                    b.Navigation("TransactionrequestType");
                });

            modelBuilder.Entity("Domain.Transactions.TransactionRequestConcreteOption", b =>
                {
                    b.HasOne("Domain.Transactions.ConcreteOption", "ConcreteOption")
                        .WithMany()
                        .HasForeignKey("ConcreteOptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Domain.Transactions.TransactionRequest", null)
                        .WithMany("TransactionRequestConcreteOption")
                        .HasForeignKey("TransactionRequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ConcreteOption");
                });

            modelBuilder.Entity("Domain.Account.Role", b =>
                {
                    b.Navigation("UserRole");
                });

            modelBuilder.Entity("Domain.Account.User", b =>
                {
                    b.Navigation("BuildingUser");

                    b.Navigation("TransactionRequest");

                    b.Navigation("UserDepartment");

                    b.Navigation("UserRole");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("Domain.Companies.Building", b =>
                {
                    b.Navigation("BuildingUser");

                    b.Navigation("ContractProductBuilding");

                    b.Navigation("TransactionRequest");
                });

            modelBuilder.Entity("Domain.Companies.Company", b =>
                {
                    b.Navigation("CompanyUser");
                });

            modelBuilder.Entity("Domain.Contracts.Contract", b =>
                {
                    b.Navigation("ContractFile");

                    b.Navigation("ContractProduct");

                    b.Navigation("TransactionRequest");
                });

            modelBuilder.Entity("Domain.Contracts.ContractProduct", b =>
                {
                    b.Navigation("ContractProductBuilding");

                    b.Navigation("ContractProductConsistencyClass");
                });

            modelBuilder.Entity("Domain.General.Comment", b =>
                {
                    b.Navigation("CommentFile");
                });

            modelBuilder.Entity("Domain.General.District", b =>
                {
                    b.Navigation("City");
                });

            modelBuilder.Entity("Domain.General.StateProvince", b =>
                {
                    b.Navigation("District");
                });

            modelBuilder.Entity("Domain.Stations.Department", b =>
                {
                    b.Navigation("StationDepartment");

                    b.Navigation("UserDepartment");
                });

            modelBuilder.Entity("Domain.Stations.Station", b =>
                {
                    b.Navigation("StationDepartment");
                });

            modelBuilder.Entity("Domain.Transactions.ConsistencyClass", b =>
                {
                    b.Navigation("ContractProductConsistencyClass");
                });

            modelBuilder.Entity("Domain.Transactions.TransactionRequest", b =>
                {
                    b.Navigation("ContractProductTransaction");

                    b.Navigation("LabResult");

                    b.Navigation("TransactionRequestConcreteOption");
                });
#pragma warning restore 612, 618
        }
    }
}
