﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data033 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsSend",
                schema: "dbo",
                table: "TransactionRequestPompType");

            migrationBuilder.DropColumn(
                name: "RequestedDate",
                schema: "dbo",
                table: "TransactionRequestPompType");

            migrationBuilder.DropColumn(
                name: "SendDate",
                schema: "dbo",
                table: "TransactionRequestPompType");

            migrationBuilder.DropColumn(
                name: "LeftWorth",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "OldWorth",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "Worth",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.AddColumn<string>(
                name: "CanceledNote",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "CanceledUserId",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "PompTypeId",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "int",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "RefundAmount",
                schema: "dbo",
                table: "ContractProductTransaction",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IncludeTax",
                schema: "dbo",
                table: "Contract",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LeftWorth",
                schema: "dbo",
                table: "Contract",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "OldWorth",
                schema: "dbo",
                table: "Contract",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalWorth",
                schema: "dbo",
                table: "Contract",
                type: "decimal(18,2)",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransaction_CanceledUserId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "CanceledUserId");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductTransaction_PompTypeId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "PompTypeId");

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductTransaction_PompType_PompTypeId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "PompTypeId",
                principalSchema: "dbo",
                principalTable: "PompType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductTransaction_User_CanceledUserId",
                schema: "dbo",
                table: "ContractProductTransaction",
                column: "CanceledUserId",
                principalSchema: "Users",
                principalTable: "User",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductTransaction_PompType_PompTypeId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductTransaction_User_CanceledUserId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropIndex(
                name: "IX_ContractProductTransaction_CanceledUserId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropIndex(
                name: "IX_ContractProductTransaction_PompTypeId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "CanceledNote",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "CanceledUserId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "PompTypeId",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "RefundAmount",
                schema: "dbo",
                table: "ContractProductTransaction");

            migrationBuilder.DropColumn(
                name: "IncludeTax",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropColumn(
                name: "LeftWorth",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropColumn(
                name: "OldWorth",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.DropColumn(
                name: "TotalWorth",
                schema: "dbo",
                table: "Contract");

            migrationBuilder.AddColumn<bool>(
                name: "IsSend",
                schema: "dbo",
                table: "TransactionRequestPompType",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "RequestedDate",
                schema: "dbo",
                table: "TransactionRequestPompType",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "SendDate",
                schema: "dbo",
                table: "TransactionRequestPompType",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "LeftWorth",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "OldWorth",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: true);

            migrationBuilder.AddColumn<decimal>(
                name: "Worth",
                schema: "dbo",
                table: "ContractProduct",
                type: "decimal(18,4)",
                nullable: true);
        }
    }
}
