﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Database.Migrations
{
    /// <inheritdoc />
    public partial class Data021 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductBuilding_Building_BuildingId",
                schema: "dbo",
                table: "ContractProductBuilding");

            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductBuilding_ContractProduct_ContractProductContractId_ContractProductProductId",
                schema: "dbo",
                table: "ContractProductBuilding");

            migrationBuilder.DropIndex(
                name: "IX_ContractProductBuilding_ContractProductContractId_ContractProductProductId",
                schema: "dbo",
                table: "ContractProductBuilding");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ContractProduct",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropIndex(
                name: "IX_ContractProduct_ContractId_ProductId",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "ContractProductContractId",
                schema: "dbo",
                table: "ContractProductBuilding");

            migrationBuilder.DropColumn(
                name: "ContractProductProductId",
                schema: "dbo",
                table: "ContractProductBuilding");

            migrationBuilder.AddColumn<Guid>(
                name: "Id",
                schema: "dbo",
                table: "ContractProduct",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.AddPrimaryKey(
                name: "PK_ContractProduct",
                schema: "dbo",
                table: "ContractProduct",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_ContractProduct_ContractId",
                schema: "dbo",
                table: "ContractProduct",
                column: "ContractId");

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductBuilding_Building_BuildingId",
                schema: "dbo",
                table: "ContractProductBuilding",
                column: "BuildingId",
                principalSchema: "dbo",
                principalTable: "Building",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductBuilding_ContractProduct_ContractProductId",
                schema: "dbo",
                table: "ContractProductBuilding",
                column: "ContractProductId",
                principalSchema: "dbo",
                principalTable: "ContractProduct",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductBuilding_Building_BuildingId",
                schema: "dbo",
                table: "ContractProductBuilding");

            migrationBuilder.DropForeignKey(
                name: "FK_ContractProductBuilding_ContractProduct_ContractProductId",
                schema: "dbo",
                table: "ContractProductBuilding");

            migrationBuilder.DropPrimaryKey(
                name: "PK_ContractProduct",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropIndex(
                name: "IX_ContractProduct_ContractId",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.DropColumn(
                name: "Id",
                schema: "dbo",
                table: "ContractProduct");

            migrationBuilder.AddColumn<Guid>(
                name: "ContractProductContractId",
                schema: "dbo",
                table: "ContractProductBuilding",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ContractProductProductId",
                schema: "dbo",
                table: "ContractProductBuilding",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_ContractProduct",
                schema: "dbo",
                table: "ContractProduct",
                columns: new[] { "ContractId", "ProductId" });

            migrationBuilder.CreateIndex(
                name: "IX_ContractProductBuilding_ContractProductContractId_ContractProductProductId",
                schema: "dbo",
                table: "ContractProductBuilding",
                columns: new[] { "ContractProductContractId", "ContractProductProductId" });

            migrationBuilder.CreateIndex(
                name: "IX_ContractProduct_ContractId_ProductId",
                schema: "dbo",
                table: "ContractProduct",
                columns: new[] { "ContractId", "ProductId" },
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductBuilding_Building_BuildingId",
                schema: "dbo",
                table: "ContractProductBuilding",
                column: "BuildingId",
                principalSchema: "dbo",
                principalTable: "Building",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_ContractProductBuilding_ContractProduct_ContractProductContractId_ContractProductProductId",
                schema: "dbo",
                table: "ContractProductBuilding",
                columns: new[] { "ContractProductContractId", "ContractProductProductId" },
                principalSchema: "dbo",
                principalTable: "ContractProduct",
                principalColumns: new[] { "ContractId", "ProductId" });
        }
    }
}
