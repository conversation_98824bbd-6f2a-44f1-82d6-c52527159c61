using Domain.Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Companies;

public class BuildingConfiguration : IEntityTypeConfiguration<Building>
{
    public void Configure(EntityTypeBuilder<Building> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.Property(b => b.Longitude).HasColumnType("decimal(18,4)");
        builder.Property(b => b.Latitude).HasColumnType("decimal(18,4)");
    }
}