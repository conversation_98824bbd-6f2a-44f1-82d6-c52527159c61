using Domain.Companies;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Companies;

public class CompanyUserConfiguration : IEntityTypeConfiguration<BuildingUser>
{
    public void Configure(EntityTypeBuilder<BuildingUser> builder)
    {
        builder.HasKey(x => new { x.UserId, x.BuildingId });
        builder.HasIndex(x => new { x.UserId, x.BuildingId }).IsUnique();
        builder.HasOne(x => x.User)
            .WithMany(x => x.BuildingUser)
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Restrict);
        builder.HasOne(x => x.Building)
            .WithMany(x => x.BuildingUser)
            .HasForeignKey(x => x.BuildingId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}