using Domain.Stations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Stations;

public class StationConfiguration : IEntityTypeConfiguration<Station>
{
    public void Configure(EntityTypeBuilder<Station> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.Property(b => b.Longitude).HasColumnType("decimal(18,4)");
        builder.Property(b => b.Latitude).HasColumnType("decimal(18,4)");
        builder.Property(b => b.InsertDate).HasDefaultValueSql("getdate()");
    }
}