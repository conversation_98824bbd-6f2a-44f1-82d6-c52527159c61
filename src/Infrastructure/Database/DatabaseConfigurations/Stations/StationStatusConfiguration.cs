using Domain.Stations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Stations;

public class StationStatusConfiguration : IEntityTypeConfiguration<StationStatus>
{
    public void Configure(EntityTypeBuilder<StationStatus> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasData(new List<StationStatus>(){
            new(){
                Id = 1,
                Name = "Aktif",
                DisplayName = "Aktif"
            },
            new(){
                Id = 2,
                Name = "Pasif",
                DisplayName = "Pasif"
            }
        });
    }
}