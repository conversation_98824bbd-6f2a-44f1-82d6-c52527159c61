using Domain.Stations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Stations;

public class StationDepartmentConfiguration : IEntityTypeConfiguration<StationDepartment>
{
    public void Configure(EntityTypeBuilder<StationDepartment> builder)
    {
        builder.HasKey(x => new { x.StationId, x.DepartmentId });
        builder.HasIndex(x => new { x.StationId, x.DepartmentId }).IsUnique();
        builder.HasOne(x => x.Station)
            .WithMany(x => x.StationDepartment)
            .HasForeignKey(x => x.StationId)
            .OnDelete(DeleteBehavior.Restrict);
        builder.HasOne(x => x.Department)
            .WithMany(x => x.StationDepartment)
            .HasForeignKey(x => x.DepartmentId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}