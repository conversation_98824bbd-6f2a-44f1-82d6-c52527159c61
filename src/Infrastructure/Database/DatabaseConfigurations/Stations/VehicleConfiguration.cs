using Domain.Stations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Stations;

public class VehicleConfiguration : IEntityTypeConfiguration<Vehicle>
{
    public void Configure(EntityTypeBuilder<Vehicle> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.Property(x => x.Plate).IsRequired();
        builder.Property(b => b.Capacity).HasColumnType("decimal(18,4)");
        builder.Property(b => b.InsertDate).HasDefaultValueSql("getdate()");
    }
}