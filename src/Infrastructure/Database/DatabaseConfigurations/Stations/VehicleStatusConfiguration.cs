using Domain.Stations;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Stations;

public class VehicleStatusConfiguration : IEntityTypeConfiguration<VehicleStatus>
{
    public void Configure(EntityTypeBuilder<VehicleStatus> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasData(new List<VehicleStatus>(){
            new(){
                Id = (int)VehicleStatusEnum.Active,
                Name = "Aktif"
            },
            new(){
                Id = (int)VehicleStatusEnum.Passive,
                Name = "Pasif"
            },
            new(){
                Id = (int)VehicleStatusEnum.Inrepair,
                Name = "Tamirde"
            },
            new(){
                Id = (int)VehicleStatusEnum.OutOfUse,
                Name = "Kullanım Dışı"
            }
        });
    }
}