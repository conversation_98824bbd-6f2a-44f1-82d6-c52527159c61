using Domain.Account;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Users;

public class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasIndex(x => x.Email).IsUnique();
        builder.HasQueryFilter(u => !u.IsDeleted);
        builder.Property(b => b.InsertDate).HasDefaultValueSql("getdate()");
        builder.ToTable("User", "Users");
    }
}