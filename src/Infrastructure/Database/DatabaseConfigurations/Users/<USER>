using Domain.Account;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Users;

public class UserDepartmentConfiguration : IEntityTypeConfiguration<UserDepartment>
{
    public void Configure(EntityTypeBuilder<UserDepartment> builder)
    {
        builder.HasKey(x => new { x.UserId, x.DepartmentId });
        builder.HasIndex(x => new { x.UserId, x.DepartmentId }).IsUnique();
        builder.HasOne(x => x.User)
            .WithMany(x => x.UserDepartment)
            .HasForeignKey(x => x.UserId)
            .OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.Department)
            .WithMany(x => x.UserDepartment)
            .HasForeignKey(x => x.DepartmentId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}