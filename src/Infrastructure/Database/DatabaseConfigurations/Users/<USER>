using Domain.Account;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Users;

public class RoleConfiguration : IEntityTypeConfiguration<Role>
{
    public void Configure(EntityTypeBuilder<Role> builder)
    {
        builder.ToTable("Role", "Users");
        builder.HasData(new List<Role>(){
            new(){
                Id = Role.ADMIN,
                Name = "Admin",
                NormalizedName = "ADMIN",
                ConcurrencyStamp = Role.ADMIN.ToString()
            },
            new(){
                Id = Role.SUPERADMIN,
                Name = "Süper Admin",
                NormalizedName = "SUPERADMIN",
                ConcurrencyStamp = Role.SUPERADMIN.ToString()
            },
            new(){
                Id = Role.BUILDING,
                Name = "Şantiye Görevlisi",
                NormalizedName = "BUILDING",
                ConcurrencyStamp = Role.BUILDING.ToString()
            },
            new(){
                Id = Role.COMPANY,
                Name = "Şirket Yetkilisi",
                NormalizedName = "COMPANY",
                ConcurrencyStamp = Role.COMPANY.ToString()
            },
            new(){
                Id = Role.CONTROL,
                Name = "Yapı Denetim Personeli",
                NormalizedName = "CONTROL",
                ConcurrencyStamp = Role.CONTROL.ToString()
            },
            new(){
                Id = Role.DRIVER,
                Name = "Şoför",
                NormalizedName = "DRIVER",
                ConcurrencyStamp = Role.DRIVER.ToString()
            },
            new(){
                Id = Role.SALES,
                Name = "Satış Temsilcisi",
                NormalizedName = "SALES",
                ConcurrencyStamp = Role.SALES.ToString()
            },
            new(){
                Id = Role.STATION,
                Name = "Santral Çalışanı",
                NormalizedName = "STATION",
                ConcurrencyStamp = Role.STATION.ToString()
            },
            new(){
                Id = Role.MOULDER,
                Name = "Kalıpçı",
                NormalizedName = "MOULDER",
                ConcurrencyStamp = Role.MOULDER.ToString()
            }
        });
    }
}