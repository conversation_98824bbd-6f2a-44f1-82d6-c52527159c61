using Domain.Account;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Users;

public class PageRuleConfiguration : IEntityTypeConfiguration<PageRule>
{
    public void Configure(EntityTypeBuilder<PageRule> builder)
    {
        builder.HasKey(x => new { x.PageId, x.UserId, x.RoleId });
        builder.HasIndex(x => new { x.PageId, x.UserId, x.RoleId }).IsUnique();
        builder.ToTable("PageRule", "Users");
    }
}