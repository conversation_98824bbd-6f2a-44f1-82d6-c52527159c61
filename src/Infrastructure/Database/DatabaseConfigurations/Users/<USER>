using Domain.Account;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Users;

public class PageConfiguration : IEntityTypeConfiguration<Page>
{
    public void Configure(EntityTypeBuilder<Page> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasIndex(x => new { x.Url, x.Method }).IsUnique();
        builder.ToTable("Page", "Users");
    }
}