using Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Contracts;

public class ContractProductConcreteOptionConfiguration : IEntityTypeConfiguration<ContractProductConcreteOption>
{
    public void Configure(EntityTypeBuilder<ContractProductConcreteOption> builder)
    {
        builder.HasKey(x => new { x.ContractProductId, x.ConcreteOptionId });
        builder.HasIndex(x => new { x.ContractProductId, x.ConcreteOptionId }).IsUnique();
        builder.HasOne(x => x.ContractProduct)
            .WithMany(x => x.ContractProductConcreteOption)
            .HasForeignKey(x => x.ContractProductId)
            .OnDelete(DeleteBehavior.Restrict);
        builder.HasOne(x => x.ConcreteOption)
            .WithMany(x => x.ContractProductConcreteOption)
            .HasForeignKey(x => x.ConcreteOptionId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}