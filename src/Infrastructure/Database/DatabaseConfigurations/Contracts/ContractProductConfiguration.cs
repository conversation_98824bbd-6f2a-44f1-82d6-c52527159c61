using Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Contracts;

public class ContractProductConfiguration : IEntityTypeConfiguration<ContractProduct>
{
    public void Configure(EntityTypeBuilder<ContractProduct> builder)
    {
        builder.Property(b => b.Amount).HasColumnType("decimal(18,4)");
        builder.Property(b => b.OldAmount).HasColumnType("decimal(18,4)");
        builder.Property(b => b.LeftAmount).HasColumnType("decimal(18,4)");
        builder.Property(b => b.Price).HasColumnType("decimal(18,4)");
    }
}