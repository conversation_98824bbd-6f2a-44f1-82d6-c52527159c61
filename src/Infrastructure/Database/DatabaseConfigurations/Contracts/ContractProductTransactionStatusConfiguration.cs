using Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Contracts;

public class ContractProductTransactionStatusConfiguration : IEntityTypeConfiguration<ContractProductTransactionStatus>
{
    public void Configure(EntityTypeBuilder<ContractProductTransactionStatus> builder)
    {
        builder.HasData(new List<ContractProductTransactionStatus>(){
            new(){
                Id = (int)ContractProductTransactionStatusEnum.Pending,
                Name = "Beklemede"
            },
            new(){
                Id = (int)ContractProductTransactionStatusEnum.OnRoad,
                Name = "Yolda"
            },
            new(){
                Id = (int)ContractProductTransactionStatusEnum.InProgress,
                Name = "İşlemde"
            },
            new(){
                Id = (int)ContractProductTransactionStatusEnum.Finished,
                Name = "Tamamlandı"
            },
            new(){
                Id = (int)ContractProductTransactionStatusEnum.Cancel,
                Name = "İptal"
            }
        });
    }
}