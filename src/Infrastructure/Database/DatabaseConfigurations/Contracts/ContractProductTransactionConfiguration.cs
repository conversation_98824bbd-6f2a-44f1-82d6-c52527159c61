using Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Contracts;

public class ContractProductTransactionConfiguration : IEntityTypeConfiguration<ContractProductTransaction>
{
    public void Configure(EntityTypeBuilder<ContractProductTransaction> builder)
    {
        builder.Property(b => b.SendingAmount).HasColumnType("decimal(18,4)");
        builder.Property(b => b.TypeId).HasDefaultValue(1);
        builder.Property(b => b.InsertDate).HasDefaultValueSql("getdate()");
        builder.HasOne(x => x.TransactionRequest)
            .WithMany(x => x.ContractProductTransaction)
            .HasForeignKey(x => x.TransactionRequestId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(x => x.Driver)
            .WithMany()
            .HasForeignKey(x => x.DriverId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(tr => tr.CanceledUser)
            .WithMany()
            .HasForeignKey(tr => tr.CanceledUserId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(tr => tr.PompType)
            .WithMany()
            .HasForeignKey(tr => tr.PompTypeId)
            .OnDelete(DeleteBehavior.Restrict);    
    }
}