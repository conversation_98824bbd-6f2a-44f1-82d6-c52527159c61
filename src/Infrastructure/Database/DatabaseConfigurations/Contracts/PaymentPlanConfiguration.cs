using Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Contracts;

public class PaymentPlanConfigurationConfiguration : IEntityTypeConfiguration<PaymentPlan>
{
    public void Configure(EntityTypeBuilder<PaymentPlan> builder)
    {
        builder.HasData(new List<PaymentPlan>(){
            new(){
                Id = (int)PaymentPlanEnum.Cash,
                Name = "Peşin"
            },
            new(){
                Id = (int)PaymentPlanEnum.CreditCard,
                Name = "Kredi Kartı"
            },
            new(){
                Id = (int)PaymentPlanEnum.CreditCardTerm,
                Name = "Kredi Kartı Vadeli"
            },
            new(){
                Id = (int)PaymentPlanEnum.Check,
                Name = "Çek 2 Ay Vadeli"
            },
            new(){
                Id = (int)PaymentPlanEnum.Month30Days,
                Name = "Ay + 30 Gün"
            }
        });
    }
}