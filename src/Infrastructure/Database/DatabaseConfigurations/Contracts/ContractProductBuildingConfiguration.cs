using Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Contracts;

public class ContractProductBuildingConfiguration : IEntityTypeConfiguration<ContractProductBuilding>
{
    public void Configure(EntityTypeBuilder<ContractProductBuilding> builder)
    {
        builder.HasKey(x => new { x.ContractProductId, x.BuildingId });
        builder.HasIndex(x => new { x.ContractProductId, x.BuildingId }).IsUnique();
        builder.HasOne(x => x.ContractProduct)
            .WithMany(x => x.ContractProductBuilding)
            .HasForeignKey(x => x.ContractProductId)
            .OnDelete(DeleteBehavior.Cascade);
        builder.HasOne(x => x.Building)
            .WithMany(x => x.ContractProductBuilding)
            .HasForeignKey(x => x.BuildingId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}