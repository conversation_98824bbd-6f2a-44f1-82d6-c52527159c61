using Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Stations;

public class TransactionTypeConfiguration : IEntityTypeConfiguration<TransactionType>
{
    public void Configure(EntityTypeBuilder<TransactionType> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasData(new List<TransactionType>(){
            new(){
                Id = (int)TransactionTypeEnum.Normal,
                Name = "Normal"
            },
            new(){
                Id = (int)TransactionTypeEnum.Return,
                Name = "İade"
            }
        });
    }
}