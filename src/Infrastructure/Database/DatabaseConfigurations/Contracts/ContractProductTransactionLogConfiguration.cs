using Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Contracts;

public class ContractProductTransactionLogConfiguration : IEntityTypeConfiguration<ContractProductTransactionLog>
{
    public void Configure(EntityTypeBuilder<ContractProductTransactionLog> builder)
    {
        builder.Property(b => b.InsertDate).HasDefaultValueSql("getdate()");
    }
}