using Domain.Contracts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Contracts;

public class ContractProductConsistencyClassConfiguration : IEntityTypeConfiguration<ContractProductConsistencyClass>
{
    public void Configure(EntityTypeBuilder<ContractProductConsistencyClass> builder)
    {
        builder.HasKey(x => new { x.ContractProductId, x.ConsistencyClassId });
        builder.HasIndex(x => new { x.ContractProductId, x.ConsistencyClassId }).IsUnique();
        builder.HasOne(x => x.ContractProduct)
            .WithMany(x => x.ContractProductConsistencyClass)
            .HasForeignKey(x => x.ContractProductId)
            .OnDelete(DeleteBehavior.Restrict);
        builder.HasOne(x => x.ConsistencyClass)
            .WithMany(x => x.ContractProductConsistencyClass)
            .HasForeignKey(x => x.ConsistencyClassId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}