using Domain.Transactions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Transactions;

public class TransactionRequestTypeConfiguration : IEntityTypeConfiguration<TransactionRequestType>
{
    public void Configure(EntityTypeBuilder<TransactionRequestType> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasData(new List<TransactionRequestType>(){
            new(){
                Id = (int)TransactionRequestTypeEnum.Normal,
                Name = "Normal",
            },
            new(){
                Id = (int)TransactionRequestTypeEnum.Discovery,
                Name = "<PERSON><PERSON><PERSON>",
            }
        });
    }
}