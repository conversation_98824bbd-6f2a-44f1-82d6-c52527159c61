using Domain.Transactions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Transactions;

public class TransactionRequestPompTypeConfiguration : IEntityTypeConfiguration<TransactionRequestPompType>
{
    public void Configure(EntityTypeBuilder<TransactionRequestPompType> builder)
    {
        builder.HasKey(x => new { x.TransactionRequestId, x.PompTypeId });
    }
}