using Domain.Transactions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Transactions;

public class ConcreteOptionConfiguration : IEntityTypeConfiguration<ConcreteOption>
{
    public void Configure(EntityTypeBuilder<ConcreteOption> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasData(new List<ConcreteOption>(){
            new(){
                Id = 1,
                Name = "Normal"
            },
            new(){
                Id = 2,
                Name = "<PERSON>r<PERSON><PERSON>"
            },
            new(){
                Id = 3,
                Name = "Su Geçirimsiz"
            },
            new(){
                Id = 4,
                Name = "Katkısız"
            },
            new(){
                Id = 5,
                Name = "Antifrizli"
            },
            new(){
                Id = 6,
                Name = "Sabit"
            },
            new(){
                Id = 7,
                Name = "Dsi"
            },
            new(){
                Id = 8,
                Name = "Bahçe Duvarı"
            },
        });
    }
}