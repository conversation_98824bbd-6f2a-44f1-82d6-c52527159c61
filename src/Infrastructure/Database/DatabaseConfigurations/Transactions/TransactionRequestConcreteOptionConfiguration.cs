using Domain.Transactions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Transactions;

public class TransactionRequestConcreteOptionConfiguration : IEntityTypeConfiguration<TransactionRequestConcreteOption>
{
    public void Configure(EntityTypeBuilder<TransactionRequestConcreteOption> builder)
    {
        builder.HasKey(x => new { x.TransactionRequestId, x.ConcreteOptionId });
        builder.HasIndex(x => new { x.TransactionRequestId, x.ConcreteOptionId }).IsUnique();
    }
}