using Domain.Transactions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Transactions;

public class ConsistencyClassConfiguration : IEntityTypeConfiguration<ConsistencyClass>
{
    public void Configure(EntityTypeBuilder<ConsistencyClass> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasData(new List<ConsistencyClass>(){
            new(){
                Id = 1,
                Name = "S1",
                Description = "1 cm - 4 cm ÇÖKME ARALIĞI"
            },
            new(){
                Id = 2,
                Name = "S2",
                Description = "5 cm - 9 cm  ÇÖKME ARALIĞI"
            },
            new(){
                Id = 3,
                Name = "S3",
                Description = "10 cm - 15 cm  ÇÖKME ARALIĞI"
            },
            new(){
                Id = 4,
                Name = "S4",
                Description = "16 cm - 21 cm  ÇÖKME ARALIĞI"
            },
            new(){
                Id = 5,
                Name = "S5",
                Description = "22 cm ve üzeri ÇÖKME ARALIĞI"
            },
        });
    }
}