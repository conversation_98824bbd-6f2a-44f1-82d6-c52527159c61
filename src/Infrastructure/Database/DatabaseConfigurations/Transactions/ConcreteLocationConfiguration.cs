using Domain.Transactions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Transactions;

public class ConcreteLocationConfiguration : IEntityTypeConfiguration<ConcreteLocation>
{
    public void Configure(EntityTypeBuilder<ConcreteLocation> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasData(new List<ConcreteLocation>(){
            new(){
                Id = 1,
                Name = "Temel",
                SubItem = "S3,S4"
            },
            new(){
                Id = 2,
                Name = "Kolon",
                SubItem = "S3"
            },
            new(){
                Id = 3,
                Name = "<PERSON><PERSON><PERSON>",
                SubItem = "S3"
            },
            new(){
                Id = 4,
                Name = "Tabliye",
                SubItem = "S3,S4"
            },
            new(){
                Id = 5,
                Name = "İstinat Duvarı",
                SubItem = "S3"
            },
            new(){
                Id = 6,
                Name = "Merdi<PERSON>",
                SubItem = "S2,S3"
            },
            new(){
                Id = 7,
                Name = "<PERSON>ıl",
                SubItem = "S3,S4"
            },
            new(){
                Id = 8,
                Name = "<PERSON><PERSON><PERSON>",
                SubItem = "S1,S2,S3,S4,S5"
            },
        });
    }
}