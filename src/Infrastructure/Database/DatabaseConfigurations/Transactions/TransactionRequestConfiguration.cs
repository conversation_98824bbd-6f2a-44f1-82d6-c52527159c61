using Domain.Transactions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Transactions;

public class TransactionRequestConfiguration : IEntityTypeConfiguration<TransactionRequest>
{
    public void Configure(EntityTypeBuilder<TransactionRequest> builder)
    {

        builder.HasQueryFilter(b => !b.IsDeleted);
        builder.Property(b => b.InsertDate).HasDefaultValueSql("getdate()");
        builder.Property(b => b.DesiredTotalConcrete).HasColumnType("decimal(18,4)");
        builder.Property(b => b.DistanceInDestination).HasColumnType("decimal(18,4)");
        builder.Property(b => b.TotalConcreteRefundable).HasColumnType("decimal(18,4)");
        builder.Property(b => b.TotalConcreteRemaining).HasColumnType("decimal(18,4)");
        builder.Property(b => b.TotalConcreteSent).HasColumnType("decimal(18,4)");
        builder.Property(b => b.TransactionrequestTypeId).HasDefaultValue((int)TransactionRequestTypeEnum.Normal);
        builder.HasOne(x => x.Contract)
            .WithMany(x => x.TransactionRequest)
            .HasForeignKey(x => x.ContractId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(x => x.RequestedPerson)
            .WithMany(x => x.TransactionRequest)
            .HasForeignKey(x => x.RequestedPersonId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(tr => tr.RequestedPerson)
            .WithMany()
            .HasForeignKey(tr => tr.RequestedPersonId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(tr => tr.ApprovedUser)
            .WithMany()
            .HasForeignKey(tr => tr.ApprovedUserId)
            .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(tr => tr.CanceledUser)
            .WithMany()
            .HasForeignKey(tr => tr.CanceledUserId)
            .OnDelete(DeleteBehavior.Restrict);
    }
}