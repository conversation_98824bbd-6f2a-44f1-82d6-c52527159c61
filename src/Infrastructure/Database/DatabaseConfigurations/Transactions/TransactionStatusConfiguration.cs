using Domain.Transactions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Database.DatabaseConfigurations.Transactions;

public class TransactionStatusConfiguration : IEntityTypeConfiguration<TransactionStatus>
{
    public void Configure(EntityTypeBuilder<TransactionStatus> builder)
    {
        builder.Property(x => x.Name).IsRequired();
        builder.HasData(new List<TransactionStatus>(){
            new(){
                Id = (int)TransactionStatusEnum.Pending,
                Name = "Onay Bekliyor",
                CustomerName = "Onay Bekliyor"
            },
            new(){
                Id = (int)TransactionStatusEnum.Approved,
                Name = "Planlanacak",
                CustomerName = "Planlanacak"
            },
            new(){
                Id = (int)TransactionStatusEnum.Planning,
                Name = "Planlamada",
                CustomerName = "Planlamada"
            },
            new(){
                Id = (int)TransactionStatusEnum.Planned,
                Name = "Planlandı",
                CustomerName = "Planlandı"
            },
            new(){
                Id = (int)TransactionStatusEnum.InProgress,
                Name = "Döküm Devam Ediyor",
                CustomerName = "Döküm Devam Ediyor"
            },
            new(){
                Id = (int)TransactionStatusEnum.Finished,
                Name = "Tamamlandı",
                CustomerName = "Tamamlandı"
            },
            new()
            {
                Id = (int)TransactionStatusEnum.CancelRequest,
                Name = "İptal Talebi",
                CustomerName = "İptal Talebi"
            },
            new()
            {
                Id = (int)TransactionStatusEnum.Reject,
                Name = "Reddedildi",
                CustomerName = "Reddedildi"
            }
        });
    }
}