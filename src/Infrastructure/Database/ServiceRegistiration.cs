using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Database.Context;
using Application.Shared.Data;

namespace Database;

public static class ServiceRegistration
{
    public static void AddDatabaseInsfrastructure(this IServiceCollection serviceCollection, IConfiguration configuration)
    {
        var connectionString = configuration.GetConnectionString("ApplicationConnection");
        serviceCollection.AddDbContext<ApplicationDbContext>(opt =>
        {
            opt.UseSqlServer(connectionString, b =>
            {
                b.MigrationsHistoryTable(HistoryRepository.DefaultTableName, "dbo");
                b.MigrationsAssembly("Database");
            });
        });
        serviceCollection.AddScoped<IApplicationDbContext>(sp => sp.GetRequiredService<ApplicationDbContext>());
    }
}