using System.Reflection;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Domain.Account;
using Domain.Catalog;
using Domain.Companies;
using Domain.Contracts;
using Domain.General;
using Domain.Stations;
using Domain.Transactions;
using Application.Shared.Data;
using Domain.Shared;
using Application.Shared;

namespace Database.Context;

public class ApplicationDbContext(
    DbContextOptions<ApplicationDbContext> options,
    IEventBus eventBus)
: IdentityDbContext<User, Role, Guid, UserClaim, UserRole, UserLogin, RoleClaim, UserToken>(options), IApplicationDbContext
{
    private readonly IEventBus _eventBus = eventBus;

    public virtual DbSet<UserDepartment> UserDepartment { get; set; }
    public virtual DbSet<Page> Page { get; set; }
    public virtual DbSet<PageRule> PageRule { get; set; }

    public virtual DbSet<Product> Product { get; set; }

    public virtual DbSet<Building> Building { get; set; }
    public virtual DbSet<BuildingUser> BuildingUser { get; set; }
    public virtual DbSet<Company> Company { get; set; }

    public virtual DbSet<Contract> Contract { get; set; }
    public virtual DbSet<PaymentPlan> PaymentPlan { get; set; }
    public virtual DbSet<ContractProduct> ContractProduct { get; set; }
    public virtual DbSet<ContractProductBuilding> ContractProductBuilding { get; set; }
    public virtual DbSet<ContractProductConsistencyClass> ContractProductConsistencyClass { get; set; }
    public virtual DbSet<ContractProductConcreteOption> ContractProductConcreteOption { get; set; }
    public virtual DbSet<ContractFile> ContractFile { get; set; }
    public virtual DbSet<ContractProductTransaction> ContractProductTransaction { get; set; }
    public virtual DbSet<ContractProductTransactionLog> ContractProductTransactionLog { get; set; }
    public virtual DbSet<ContractProductTransactionStatus> ContractProductTransactionStatus { get; set; }
    public virtual DbSet<TransactionType> TransactionType { get; set; }

    public virtual DbSet<City> City { get; set; }
    public virtual DbSet<District> District { get; set; }
    public virtual DbSet<NotAvailableDate> NotAvailableDate { get; set; }
    public virtual DbSet<Notification> Notification { get; set; }
    public virtual DbSet<StateProvince> StateProvince { get; set; }
    public virtual DbSet<Gallery> Gallery { get; set; }
    public virtual DbSet<Comment> Comment { get; set; }
    public virtual DbSet<CommentFile> CommentFile { get; set; }

    public virtual DbSet<Department> Department { get; set; }
    public virtual DbSet<PompType> PompType { get; set; }
    public virtual DbSet<Station> Station { get; set; }
    public virtual DbSet<StationDepartment> StationDepartment { get; set; }
    public virtual DbSet<StationStatus> StationStatus { get; set; }
    public virtual DbSet<Vehicle> Vehicle { get; set; }
    public virtual DbSet<VehicleStatus> VehicleStatus { get; set; }
    public virtual DbSet<VehicleType> VehicleType { get; set; }

    public virtual DbSet<TransactionRequest> TransactionRequest { get; set; }
    public DbSet<TransactionRequestConcreteOption> TransactionRequestConcreteOption { get; set; }
    public virtual DbSet<TransactionRequestPompType> TransactionRequestPompType { get; set; }
    public virtual DbSet<TransactionRequestType> TransactionRequestType { get; set; }
    public virtual DbSet<LabResult> LabResult { get; set; }
    public virtual DbSet<TransactionStatus> TransactionStatus { get; set; }
    public virtual DbSet<ConcreteLocation> ConcreteLocation { get; set; }
    public virtual DbSet<ConcreteOption> ConcreteOption { get; set; }
    public virtual DbSet<ConsistencyClass> ConsistencyClass { get; set; }

    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);
        builder.HasDefaultSchema("dbo");
        builder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configurationBuilder)
    {
        base.ConfigureConventions(configurationBuilder);
        configurationBuilder.Properties<string>()
            .HaveMaxLength(1024);
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        int result = await base.SaveChangesAsync(cancellationToken);

        await DispatchDomainEvents(cancellationToken);

        return result;
    }

    public async Task DispatchDomainEvents(CancellationToken cancellationToken = default)
    {
        var entities = ChangeTracker
            .Entries<BaseEntity>()
            .Where(e => e.Entity.DomainEvents.Any())
            .Select(e => e.Entity);

        var domainEvents = entities
            .SelectMany(e => e.DomainEvents)
            .ToList();

        entities.ToList().ForEach(e => e.ClearDomainEvents());

        foreach (var domainEvent in domainEvents)
        {
            await _eventBus.PublishAsync(domainEvent, cancellationToken);
        }
    }
}